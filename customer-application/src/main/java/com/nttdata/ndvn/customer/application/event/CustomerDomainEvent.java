package com.nttdata.ndvn.customer.application.event;

import com.nttdata.ndvn.customer.domain.model.Customer;
import com.nttdata.ndvn.customer.domain.model.CustomerStatus;
import org.springframework.context.ApplicationEvent;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Base class for customer domain events.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-28
 */
public abstract class CustomerDomainEvent extends ApplicationEvent {
    
    private final Customer customer;
    private final LocalDateTime occurredAt;
    
    protected CustomerDomainEvent(Object source, Customer customer) {
        super(source);
        this.customer = customer;
        this.occurredAt = LocalDateTime.now();
    }
    
    public Customer getCustomer() {
        return customer;
    }
    
    public LocalDateTime getOccurredAt() {
        return occurredAt;
    }
    
    /**
     * Customer created event
     */
    public static class CustomerCreated extends CustomerDomainEvent {
        public CustomerCreated(Object source, Customer customer) {
            super(source, customer);
        }
    }
    
    /**
     * Customer updated event
     */
    public static class CustomerUpdated extends CustomerDomainEvent {
        public CustomerUpdated(Object source, Customer customer) {
            super(source, customer);
        }
    }
    
    /**
     * Customer status changed event
     */
    public static class CustomerStatusChanged extends CustomerDomainEvent {
        private final CustomerStatus previousStatus;
        
        public CustomerStatusChanged(Object source, Customer customer, CustomerStatus previousStatus) {
            super(source, customer);
            this.previousStatus = previousStatus;
        }
        
        public CustomerStatus getPreviousStatus() {
            return previousStatus;
        }
    }
    
    /**
     * Customer credit limit updated event
     */
    public static class CustomerCreditLimitUpdated extends CustomerDomainEvent {
        private final BigDecimal previousCreditLimit;
        
        public CustomerCreditLimitUpdated(Object source, Customer customer, BigDecimal previousCreditLimit) {
            super(source, customer);
            this.previousCreditLimit = previousCreditLimit;
        }
        
        public BigDecimal getPreviousCreditLimit() {
            return previousCreditLimit;
        }
    }
    
    /**
     * Customer segment assigned event
     */
    public static class CustomerSegmentAssigned extends CustomerDomainEvent {
        private final String segmentName;
        private final String assignedBy;
        
        public CustomerSegmentAssigned(Object source, Customer customer, String segmentName, String assignedBy) {
            super(source, customer);
            this.segmentName = segmentName;
            this.assignedBy = assignedBy;
        }
        
        public String getSegmentName() {
            return segmentName;
        }
        
        public String getAssignedBy() {
            return assignedBy;
        }
    }
}

package com.nttdata.ndvn.customer.events.listener;

import com.nttdata.ndvn.customer.application.event.CustomerDomainEvent;
import com.nttdata.ndvn.customer.events.publisher.StandardizedCustomerEventPublisher;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * Event listener that converts domain events to Kafka events.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-28
 */
@Component
public class CustomerDomainEventListener {
    
    private static final Logger logger = LoggerFactory.getLogger(CustomerDomainEventListener.class);
    
    private final StandardizedCustomerEventPublisher eventPublisher;
    
    public CustomerDomainEventListener(StandardizedCustomerEventPublisher eventPublisher) {
        this.eventPublisher = eventPublisher;
    }
    
    @EventListener
    @Async
    public void handleCustomerCreated(CustomerDomainEvent.CustomerCreated event) {
        try {
            eventPublisher.publishCustomerCreated(event.getCustomer())
                .whenComplete((result, ex) -> {
                    if (ex != null) {
                        logger.error("Failed to publish CustomerCreatedEvent to Kafka for customer: {}", 
                                event.getCustomer().getId(), ex);
                    } else {
                        logger.debug("Successfully published CustomerCreatedEvent to Kafka for customer: {}", 
                                event.getCustomer().getId());
                    }
                });
        } catch (Exception ex) {
            logger.error("Error handling CustomerCreated domain event for customer: {}", 
                    event.getCustomer().getId(), ex);
        }
    }
    
    @EventListener
    @Async
    public void handleCustomerUpdated(CustomerDomainEvent.CustomerUpdated event) {
        try {
            eventPublisher.publishCustomerUpdated(event.getCustomer())
                .whenComplete((result, ex) -> {
                    if (ex != null) {
                        logger.error("Failed to publish CustomerUpdatedEvent to Kafka for customer: {}", 
                                event.getCustomer().getId(), ex);
                    } else {
                        logger.debug("Successfully published CustomerUpdatedEvent to Kafka for customer: {}", 
                                event.getCustomer().getId());
                    }
                });
        } catch (Exception ex) {
            logger.error("Error handling CustomerUpdated domain event for customer: {}", 
                    event.getCustomer().getId(), ex);
        }
    }
    
    @EventListener
    @Async
    public void handleCustomerStatusChanged(CustomerDomainEvent.CustomerStatusChanged event) {
        try {
            eventPublisher.publishCustomerStatusChanged(event.getCustomer(), event.getPreviousStatus())
                .whenComplete((result, ex) -> {
                    if (ex != null) {
                        logger.error("Failed to publish CustomerStatusChangedEvent to Kafka for customer: {}", 
                                event.getCustomer().getId(), ex);
                    } else {
                        logger.debug("Successfully published CustomerStatusChangedEvent to Kafka for customer: {}", 
                                event.getCustomer().getId());
                    }
                });
        } catch (Exception ex) {
            logger.error("Error handling CustomerStatusChanged domain event for customer: {}", 
                    event.getCustomer().getId(), ex);
        }
    }
    
    @EventListener
    @Async
    public void handleCustomerCreditLimitUpdated(CustomerDomainEvent.CustomerCreditLimitUpdated event) {
        try {
            eventPublisher.publishCustomerCreditLimitUpdated(event.getCustomer(), event.getPreviousCreditLimit())
                .whenComplete((result, ex) -> {
                    if (ex != null) {
                        logger.error("Failed to publish CustomerCreditLimitUpdatedEvent to Kafka for customer: {}", 
                                event.getCustomer().getId(), ex);
                    } else {
                        logger.debug("Successfully published CustomerCreditLimitUpdatedEvent to Kafka for customer: {}", 
                                event.getCustomer().getId());
                    }
                });
        } catch (Exception ex) {
            logger.error("Error handling CustomerCreditLimitUpdated domain event for customer: {}", 
                    event.getCustomer().getId(), ex);
        }
    }
    
    @EventListener
    @Async
    public void handleCustomerSegmentAssigned(CustomerDomainEvent.CustomerSegmentAssigned event) {
        try {
            eventPublisher.publishCustomerSegmentAssigned(event.getCustomer(), event.getSegmentName(), event.getAssignedBy())
                .whenComplete((result, ex) -> {
                    if (ex != null) {
                        logger.error("Failed to publish CustomerSegmentAssignedEvent to Kafka for customer: {}", 
                                event.getCustomer().getId(), ex);
                    } else {
                        logger.debug("Successfully published CustomerSegmentAssignedEvent to Kafka for customer: {}", 
                                event.getCustomer().getId());
                    }
                });
        } catch (Exception ex) {
            logger.error("Error handling CustomerSegmentAssigned domain event for customer: {}", 
                    event.getCustomer().getId(), ex);
        }
    }
}

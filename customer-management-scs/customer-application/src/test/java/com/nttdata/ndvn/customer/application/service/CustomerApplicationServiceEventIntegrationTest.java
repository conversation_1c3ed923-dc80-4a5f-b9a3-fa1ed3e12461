package com.nttdata.ndvn.customer.application.service;

import com.nttdata.ndvn.customer.application.dto.CustomerDto;
import com.nttdata.ndvn.customer.application.mapper.CustomerMapper;
import com.nttdata.ndvn.customer.domain.model.Customer;
import com.nttdata.ndvn.customer.domain.model.CustomerStatus;
import com.nttdata.ndvn.customer.domain.model.CustomerType;
import com.nttdata.ndvn.customer.domain.repository.CustomerRepository;
import com.nttdata.ndvn.customer.domain.service.CustomerDomainService;
import com.nttdata.ndvn.customer.events.publisher.StandardizedCustomerEventPublisher;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.kafka.support.SendResult;

import java.math.BigDecimal;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * Integration test for CustomerApplicationService event publishing.
 */
@ExtendWith(MockitoExtension.class)
class CustomerApplicationServiceEventIntegrationTest {
    
    @Mock
    private CustomerRepository customerRepository;
    
    @Mock
    private CustomerDomainService customerDomainService;
    
    @Mock
    private CustomerMapper customerMapper;
    
    @Mock
    private StandardizedCustomerEventPublisher eventPublisher;
    
    @Mock
    private SendResult<String, String> sendResult;
    
    private CustomerApplicationService customerApplicationService;
    
    @BeforeEach
    void setUp() {
        customerApplicationService = new CustomerApplicationService(
            customerRepository, customerDomainService, customerMapper, eventPublisher
        );
    }
    
    @Test
    void shouldPublishCustomerCreatedEventWhenCreatingCustomer() {
        // Given
        CustomerDto customerDto = new CustomerDto();
        customerDto.setEmail("<EMAIL>");
        customerDto.setDisplayName("Test Customer");
        customerDto.setCustomerType(CustomerType.INDIVIDUAL);
        
        Customer customer = new Customer("CUST001", CustomerType.INDIVIDUAL, "<EMAIL>", "Test Customer");
        CustomerDto resultDto = new CustomerDto();
        resultDto.setId(customer.getId());
        
        when(customerDomainService.generateCustomerNumber()).thenReturn("CUST001");
        when(customerMapper.toEntity(customerDto)).thenReturn(customer);
        when(customerRepository.save(customer)).thenReturn(customer);
        when(customerMapper.toDto(customer)).thenReturn(resultDto);
        when(eventPublisher.publishCustomerCreated(customer))
            .thenReturn(CompletableFuture.completedFuture(sendResult));
        
        // When
        CustomerDto result = customerApplicationService.createCustomer(customerDto);
        
        // Then
        assertThat(result).isNotNull();
        
        verify(customerDomainService).validateCustomerForCreation(customer);
        verify(customerRepository).save(customer);
        verify(eventPublisher).publishCustomerCreated(customer);
    }
    
    @Test
    void shouldPublishCustomerUpdatedEventWhenUpdatingCustomer() {
        // Given
        UUID customerId = UUID.randomUUID();
        CustomerDto updateDto = new CustomerDto();
        updateDto.setDisplayName("Updated Customer");
        
        Customer customer = new Customer("CUST001", CustomerType.INDIVIDUAL, "<EMAIL>", "Test Customer");
        CustomerDto resultDto = new CustomerDto();
        resultDto.setId(customerId);
        
        when(customerRepository.findById(customerId)).thenReturn(Optional.of(customer));
        when(customerRepository.save(customer)).thenReturn(customer);
        when(customerMapper.toDto(customer)).thenReturn(resultDto);
        when(eventPublisher.publishCustomerUpdated(customer))
            .thenReturn(CompletableFuture.completedFuture(sendResult));
        
        // When
        CustomerDto result = customerApplicationService.updateCustomer(customerId, updateDto);
        
        // Then
        assertThat(result).isNotNull();
        
        verify(customerRepository).findById(customerId);
        verify(customerMapper).updateEntityFromDto(updateDto, customer);
        verify(customerRepository).save(customer);
        verify(eventPublisher).publishCustomerUpdated(customer);
    }
    
    @Test
    void shouldPublishCustomerStatusChangedEventWhenUpdatingStatus() {
        // Given
        UUID customerId = UUID.randomUUID();
        CustomerStatus newStatus = CustomerStatus.INACTIVE;
        
        Customer customer = new Customer("CUST001", CustomerType.INDIVIDUAL, "<EMAIL>", "Test Customer");
        customer.changeStatus(CustomerStatus.ACTIVE); // Set initial status
        CustomerDto resultDto = new CustomerDto();
        resultDto.setId(customerId);
        
        when(customerRepository.findById(customerId)).thenReturn(Optional.of(customer));
        when(customerRepository.save(customer)).thenReturn(customer);
        when(customerMapper.toDto(customer)).thenReturn(resultDto);
        when(eventPublisher.publishCustomerStatusChanged(eq(customer), eq(CustomerStatus.ACTIVE)))
            .thenReturn(CompletableFuture.completedFuture(sendResult));
        
        // When
        CustomerDto result = customerApplicationService.updateCustomerStatus(customerId, newStatus);
        
        // Then
        assertThat(result).isNotNull();
        
        verify(customerRepository).findById(customerId);
        verify(customerRepository).save(customer);
        verify(eventPublisher).publishCustomerStatusChanged(customer, CustomerStatus.ACTIVE);
    }
    
    @Test
    void shouldPublishCustomerCreditLimitUpdatedEventWhenUpdatingCreditLimit() {
        // Given
        UUID customerId = UUID.randomUUID();
        BigDecimal newCreditLimit = new BigDecimal("10000.00");
        BigDecimal previousCreditLimit = new BigDecimal("5000.00");
        
        Customer customer = new Customer("CUST001", CustomerType.INDIVIDUAL, "<EMAIL>", "Test Customer");
        customer.updateCreditLimit(previousCreditLimit); // Set initial credit limit
        CustomerDto resultDto = new CustomerDto();
        resultDto.setId(customerId);
        
        when(customerRepository.findById(customerId)).thenReturn(Optional.of(customer));
        when(customerDomainService.determineCustomerClassification(customer)).thenReturn("PREMIUM");
        when(customerRepository.save(customer)).thenReturn(customer);
        when(customerMapper.toDto(customer)).thenReturn(resultDto);
        when(eventPublisher.publishCustomerCreditLimitUpdated(eq(customer), eq(previousCreditLimit)))
            .thenReturn(CompletableFuture.completedFuture(sendResult));
        
        // When
        CustomerDto result = customerApplicationService.updateCreditLimit(customerId, newCreditLimit);
        
        // Then
        assertThat(result).isNotNull();
        
        verify(customerRepository).findById(customerId);
        verify(customerRepository).save(customer);
        verify(eventPublisher).publishCustomerCreditLimitUpdated(customer, previousCreditLimit);
    }
    
    @Test
    void shouldPublishCustomerSegmentAssignedEventWhenAssigningToSegment() {
        // Given
        UUID customerId = UUID.randomUUID();
        String segmentName = "VIP";
        String assignedBy = "admin";
        
        Customer customer = new Customer("CUST001", CustomerType.INDIVIDUAL, "<EMAIL>", "Test Customer");
        CustomerDto resultDto = new CustomerDto();
        resultDto.setId(customerId);
        
        when(customerRepository.findById(customerId)).thenReturn(Optional.of(customer));
        when(customerRepository.save(customer)).thenReturn(customer);
        when(customerMapper.toDto(customer)).thenReturn(resultDto);
        when(eventPublisher.publishCustomerSegmentAssigned(customer, segmentName, assignedBy))
            .thenReturn(CompletableFuture.completedFuture(sendResult));
        
        // When
        CustomerDto result = customerApplicationService.assignToSegment(customerId, segmentName, assignedBy);
        
        // Then
        assertThat(result).isNotNull();
        
        verify(customerRepository).findById(customerId);
        verify(customerDomainService).assignCustomerToSegment(customer, segmentName, assignedBy);
        verify(customerRepository).save(customer);
        verify(eventPublisher).publishCustomerSegmentAssigned(customer, segmentName, assignedBy);
    }
    
    @Test
    void shouldHandleEventPublishingFailuresGracefully() {
        // Given
        CustomerDto customerDto = new CustomerDto();
        customerDto.setEmail("<EMAIL>");
        customerDto.setDisplayName("Test Customer");
        customerDto.setCustomerType(CustomerType.INDIVIDUAL);
        
        Customer customer = new Customer("CUST001", CustomerType.INDIVIDUAL, "<EMAIL>", "Test Customer");
        CustomerDto resultDto = new CustomerDto();
        resultDto.setId(customer.getId());
        
        when(customerDomainService.generateCustomerNumber()).thenReturn("CUST001");
        when(customerMapper.toEntity(customerDto)).thenReturn(customer);
        when(customerRepository.save(customer)).thenReturn(customer);
        when(customerMapper.toDto(customer)).thenReturn(resultDto);
        
        // Simulate event publishing failure
        CompletableFuture<SendResult<String, String>> failedFuture = new CompletableFuture<>();
        failedFuture.completeExceptionally(new RuntimeException("Kafka is down"));
        when(eventPublisher.publishCustomerCreated(customer)).thenReturn(failedFuture);
        
        // When
        CustomerDto result = customerApplicationService.createCustomer(customerDto);
        
        // Then - should still return the customer even if event publishing fails
        assertThat(result).isNotNull();
        verify(eventPublisher).publishCustomerCreated(customer);
    }
}

package com.nttdata.ndvn.customer.events.consumer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.nttdata.ndvn.customer.application.service.CustomerApplicationService;
import com.nttdata.ndvn.customer.events.consumer.events.UserCreatedEvent;
import com.nttdata.ndvn.customer.events.consumer.events.UserEnabledEvent;
import com.nttdata.ndvn.customer.events.consumer.events.UserDisabledEvent;
import com.nttdata.ndvn.customer.events.consumer.events.UserEmailVerifiedEvent;
import com.nttdata.ndvn.shared.events.consumer.BaseEventConsumer;
import com.nttdata.ndvn.shared.events.dlq.DeadLetterQueueHandler;
import io.micrometer.core.instrument.MeterRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Event consumer for user-related events in the Customer Management SCS.
 * 
 * This consumer handles user lifecycle events to maintain customer account
 * synchronization and perform related customer management operations.
 */
@Component
public class UserEventConsumer extends BaseEventConsumer {
    
    private static final Logger logger = LoggerFactory.getLogger(UserEventConsumer.class);
    private static final String CONSUMER_GROUP = "customer-management-service";
    
    private final CustomerApplicationService customerApplicationService;
    private final DeadLetterQueueHandler dlqHandler;
    
    // Simple in-memory store for processed event IDs (in production, use Redis or database)
    private final Set<String> processedEventIds = ConcurrentHashMap.newKeySet();
    
    public UserEventConsumer(ObjectMapper objectMapper,
                           MeterRegistry meterRegistry,
                           CustomerApplicationService customerApplicationService,
                           DeadLetterQueueHandler dlqHandler) {
        super(objectMapper, meterRegistry);
        this.customerApplicationService = customerApplicationService;
        this.dlqHandler = dlqHandler;
    }
    
    /**
     * Handles user events from user management service.
     */
    @KafkaListener(topics = "user.events", groupId = CONSUMER_GROUP)
    public void handleUserEvent(@Payload String eventPayload,
                              @Header Map<String, Object> headers,
                              Acknowledgment acknowledgment) {
        
        String eventType = getEventTypeFromHeaders(headers);
        
        switch (eventType) {
            case "UserCreated" -> processEvent(eventPayload, headers, acknowledgment, 
                                             UserCreatedEvent.class, this::handleUserCreated);
            case "UserEnabled" -> processEvent(eventPayload, headers, acknowledgment, 
                                             UserEnabledEvent.class, this::handleUserEnabled);
            case "UserDisabled" -> processEvent(eventPayload, headers, acknowledgment, 
                                              UserDisabledEvent.class, this::handleUserDisabled);
            case "UserEmailVerified" -> processEvent(eventPayload, headers, acknowledgment, 
                                                    UserEmailVerifiedEvent.class, this::handleUserEmailVerified);
            default -> {
                logger.warn("Unknown user event type: {}", eventType);
                acknowledgment.acknowledge();
            }
        }
    }
    
    /**
     * Handles user created events.
     * Updates customer information when a user account is created.
     */
    private void handleUserCreated(UserCreatedEvent event) {
        logger.info("Processing UserCreatedEvent for user: {} with correlation: {}", 
                   event.getUserId(), event.getCorrelationId());
        
        try {
            // Find customer by email
            var customerOptional = customerApplicationService.getCustomerByEmail(event.getEmail());
            
            if (customerOptional.isPresent()) {
                var customer = customerOptional.get();
                
                // Update customer with user account information
                // Note: CustomerDto doesn't have setDisplayName, it's computed from firstName/lastName/companyName
                // The display name is automatically computed by the getDisplayName() method
                
                // Add metadata about linked user account
                var updatedCustomer = customerApplicationService.updateCustomer(customer.getId(), customer);
                
                logger.info("Updated customer {} with user account information for user: {}", 
                           customer.getId(), event.getUserId());
                
            } else {
                logger.warn("No customer found for user email: {}, user: {}", event.getEmail(), event.getUserId());
            }
            
        } catch (Exception e) {
            logger.error("Failed to update customer for user creation: {}", event.getUserId(), e);
            throw e; // Re-throw to trigger retry logic
        }
    }
    
    /**
     * Handles user enabled events.
     * Updates customer status when user account is enabled.
     */
    private void handleUserEnabled(UserEnabledEvent event) {
        logger.info("Processing UserEnabledEvent for user: {} with correlation: {}", 
                   event.getUserId(), event.getCorrelationId());
        
        try {
            // Find customer by email
            var customerOptional = customerApplicationService.getCustomerByEmail(event.getEmail());
            
            if (customerOptional.isPresent()) {
                var customer = customerOptional.get();
                
                // Activate customer if currently inactive
                if (!"ACTIVE".equals(customer.getStatus().toString())) {
                    customerApplicationService.updateCustomerStatus(
                        customer.getId(), 
                        com.nttdata.ndvn.customer.domain.model.CustomerStatus.ACTIVE
                    );
                    
                    logger.info("Activated customer {} due to user account enablement", customer.getId());
                }
                
            } else {
                logger.warn("No customer found for enabled user email: {}", event.getEmail());
            }
            
        } catch (Exception e) {
            logger.error("Failed to update customer for user enablement: {}", event.getUserId(), e);
            throw e;
        }
    }
    
    /**
     * Handles user disabled events.
     * Updates customer status when user account is disabled.
     */
    private void handleUserDisabled(UserDisabledEvent event) {
        logger.info("Processing UserDisabledEvent for user: {} with correlation: {}", 
                   event.getUserId(), event.getCorrelationId());
        
        try {
            // Find customer by email
            var customerOptional = customerApplicationService.getCustomerByEmail(event.getEmail());
            
            if (customerOptional.isPresent()) {
                var customer = customerOptional.get();
                
                // Suspend customer if currently active
                if ("ACTIVE".equals(customer.getStatus().toString())) {
                    customerApplicationService.updateCustomerStatus(
                        customer.getId(), 
                        com.nttdata.ndvn.customer.domain.model.CustomerStatus.SUSPENDED
                    );
                    
                    logger.info("Suspended customer {} due to user account disablement", customer.getId());
                }
                
            } else {
                logger.warn("No customer found for disabled user email: {}", event.getEmail());
            }
            
        } catch (Exception e) {
            logger.error("Failed to update customer for user disablement: {}", event.getUserId(), e);
            throw e;
        }
    }
    
    /**
     * Handles user email verified events.
     * Updates customer verification status when user email is verified.
     */
    private void handleUserEmailVerified(UserEmailVerifiedEvent event) {
        logger.info("Processing UserEmailVerifiedEvent for user: {} with correlation: {}", 
                   event.getUserId(), event.getCorrelationId());
        
        try {
            // Find customer by email
            var customerOptional = customerApplicationService.getCustomerByEmail(event.getEmail());
            
            if (customerOptional.isPresent()) {
                var customer = customerOptional.get();
                
                // Update customer verification status
                // Note: This would require adding email verification fields to customer domain
                logger.info("Customer {} email verified through user account: {}", 
                           customer.getId(), event.getUserId());
                
                // TODO: Add email verification tracking to customer domain model
                
            } else {
                logger.warn("No customer found for email verified user: {}", event.getEmail());
            }
            
        } catch (Exception e) {
            logger.error("Failed to update customer for user email verification: {}", event.getUserId(), e);
            throw e;
        }
    }
    
    /**
     * Gets event type from Kafka headers.
     */
    private String getEventTypeFromHeaders(Map<String, Object> headers) {
        Object eventType = headers.get("eventType");
        if (eventType != null) {
            return eventType.toString();
        }
        return "UNKNOWN";
    }
    
    @Override
    protected String getConsumerGroup() {
        return CONSUMER_GROUP;
    }
    
    @Override
    protected boolean isDuplicateEvent(com.nttdata.ndvn.shared.events.BaseEvent event) {
        return processedEventIds.contains(event.getEventId().toString());
    }
    
    @Override
    protected void markEventAsProcessed(com.nttdata.ndvn.shared.events.BaseEvent event) {
        processedEventIds.add(event.getEventId().toString());
    }
    
    @Override
    protected void sendToDeadLetterQueue(String eventPayload, String topic, String errorType, String errorMessage) {
        dlqHandler.sendToDeadLetterQueue(topic, eventPayload, errorType, errorMessage)
            .whenComplete((result, ex) -> {
                if (ex != null) {
                    logger.error("Failed to send event to DLQ for topic: {}", topic, ex);
                } else {
                    logger.info("Successfully sent event to DLQ for topic: {}", topic);
                }
            });
    }
}

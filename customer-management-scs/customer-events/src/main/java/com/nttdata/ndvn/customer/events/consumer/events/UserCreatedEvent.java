package com.nttdata.ndvn.customer.events.consumer.events;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nttdata.ndvn.shared.events.BaseEvent;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Event received when a user is created in the user management service.
 * Used by customer management service to update corresponding customer information.
 */
public class UserCreatedEvent extends BaseEvent {
    
    @NotNull
    private UUID userId;
    
    @NotBlank
    private String username;
    
    @NotBlank
    @Email
    private String email;
    
    @NotBlank
    private String displayName;
    
    private boolean enabled;
    
    private boolean emailVerified;
    
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime createdAt;
    
    // Default constructor for Jackson
    public UserCreatedEvent() {
        super();
    }
    
    public UserCreatedEvent(UUID userId, String username, String email, String displayName,
                           boolean enabled, boolean emailVerified, LocalDateTime createdAt) {
        super("UserCreated", "1.0", "user-management-service");
        this.userId = userId;
        this.username = username;
        this.email = email;
        this.displayName = displayName;
        this.enabled = enabled;
        this.emailVerified = emailVerified;
        this.createdAt = createdAt;
        
        // Set aggregate context
        setAggregateContext(userId.toString(), "User", 1L);
    }
    
    // Getters and setters
    public UUID getUserId() { return userId; }
    public void setUserId(UUID userId) { this.userId = userId; }
    
    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }
    
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
    
    public String getDisplayName() { return displayName; }
    public void setDisplayName(String displayName) { this.displayName = displayName; }
    
    public boolean isEnabled() { return enabled; }
    public void setEnabled(boolean enabled) { this.enabled = enabled; }
    
    public boolean isEmailVerified() { return emailVerified; }
    public void setEmailVerified(boolean emailVerified) { this.emailVerified = emailVerified; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    @Override
    public String toString() {
        return "UserCreatedEvent{" +
                "userId=" + userId +
                ", username='" + username + '\'' +
                ", email='" + email + '\'' +
                ", displayName='" + displayName + '\'' +
                ", enabled=" + enabled +
                ", emailVerified=" + emailVerified +
                ", createdAt=" + createdAt +
                ", eventId=" + getEventId() +
                ", correlationId='" + getCorrelationId() + '\'' +
                '}';
    }
}

package com.nttdata.ndvn.customer.events.consumer.events;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nttdata.ndvn.shared.events.BaseEvent;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Event received when a user's email is verified in the user management service.
 * Used by customer management service to update corresponding customer verification status.
 */
public class UserEmailVerifiedEvent extends BaseEvent {
    
    @NotNull
    private UUID userId;
    
    @NotBlank
    private String username;
    
    @NotBlank
    @Email
    private String email;
    
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime verifiedAt;
    
    // Default constructor for Jackson
    public UserEmailVerifiedEvent() {
        super();
    }
    
    public UserEmailVerifiedEvent(UUID userId, String username, String email, LocalDateTime verifiedAt) {
        super("UserEmailVerified", "1.0", "user-management-service");
        this.userId = userId;
        this.username = username;
        this.email = email;
        this.verifiedAt = verifiedAt;
        
        // Set aggregate context
        setAggregateContext(userId.toString(), "User", null);
    }
    
    // Getters and setters
    public UUID getUserId() { return userId; }
    public void setUserId(UUID userId) { this.userId = userId; }
    
    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }
    
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
    
    public LocalDateTime getVerifiedAt() { return verifiedAt; }
    public void setVerifiedAt(LocalDateTime verifiedAt) { this.verifiedAt = verifiedAt; }
    
    @Override
    public String toString() {
        return "UserEmailVerifiedEvent{" +
                "userId=" + userId +
                ", username='" + username + '\'' +
                ", email='" + email + '\'' +
                ", verifiedAt=" + verifiedAt +
                ", eventId=" + getEventId() +
                ", correlationId='" + getCorrelationId() + '\'' +
                '}';
    }
}

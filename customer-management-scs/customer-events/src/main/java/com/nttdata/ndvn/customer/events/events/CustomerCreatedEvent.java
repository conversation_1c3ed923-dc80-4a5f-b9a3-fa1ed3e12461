package com.nttdata.ndvn.customer.events.events;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nttdata.ndvn.customer.domain.model.CustomerStatus;
import com.nttdata.ndvn.customer.domain.model.CustomerType;
import com.nttdata.ndvn.shared.events.BaseEvent;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Event published when a new customer is created.
 * 
 * This event extends BaseEvent to provide standardized event metadata
 * and correlation tracking.
 */
public class CustomerCreatedEvent extends BaseEvent {
    
    @NotNull
    private UUID customerId;
    
    @NotBlank
    private String customerNumber;
    
    @NotNull
    private CustomerType customerType;
    
    @NotBlank
    @Email
    private String email;
    
    @NotBlank
    private String displayName;
    
    @NotNull
    private CustomerStatus status;
    
    private String classification;
    
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime createdAt;
    
    // Default constructor for Jackson
    public CustomerCreatedEvent() {
        super();
    }
    
    public CustomerCreatedEvent(UUID customerId, String customerNumber, CustomerType customerType,
                               String email, String displayName, CustomerStatus status,
                               String classification, LocalDateTime createdAt) {
        super("CustomerCreated", "1.0", "customer-management-service");
        this.customerId = customerId;
        this.customerNumber = customerNumber;
        this.customerType = customerType;
        this.email = email;
        this.displayName = displayName;
        this.status = status;
        this.classification = classification;
        this.createdAt = createdAt;
        
        // Set aggregate context
        setAggregateContext(customerId.toString(), "Customer", 1L);
    }
    
    // Getters and setters
    public UUID getCustomerId() { return customerId; }
    public void setCustomerId(UUID customerId) { this.customerId = customerId; }
    
    public String getCustomerNumber() { return customerNumber; }
    public void setCustomerNumber(String customerNumber) { this.customerNumber = customerNumber; }
    
    public CustomerType getCustomerType() { return customerType; }
    public void setCustomerType(CustomerType customerType) { this.customerType = customerType; }
    
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
    
    public String getDisplayName() { return displayName; }
    public void setDisplayName(String displayName) { this.displayName = displayName; }
    
    public CustomerStatus getStatus() { return status; }
    public void setStatus(CustomerStatus status) { this.status = status; }
    
    public String getClassification() { return classification; }
    public void setClassification(String classification) { this.classification = classification; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    @Override
    public String toString() {
        return "CustomerCreatedEvent{" +
                "customerId=" + customerId +
                ", customerNumber='" + customerNumber + '\'' +
                ", customerType=" + customerType +
                ", email='" + email + '\'' +
                ", displayName='" + displayName + '\'' +
                ", status=" + status +
                ", classification='" + classification + '\'' +
                ", createdAt=" + createdAt +
                ", eventId=" + getEventId() +
                ", correlationId='" + getCorrelationId() + '\'' +
                '}';
    }
}

package com.nttdata.ndvn.customer.events.events;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nttdata.ndvn.shared.events.BaseEvent;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Event published when a customer's credit limit is updated.
 */
public class CustomerCreditLimitUpdatedEvent extends BaseEvent {
    
    @NotNull
    private UUID customerId;
    
    @NotBlank
    private String customerNumber;
    
    private BigDecimal previousCreditLimit;
    
    @NotNull
    private BigDecimal newCreditLimit;
    
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime updatedAt;
    
    // Default constructor for Jackson
    public CustomerCreditLimitUpdatedEvent() {
        super();
    }
    
    public CustomerCreditLimitUpdatedEvent(UUID customerId, String customerNumber,
                                          BigDecimal previousCreditLimit, BigDecimal newCreditLimit,
                                          LocalDateTime updatedAt) {
        super("CustomerCreditLimitUpdated", "1.0", "customer-management-service");
        this.customerId = customerId;
        this.customerNumber = customerNumber;
        this.previousCreditLimit = previousCreditLimit;
        this.newCreditLimit = newCreditLimit;
        this.updatedAt = updatedAt;
        
        // Set aggregate context
        setAggregateContext(customerId.toString(), "Customer", null);
    }
    
    // Getters and setters
    public UUID getCustomerId() { return customerId; }
    public void setCustomerId(UUID customerId) { this.customerId = customerId; }
    
    public String getCustomerNumber() { return customerNumber; }
    public void setCustomerNumber(String customerNumber) { this.customerNumber = customerNumber; }
    
    public BigDecimal getPreviousCreditLimit() { return previousCreditLimit; }
    public void setPreviousCreditLimit(BigDecimal previousCreditLimit) { this.previousCreditLimit = previousCreditLimit; }
    
    public BigDecimal getNewCreditLimit() { return newCreditLimit; }
    public void setNewCreditLimit(BigDecimal newCreditLimit) { this.newCreditLimit = newCreditLimit; }
    
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
    
    @Override
    public String toString() {
        return "CustomerCreditLimitUpdatedEvent{" +
                "customerId=" + customerId +
                ", customerNumber='" + customerNumber + '\'' +
                ", previousCreditLimit=" + previousCreditLimit +
                ", newCreditLimit=" + newCreditLimit +
                ", updatedAt=" + updatedAt +
                ", eventId=" + getEventId() +
                ", correlationId='" + getCorrelationId() + '\'' +
                '}';
    }
}

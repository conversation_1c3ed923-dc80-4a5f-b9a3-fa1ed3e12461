package com.nttdata.ndvn.customer.events.events;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nttdata.ndvn.shared.events.BaseEvent;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Event published when a customer is deleted.
 */
public class CustomerDeletedEvent extends BaseEvent {
    
    @NotNull
    private UUID customerId;
    
    @NotBlank
    private String customerNumber;
    
    @NotBlank
    @Email
    private String email;
    
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime deletedAt;
    
    // Default constructor for Jackson
    public CustomerDeletedEvent() {
        super();
    }
    
    public CustomerDeletedEvent(UUID customerId, String customerNumber, String email, LocalDateTime deletedAt) {
        super("CustomerDeleted", "1.0", "customer-management-service");
        this.customerId = customerId;
        this.customerNumber = customerNumber;
        this.email = email;
        this.deletedAt = deletedAt;
        
        // Set aggregate context
        setAggregateContext(customerId.toString(), "Customer", null);
    }
    
    // Getters and setters
    public UUID getCustomerId() { return customerId; }
    public void setCustomerId(UUID customerId) { this.customerId = customerId; }
    
    public String getCustomerNumber() { return customerNumber; }
    public void setCustomerNumber(String customerNumber) { this.customerNumber = customerNumber; }
    
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
    
    public LocalDateTime getDeletedAt() { return deletedAt; }
    public void setDeletedAt(LocalDateTime deletedAt) { this.deletedAt = deletedAt; }
    
    @Override
    public String toString() {
        return "CustomerDeletedEvent{" +
                "customerId=" + customerId +
                ", customerNumber='" + customerNumber + '\'' +
                ", email='" + email + '\'' +
                ", deletedAt=" + deletedAt +
                ", eventId=" + getEventId() +
                ", correlationId='" + getCorrelationId() + '\'' +
                '}';
    }
}

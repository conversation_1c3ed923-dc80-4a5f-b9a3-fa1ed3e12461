package com.nttdata.ndvn.customer.events.events;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nttdata.ndvn.shared.events.BaseEvent;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Event published when a customer is assigned to a segment.
 */
public class CustomerSegmentAssignedEvent extends BaseEvent {
    
    @NotNull
    private UUID customerId;
    
    @NotBlank
    private String customerNumber;
    
    @NotBlank
    private String segmentName;
    
    @NotBlank
    private String assignedBy;
    
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime assignedAt;
    
    // Default constructor for Jackson
    public CustomerSegmentAssignedEvent() {
        super();
    }
    
    public CustomerSegmentAssignedEvent(UUID customerId, String customerNumber, String segmentName,
                                       String assignedBy, LocalDateTime assignedAt) {
        super("CustomerSegmentAssigned", "1.0", "customer-management-service");
        this.customerId = customerId;
        this.customerNumber = customerNumber;
        this.segmentName = segmentName;
        this.assignedBy = assignedBy;
        this.assignedAt = assignedAt;
        
        // Set aggregate context
        setAggregateContext(customerId.toString(), "Customer", null);
    }
    
    // Getters and setters
    public UUID getCustomerId() { return customerId; }
    public void setCustomerId(UUID customerId) { this.customerId = customerId; }
    
    public String getCustomerNumber() { return customerNumber; }
    public void setCustomerNumber(String customerNumber) { this.customerNumber = customerNumber; }
    
    public String getSegmentName() { return segmentName; }
    public void setSegmentName(String segmentName) { this.segmentName = segmentName; }
    
    public String getAssignedBy() { return assignedBy; }
    public void setAssignedBy(String assignedBy) { this.assignedBy = assignedBy; }
    
    public LocalDateTime getAssignedAt() { return assignedAt; }
    public void setAssignedAt(LocalDateTime assignedAt) { this.assignedAt = assignedAt; }
    
    @Override
    public String toString() {
        return "CustomerSegmentAssignedEvent{" +
                "customerId=" + customerId +
                ", customerNumber='" + customerNumber + '\'' +
                ", segmentName='" + segmentName + '\'' +
                ", assignedBy='" + assignedBy + '\'' +
                ", assignedAt=" + assignedAt +
                ", eventId=" + getEventId() +
                ", correlationId='" + getCorrelationId() + '\'' +
                '}';
    }
}

package com.nttdata.ndvn.customer.events.events;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nttdata.ndvn.customer.domain.model.CustomerStatus;
import com.nttdata.ndvn.shared.events.BaseEvent;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Event published when a customer's status changes.
 */
public class CustomerStatusChangedEvent extends BaseEvent {
    
    @NotNull
    private UUID customerId;
    
    @NotBlank
    private String customerNumber;
    
    @NotNull
    private CustomerStatus previousStatus;
    
    @NotNull
    private CustomerStatus newStatus;
    
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime changedAt;
    
    // Default constructor for Jackson
    public CustomerStatusChangedEvent() {
        super();
    }
    
    public CustomerStatusChangedEvent(UUID customerId, String customerNumber,
                                     CustomerStatus previousStatus, CustomerStatus newStatus,
                                     LocalDateTime changedAt) {
        super("CustomerStatusChanged", "1.0", "customer-management-service");
        this.customerId = customerId;
        this.customerNumber = customerNumber;
        this.previousStatus = previousStatus;
        this.newStatus = newStatus;
        this.changedAt = changedAt;
        
        // Set aggregate context
        setAggregateContext(customerId.toString(), "Customer", null);
    }
    
    // Getters and setters
    public UUID getCustomerId() { return customerId; }
    public void setCustomerId(UUID customerId) { this.customerId = customerId; }
    
    public String getCustomerNumber() { return customerNumber; }
    public void setCustomerNumber(String customerNumber) { this.customerNumber = customerNumber; }
    
    public CustomerStatus getPreviousStatus() { return previousStatus; }
    public void setPreviousStatus(CustomerStatus previousStatus) { this.previousStatus = previousStatus; }
    
    public CustomerStatus getNewStatus() { return newStatus; }
    public void setNewStatus(CustomerStatus newStatus) { this.newStatus = newStatus; }
    
    public LocalDateTime getChangedAt() { return changedAt; }
    public void setChangedAt(LocalDateTime changedAt) { this.changedAt = changedAt; }
    
    @Override
    public String toString() {
        return "CustomerStatusChangedEvent{" +
                "customerId=" + customerId +
                ", customerNumber='" + customerNumber + '\'' +
                ", previousStatus=" + previousStatus +
                ", newStatus=" + newStatus +
                ", changedAt=" + changedAt +
                ", eventId=" + getEventId() +
                ", correlationId='" + getCorrelationId() + '\'' +
                '}';
    }
}

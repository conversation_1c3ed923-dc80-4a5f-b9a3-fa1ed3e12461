package com.nttdata.ndvn.customer.events.publisher;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.nttdata.ndvn.customer.domain.model.Customer;
import com.nttdata.ndvn.customer.domain.model.CustomerStatus;
import com.nttdata.ndvn.customer.events.events.CustomerCreatedEvent;
import com.nttdata.ndvn.customer.events.events.CustomerUpdatedEvent;
import com.nttdata.ndvn.customer.events.events.CustomerStatusChangedEvent;
import com.nttdata.ndvn.customer.events.events.CustomerDeletedEvent;
import com.nttdata.ndvn.customer.events.events.CustomerSegmentAssignedEvent;
import com.nttdata.ndvn.customer.events.events.CustomerCreditLimitUpdatedEvent;
import com.nttdata.ndvn.shared.events.BaseEvent;
import com.nttdata.ndvn.shared.events.publisher.BaseEventPublisher;
import io.micrometer.core.instrument.MeterRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.concurrent.CompletableFuture;

/**
 * Standardized event publisher for customer domain events using the shared events framework.
 * 
 * This publisher extends BaseEventPublisher to provide standardized event publishing
 * with correlation ID management, metrics collection, and error handling.
 */
@Component
public class StandardizedCustomerEventPublisher extends BaseEventPublisher {
    
    private static final Logger logger = LoggerFactory.getLogger(StandardizedCustomerEventPublisher.class);
    
    private static final String CUSTOMER_EVENTS_TOPIC = "customer.events";
    private static final String SERVICE_NAME = "customer-management-service";
    private static final String EVENT_VERSION = "1.0";
    
    public StandardizedCustomerEventPublisher(KafkaTemplate<String, String> kafkaTemplate,
                                            ObjectMapper objectMapper,
                                            MeterRegistry meterRegistry) {
        super(kafkaTemplate, objectMapper, meterRegistry);
    }
    
    /**
     * Publishes a customer created event.
     */
    public CompletableFuture<SendResult<String, String>> publishCustomerCreated(Customer customer) {
        CustomerCreatedEvent event = new CustomerCreatedEvent(
                customer.getId(),
                customer.getCustomerNumber(),
                customer.getCustomerType(),
                customer.getEmail(),
                customer.getDisplayName(),
                customer.getStatus(),
                customer.getClassification(),
                customer.getCreatedAt()
        );
        
        enrichCustomerEvent(event, customer);
        
        logger.info("Publishing CustomerCreatedEvent for customer: {}", customer.getId());
        return publishEvent(CUSTOMER_EVENTS_TOPIC, customer.getId().toString(), event);
    }
    
    /**
     * Publishes a customer updated event.
     */
    public CompletableFuture<SendResult<String, String>> publishCustomerUpdated(Customer customer) {
        CustomerUpdatedEvent event = new CustomerUpdatedEvent(
                customer.getId(),
                customer.getCustomerNumber(),
                customer.getCustomerType(),
                customer.getEmail(),
                customer.getDisplayName(),
                customer.getStatus(),
                customer.getClassification(),
                customer.getUpdatedAt()
        );
        
        enrichCustomerEvent(event, customer);
        
        logger.info("Publishing CustomerUpdatedEvent for customer: {}", customer.getId());
        return publishEvent(CUSTOMER_EVENTS_TOPIC, customer.getId().toString(), event);
    }
    
    /**
     * Publishes a customer status changed event.
     */
    public CompletableFuture<SendResult<String, String>> publishCustomerStatusChanged(Customer customer, CustomerStatus previousStatus) {
        CustomerStatusChangedEvent event = new CustomerStatusChangedEvent(
                customer.getId(),
                customer.getCustomerNumber(),
                previousStatus,
                customer.getStatus(),
                customer.getUpdatedAt()
        );
        
        enrichCustomerEvent(event, customer);
        
        logger.info("Publishing CustomerStatusChangedEvent for customer: {}", customer.getId());
        return publishEvent(CUSTOMER_EVENTS_TOPIC, customer.getId().toString(), event);
    }
    
    /**
     * Publishes a customer deleted event.
     */
    public CompletableFuture<SendResult<String, String>> publishCustomerDeleted(Customer customer) {
        CustomerDeletedEvent event = new CustomerDeletedEvent(
                customer.getId(),
                customer.getCustomerNumber(),
                customer.getEmail(),
                LocalDateTime.now()
        );
        
        enrichCustomerEvent(event, customer);
        
        logger.info("Publishing CustomerDeletedEvent for customer: {}", customer.getId());
        return publishEvent(CUSTOMER_EVENTS_TOPIC, customer.getId().toString(), event);
    }
    
    /**
     * Publishes a customer segment assigned event.
     */
    public CompletableFuture<SendResult<String, String>> publishCustomerSegmentAssigned(Customer customer, String segmentName, String assignedBy) {
        CustomerSegmentAssignedEvent event = new CustomerSegmentAssignedEvent(
                customer.getId(),
                customer.getCustomerNumber(),
                segmentName,
                assignedBy,
                LocalDateTime.now()
        );
        
        enrichCustomerEvent(event, customer);
        event.addMetadata("segmentName", segmentName);
        event.addMetadata("assignedBy", assignedBy);
        
        logger.info("Publishing CustomerSegmentAssignedEvent for customer: {}", customer.getId());
        return publishEvent(CUSTOMER_EVENTS_TOPIC, customer.getId().toString(), event);
    }
    
    /**
     * Publishes a customer credit limit updated event.
     */
    public CompletableFuture<SendResult<String, String>> publishCustomerCreditLimitUpdated(Customer customer, BigDecimal previousLimit) {
        CustomerCreditLimitUpdatedEvent event = new CustomerCreditLimitUpdatedEvent(
                customer.getId(),
                customer.getCustomerNumber(),
                previousLimit,
                customer.getCreditLimit(),
                customer.getUpdatedAt()
        );
        
        enrichCustomerEvent(event, customer);
        event.addMetadata("previousCreditLimit", previousLimit.toString());
        event.addMetadata("newCreditLimit", customer.getCreditLimit().toString());
        
        logger.info("Publishing CustomerCreditLimitUpdatedEvent for customer: {}", customer.getId());
        return publishEvent(CUSTOMER_EVENTS_TOPIC, customer.getId().toString(), event);
    }
    
    /**
     * Enriches customer events with common properties.
     */
    private void enrichCustomerEvent(BaseEvent event, Customer customer) {
        event.setAggregateId(customer.getId().toString());
        event.setAggregateType("Customer");
        event.setAggregateVersion(1L); // Default version since Customer doesn't have versioning
        
        // Add customer-specific metadata
        event.addMetadata("customerNumber", customer.getCustomerNumber());
        event.addMetadata("customerType", customer.getCustomerType().toString());
        event.addMetadata("customerStatus", customer.getStatus().toString());
        event.addMetadata("customerClassification", customer.getClassification());
        
        if (customer.getCreditLimit() != null) {
            event.addMetadata("creditLimit", customer.getCreditLimit().toString());
        }
        
        // Add segment information from segment assignments
        if (customer.getSegmentAssignments() != null && !customer.getSegmentAssignments().isEmpty()) {
            var primarySegment = customer.getSegmentAssignments().get(0);
            event.addMetadata("segment", primarySegment.getSegment().getName());
        }
    }
    
    @Override
    protected String getServiceName() {
        return SERVICE_NAME;
    }
    
    @Override
    protected void doAdditionalValidation(BaseEvent event) {
        // Add customer-specific validation
        if (event.getAggregateType() != null && "Customer".equals(event.getAggregateType())) {
            if (event.getAggregateId() == null || event.getAggregateId().trim().isEmpty()) {
                throw new IllegalArgumentException("Customer events must have an aggregate ID (customer ID)");
            }
            
            // Validate customer number in metadata
            if (!event.hasMetadata("customerNumber")) {
                throw new IllegalArgumentException("Customer events must include customer number in metadata");
            }
        }
    }
}

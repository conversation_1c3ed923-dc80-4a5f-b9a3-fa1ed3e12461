package com.nttdata.ndvn.customer.infrastructure.client;

import com.nttdata.ndvn.shared.infrastructure.client.BaseApiClient;
import com.nttdata.ndvn.shared.infrastructure.discovery.ServiceDiscoveryClient;
import com.nttdata.ndvn.shared.infrastructure.security.JwtTokenProvider;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import io.github.resilience4j.retry.annotation.Retry;
import io.github.resilience4j.timelimiter.annotation.TimeLimiter;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * API client for Order Management Service integration from Customer Management.
 * 
 * Provides methods for:
 * - Retrieving customer order history
 * - Checking order status
 * - Order analytics and reporting
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-28
 */
@Component
public class OrderServiceClient extends BaseApiClient {
    
    private static final String SERVICE_NAME = "order-management-service";
    
    public OrderServiceClient(ServiceDiscoveryClient serviceDiscovery,
                            WebClient.Builder webClientBuilder,
                            JwtTokenProvider tokenProvider,
                            MeterRegistry meterRegistry) {
        super(SERVICE_NAME, serviceDiscovery, webClientBuilder, tokenProvider, meterRegistry);
    }
    
    @Override
    protected String getClientServiceName() {
        return "customer-management-service";
    }
    
    /**
     * Get all orders for a specific customer.
     * 
     * @param customerId the customer ID
     * @return CompletableFuture with list of customer orders
     */
    @CircuitBreaker(name = SERVICE_NAME, fallbackMethod = "getCustomerOrdersFallback")
    @Retry(name = SERVICE_NAME)
    @TimeLimiter(name = SERVICE_NAME)
    public CompletableFuture<List<Object>> getCustomerOrders(UUID customerId) {
        return get("/api/v1/orders/customer/{customerId}", Object[].class, customerId)
                .thenApply(array -> array != null ? Arrays.asList(array) : Collections.emptyList());
    }
    
    /**
     * Get customer order summary statistics.
     * 
     * @param customerId the customer ID
     * @return CompletableFuture with order summary
     */
    @CircuitBreaker(name = SERVICE_NAME, fallbackMethod = "getCustomerOrderSummaryFallback")
    @Retry(name = SERVICE_NAME)
    @TimeLimiter(name = SERVICE_NAME)
    public CompletableFuture<Object> getCustomerOrderSummary(UUID customerId) {
        return get("/api/v1/orders/customer/{customerId}/summary", Object.class, customerId);
    }
    
    /**
     * Get recent orders for a customer.
     * 
     * @param customerId the customer ID
     * @param limit the maximum number of orders to return
     * @return CompletableFuture with recent orders
     */
    @CircuitBreaker(name = SERVICE_NAME, fallbackMethod = "getRecentOrdersFallback")
    @Retry(name = SERVICE_NAME)
    @TimeLimiter(name = SERVICE_NAME)
    public CompletableFuture<List<Object>> getRecentOrders(UUID customerId, int limit) {
        return get("/api/v1/orders/customer/{customerId}/recent?limit={limit}", Object[].class, customerId, limit)
                .thenApply(array -> array != null ? Arrays.asList(array) : Collections.emptyList());
    }
    
    /**
     * Check if customer has any pending orders.
     * 
     * @param customerId the customer ID
     * @return CompletableFuture with boolean result
     */
    @CircuitBreaker(name = SERVICE_NAME, fallbackMethod = "hasPendingOrdersFallback")
    @Retry(name = SERVICE_NAME)
    @TimeLimiter(name = SERVICE_NAME)
    public CompletableFuture<Boolean> hasPendingOrders(UUID customerId) {
        return get("/api/v1/orders/customer/{customerId}/pending/exists", Boolean.class, customerId);
    }
    
    /**
     * Get customer lifetime value based on order history.
     * 
     * @param customerId the customer ID
     * @return CompletableFuture with lifetime value
     */
    @CircuitBreaker(name = SERVICE_NAME, fallbackMethod = "getCustomerLifetimeValueFallback")
    @Retry(name = SERVICE_NAME)
    @TimeLimiter(name = SERVICE_NAME)
    public CompletableFuture<Object> getCustomerLifetimeValue(UUID customerId) {
        return get("/api/v1/analytics/customer/{customerId}/lifetime-value", Object.class, customerId);
    }
    
    // Fallback methods
    
    public CompletableFuture<List<Object>> getCustomerOrdersFallback(UUID customerId, Exception ex) {
        logger.warn("Fallback: Failed to get orders for customer {}", customerId, ex);
        return CompletableFuture.completedFuture(List.of());
    }
    
    public CompletableFuture<Object> getCustomerOrderSummaryFallback(UUID customerId, Exception ex) {
        logger.warn("Fallback: Failed to get order summary for customer {}", customerId, ex);
        return CompletableFuture.completedFuture(createEmptyOrderSummary());
    }
    
    public CompletableFuture<List<Object>> getRecentOrdersFallback(UUID customerId, int limit, Exception ex) {
        logger.warn("Fallback: Failed to get recent orders for customer {}", customerId, ex);
        return CompletableFuture.completedFuture(List.of());
    }
    
    public CompletableFuture<Boolean> hasPendingOrdersFallback(UUID customerId, Exception ex) {
        logger.warn("Fallback: Failed to check pending orders for customer {}", customerId, ex);
        return CompletableFuture.completedFuture(false);
    }
    
    public CompletableFuture<Object> getCustomerLifetimeValueFallback(UUID customerId, Exception ex) {
        logger.warn("Fallback: Failed to get lifetime value for customer {}", customerId, ex);
        return CompletableFuture.completedFuture(createEmptyLifetimeValue());
    }
    
    /**
     * Create empty order summary for fallback.
     */
    private Object createEmptyOrderSummary() {
        return new Object() {
            public final int totalOrders = 0;
            public final double totalAmount = 0.0;
            public final int pendingOrders = 0;
            public final int completedOrders = 0;
        };
    }
    
    /**
     * Create empty lifetime value for fallback.
     */
    private Object createEmptyLifetimeValue() {
        return new Object() {
            public final double totalValue = 0.0;
            public final int orderCount = 0;
            public final double averageOrderValue = 0.0;
        };
    }
}

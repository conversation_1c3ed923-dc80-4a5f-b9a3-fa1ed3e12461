# NDVN Terasoluna Base - API Documentation

## Table of Contents
- [API Overview](#api-overview)
- [Authentication](#authentication)
- [Common Patterns](#common-patterns)
- [User Management API](#user-management-api)
- [Customer Management API](#customer-management-api)
- [Product Catalog API](#product-catalog-api)
- [Order Management API](#order-management-api)
- [Notification Service API](#notification-service-api)
- [Event APIs](#event-apis)
- [Error Handling](#error-handling)

## API Overview

The NDVN Terasoluna Base platform exposes RESTful APIs for each microservice. All APIs follow consistent patterns for authentication, error handling, and response formats.

### Base URLs
- **API Gateway**: `http://localhost:8080`
- **User Management**: `http://localhost:8080/api/users`
- **Customer Management**: `http://localhost:8081/api/customers`
- **Product Catalog**: `http://localhost:8082/api/products`
- **Order Management**: `http://localhost:8083/api/orders`
- **Notification Service**: `http://localhost:8084/api/notifications`

### API Versioning
- **Current Version**: v1
- **URL Pattern**: `/api/v1/{resource}`
- **Header**: `Accept: application/vnd.ndvn.v1+json`

### Content Types
- **Request**: `application/json`
- **Response**: `application/json`
- **Character Encoding**: UTF-8

## Authentication

### OAuth 2.0 / OpenID Connect
All APIs use OAuth 2.0 with JWT tokens for authentication.

#### Token Endpoint
```http
POST /realms/ndvn-realm/protocol/openid-connect/token
Host: localhost:8180
Content-Type: application/x-www-form-urlencoded

grant_type=client_credentials&
client_id=ndvn-client&
client_secret=your-client-secret
```

#### Response
```json
{
  "access_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "Bearer",
  "expires_in": 3600,
  "scope": "read write"
}
```

#### Using the Token
```http
GET /api/v1/users
Host: localhost:8080
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

### Scopes and Permissions
- **read**: Read access to resources
- **write**: Create and update resources
- **delete**: Delete resources
- **admin**: Administrative operations

## Common Patterns

### Standard Response Format
```json
{
  "data": {},
  "meta": {
    "timestamp": "2024-01-15T10:30:00Z",
    "version": "v1",
    "requestId": "req-123456"
  },
  "links": {
    "self": "/api/v1/users/123",
    "related": "/api/v1/users/123/orders"
  }
}
```

### Pagination
```json
{
  "data": [...],
  "meta": {
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 100,
      "totalPages": 5
    }
  },
  "links": {
    "first": "/api/v1/users?page=1&size=20",
    "prev": null,
    "next": "/api/v1/users?page=2&size=20",
    "last": "/api/v1/users?page=5&size=20"
  }
}
```

### Filtering and Sorting
```http
GET /api/v1/users?filter=status:active&sort=createdAt:desc&page=1&size=20
```

## User Management API

### Endpoints

#### Get All Users
```http
GET /api/v1/users
Authorization: Bearer {token}
```

**Query Parameters:**
- `page` (integer): Page number (default: 1)
- `size` (integer): Page size (default: 20)
- `filter` (string): Filter criteria
- `sort` (string): Sort criteria

**Response:**
```json
{
  "data": [
    {
      "id": "user-123",
      "username": "john.doe",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "status": "ACTIVE",
      "roles": ["USER"],
      "createdAt": "2024-01-15T10:30:00Z",
      "updatedAt": "2024-01-15T10:30:00Z"
    }
  ]
}
```

#### Get User by ID
```http
GET /api/v1/users/{userId}
Authorization: Bearer {token}
```

#### Create User
```http
POST /api/v1/users
Authorization: Bearer {token}
Content-Type: application/json

{
  "username": "jane.doe",
  "email": "<EMAIL>",
  "firstName": "Jane",
  "lastName": "Doe",
  "password": "securePassword123"
}
```

#### Update User
```http
PUT /api/v1/users/{userId}
Authorization: Bearer {token}
Content-Type: application/json

{
  "firstName": "Jane",
  "lastName": "Smith",
  "email": "<EMAIL>"
}
```

#### Delete User
```http
DELETE /api/v1/users/{userId}
Authorization: Bearer {token}
```

### User Roles and Permissions
```http
GET /api/v1/users/{userId}/roles
POST /api/v1/users/{userId}/roles
DELETE /api/v1/users/{userId}/roles/{roleId}
```

## Customer Management API

### Endpoints

#### Get All Customers
```http
GET /api/v1/customers
Authorization: Bearer {token}
```

**Response:**
```json
{
  "data": [
    {
      "id": "customer-123",
      "customerId": "CUST-001",
      "companyName": "Acme Corp",
      "contactPerson": "John Smith",
      "email": "<EMAIL>",
      "phone": "******-0123",
      "address": {
        "street": "123 Main St",
        "city": "New York",
        "state": "NY",
        "zipCode": "10001",
        "country": "USA"
      },
      "status": "ACTIVE",
      "createdAt": "2024-01-15T10:30:00Z"
    }
  ]
}
```

#### Customer Operations
```http
POST /api/v1/customers          # Create customer
GET /api/v1/customers/{id}      # Get customer
PUT /api/v1/customers/{id}      # Update customer
DELETE /api/v1/customers/{id}   # Delete customer
```

#### Customer Analytics
```http
GET /api/v1/customers/{id}/analytics
GET /api/v1/customers/{id}/orders
GET /api/v1/customers/{id}/transactions
```

## Product Catalog API

### Endpoints

#### Get All Products
```http
GET /api/v1/products
Authorization: Bearer {token}
```

**Query Parameters:**
- `category` (string): Filter by category
- `status` (string): Filter by status
- `minPrice` (decimal): Minimum price filter
- `maxPrice` (decimal): Maximum price filter
- `search` (string): Text search

**Response:**
```json
{
  "data": [
    {
      "id": "product-123",
      "sku": "PROD-001",
      "name": "Premium Widget",
      "description": "High-quality widget for professional use",
      "category": {
        "id": "cat-123",
        "name": "Widgets",
        "path": "electronics/widgets"
      },
      "price": {
        "amount": 99.99,
        "currency": "USD"
      },
      "inventory": {
        "quantity": 100,
        "reserved": 5,
        "available": 95
      },
      "status": "ACTIVE",
      "images": [
        {
          "url": "https://cdn.example.com/product-123-main.jpg",
          "alt": "Premium Widget - Main View",
          "primary": true
        }
      ],
      "attributes": {
        "color": "Blue",
        "size": "Medium",
        "weight": "1.5kg"
      },
      "createdAt": "2024-01-15T10:30:00Z"
    }
  ]
}
```

#### Product Operations
```http
POST /api/v1/products           # Create product
GET /api/v1/products/{id}       # Get product
PUT /api/v1/products/{id}       # Update product
DELETE /api/v1/products/{id}    # Delete product
```

#### Product Categories
```http
GET /api/v1/categories
GET /api/v1/categories/{id}/products
POST /api/v1/categories
```

#### Inventory Management
```http
GET /api/v1/products/{id}/inventory
PUT /api/v1/products/{id}/inventory
POST /api/v1/products/{id}/inventory/reserve
POST /api/v1/products/{id}/inventory/release
```

## Order Management API

### Endpoints

#### Get All Orders
```http
GET /api/v1/orders
Authorization: Bearer {token}
```

**Response:**
```json
{
  "data": [
    {
      "id": "order-123",
      "orderNumber": "ORD-2024-001",
      "customerId": "customer-123",
      "status": "CONFIRMED",
      "orderDate": "2024-01-15T10:30:00Z",
      "items": [
        {
          "productId": "product-123",
          "sku": "PROD-001",
          "name": "Premium Widget",
          "quantity": 2,
          "unitPrice": 99.99,
          "totalPrice": 199.98
        }
      ],
      "totals": {
        "subtotal": 199.98,
        "tax": 20.00,
        "shipping": 10.00,
        "total": 229.98,
        "currency": "USD"
      },
      "shippingAddress": {
        "name": "John Smith",
        "street": "123 Main St",
        "city": "New York",
        "state": "NY",
        "zipCode": "10001",
        "country": "USA"
      },
      "payment": {
        "method": "CREDIT_CARD",
        "status": "PAID",
        "transactionId": "txn-123"
      }
    }
  ]
}
```

#### Order Operations
```http
POST /api/v1/orders            # Create order
GET /api/v1/orders/{id}        # Get order
PUT /api/v1/orders/{id}        # Update order
DELETE /api/v1/orders/{id}     # Cancel order
```

#### Order Status Management
```http
POST /api/v1/orders/{id}/confirm
POST /api/v1/orders/{id}/ship
POST /api/v1/orders/{id}/deliver
POST /api/v1/orders/{id}/cancel
```

#### Order Tracking
```http
GET /api/v1/orders/{id}/status
GET /api/v1/orders/{id}/tracking
GET /api/v1/orders/{id}/history
```

## Notification Service API

### Endpoints

#### Send Notification
```http
POST /api/v1/notifications
Authorization: Bearer {token}
Content-Type: application/json

{
  "type": "EMAIL",
  "recipient": "<EMAIL>",
  "subject": "Order Confirmation",
  "template": "order-confirmation",
  "data": {
    "orderNumber": "ORD-2024-001",
    "customerName": "John Smith",
    "total": 229.98
  },
  "priority": "HIGH",
  "scheduledAt": "2024-01-15T10:30:00Z"
}
```

#### Notification Types
- `EMAIL`: Email notifications
- `SMS`: SMS notifications
- `PUSH`: Push notifications
- `IN_APP`: In-application notifications

#### Get Notification Status
```http
GET /api/v1/notifications/{id}
GET /api/v1/notifications/{id}/status
```

#### Notification Templates
```http
GET /api/v1/notification-templates
POST /api/v1/notification-templates
PUT /api/v1/notification-templates/{id}
```

## Event APIs

### Event Publishing
Events are published automatically by the system, but can also be triggered via API:

```http
POST /api/v1/events
Authorization: Bearer {token}
Content-Type: application/json

{
  "eventType": "USER_CREATED",
  "aggregateId": "user-123",
  "aggregateType": "User",
  "data": {
    "userId": "user-123",
    "username": "john.doe",
    "email": "<EMAIL>"
  },
  "metadata": {
    "source": "user-management-service",
    "version": "1.0",
    "correlationId": "req-123456"
  }
}
```

### Event Types
- **User Events**: `USER_CREATED`, `USER_UPDATED`, `USER_DELETED`
- **Customer Events**: `CUSTOMER_CREATED`, `CUSTOMER_UPDATED`
- **Product Events**: `PRODUCT_CREATED`, `PRODUCT_UPDATED`, `INVENTORY_UPDATED`
- **Order Events**: `ORDER_CREATED`, `ORDER_CONFIRMED`, `ORDER_SHIPPED`, `ORDER_DELIVERED`

### Event Subscriptions
```http
GET /api/v1/events/subscriptions
POST /api/v1/events/subscriptions
DELETE /api/v1/events/subscriptions/{id}
```

## Error Handling

### Standard Error Response
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": [
      {
        "field": "email",
        "message": "Email format is invalid"
      }
    ],
    "timestamp": "2024-01-15T10:30:00Z",
    "requestId": "req-123456"
  }
}
```

### HTTP Status Codes
- **200 OK**: Successful GET, PUT
- **201 Created**: Successful POST
- **204 No Content**: Successful DELETE
- **400 Bad Request**: Invalid request data
- **401 Unauthorized**: Authentication required
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Resource not found
- **409 Conflict**: Resource conflict
- **422 Unprocessable Entity**: Validation errors
- **500 Internal Server Error**: Server error

### Error Codes
- `VALIDATION_ERROR`: Input validation failed
- `AUTHENTICATION_ERROR`: Authentication failed
- `AUTHORIZATION_ERROR`: Insufficient permissions
- `RESOURCE_NOT_FOUND`: Requested resource not found
- `RESOURCE_CONFLICT`: Resource already exists
- `BUSINESS_RULE_VIOLATION`: Business logic violation
- `EXTERNAL_SERVICE_ERROR`: External service unavailable
- `INTERNAL_ERROR`: Internal server error

For interactive API exploration, visit the Swagger UI at:
- **User Management**: http://localhost:8080/swagger-ui.html
- **Customer Management**: http://localhost:8081/swagger-ui.html
- **Product Catalog**: http://localhost:8082/swagger-ui.html
- **Order Management**: http://localhost:8083/swagger-ui.html
- **Notification Service**: http://localhost:8084/swagger-ui.html

# NDVN Terasoluna Base - Configuration Guide

## Table of Contents
- [Configuration Overview](#configuration-overview)
- [Environment Profiles](#environment-profiles)
- [Database Configuration](#database-configuration)
- [Kafka Configuration](#kafka-configuration)
- [Security Configuration](#security-configuration)
- [Service Discovery Configuration](#service-discovery-configuration)
- [Monitoring Configuration](#monitoring-configuration)
- [Logging Configuration](#logging-configuration)
- [Custom Properties](#custom-properties)

## Configuration Overview

The NDVN Terasoluna Base platform uses Spring Boot's configuration system with support for multiple environments, external configuration sources, and property encryption.

### Configuration Sources (in order of precedence)
1. **Command line arguments**
2. **Environment variables**
3. **External configuration files**
4. **Application properties/YAML files**
5. **Default values**

### Configuration Files Structure
```
src/main/resources/
├── application.yml                 # Default configuration
├── application-dev.yml            # Development environment
├── application-staging.yml        # Staging environment
├── application-prod.yml           # Production environment
└── bootstrap.yml                  # Bootstrap configuration
```

## Environment Profiles

### Development Profile (`dev`)
```yaml
# application-dev.yml
spring:
  profiles:
    active: dev
  
  datasource:
    url: ********************************/${spring.application.name}_db
    username: ndvn_user
    password: ndvn_password
    hikari:
      maximum-pool-size: 5
      minimum-idle: 2
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        format_sql: true
  
  kafka:
    bootstrap-servers: localhost:9092
    consumer:
      group-id: ${spring.application.name}-dev
      auto-offset-reset: earliest
    producer:
      retries: 3
      batch-size: 16384
  
  cloud:
    consul:
      host: localhost
      port: 8500
      discovery:
        health-check-interval: 10s
        instance-id: ${spring.application.name}-${random.uuid}

logging:
  level:
    com.nttdata.ndvn: DEBUG
    org.springframework.kafka: DEBUG
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always
```

### Staging Profile (`staging`)
```yaml
# application-staging.yml
spring:
  profiles:
    active: staging
  
  datasource:
    url: jdbc:postgresql://${DB_HOST:staging-db}:${DB_PORT:5432}/${DB_NAME}
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
  
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:staging-kafka:9092}
    consumer:
      group-id: ${spring.application.name}-staging
    producer:
      retries: 5
      acks: all
  
  cloud:
    consul:
      host: ${CONSUL_HOST:staging-consul}
      port: ${CONSUL_PORT:8500}

logging:
  level:
    com.nttdata.ndvn: INFO
    org.springframework.kafka: WARN
  file:
    name: /var/log/${spring.application.name}.log

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
```

### Production Profile (`prod`)
```yaml
# application-prod.yml
spring:
  profiles:
    active: prod
  
  datasource:
    url: jdbc:postgresql://${DB_HOST}:${DB_PORT}/${DB_NAME}
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    hikari:
      maximum-pool-size: 20
      minimum-idle: 10
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000
  
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false
    properties:
      hibernate:
        jdbc:
          batch_size: 25
        order_inserts: true
        order_updates: true
  
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS}
    consumer:
      group-id: ${spring.application.name}-prod
      enable-auto-commit: false
      auto-offset-reset: latest
    producer:
      retries: 10
      acks: all
      compression-type: snappy
      batch-size: 32768
      linger-ms: 5
  
  cloud:
    consul:
      host: ${CONSUL_HOST}
      port: ${CONSUL_PORT:8500}
      discovery:
        health-check-interval: 30s
        health-check-timeout: 10s

logging:
  level:
    com.nttdata.ndvn: INFO
    org.springframework.kafka: WARN
    org.hibernate.SQL: WARN
  file:
    name: /var/log/${spring.application.name}.log
  logback:
    rollingpolicy:
      max-file-size: 100MB
      max-history: 30

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
```

## Database Configuration

### Connection Pool Settings
```yaml
spring:
  datasource:
    hikari:
      # Connection pool size
      maximum-pool-size: 20
      minimum-idle: 5
      
      # Connection timeouts
      connection-timeout: 30000      # 30 seconds
      idle-timeout: 600000          # 10 minutes
      max-lifetime: 1800000         # 30 minutes
      
      # Leak detection
      leak-detection-threshold: 60000  # 1 minute
      
      # Connection validation
      connection-test-query: SELECT 1
      validation-timeout: 5000
```

### JPA/Hibernate Configuration
```yaml
spring:
  jpa:
    # Database platform
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    
    # DDL generation
    hibernate:
      ddl-auto: none  # none, validate, update, create, create-drop
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
    
    # SQL logging
    show-sql: false
    properties:
      hibernate:
        format_sql: true
        use_sql_comments: true
        
        # Performance settings
        jdbc:
          batch_size: 25
          fetch_size: 50
        order_inserts: true
        order_updates: true
        
        # Cache settings
        cache:
          use_second_level_cache: true
          use_query_cache: true
          region:
            factory_class: org.hibernate.cache.jcache.JCacheRegionFactory
```

### Database Migration (Flyway)
```yaml
spring:
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    validate-on-migrate: true
    clean-disabled: true
    schemas: public
```

## Kafka Configuration

### Producer Configuration
```yaml
spring:
  kafka:
    producer:
      # Serialization
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
      
      # Reliability
      acks: all                    # Wait for all replicas
      retries: 10                  # Retry failed sends
      enable-idempotence: true     # Exactly-once semantics
      
      # Performance
      batch-size: 32768           # 32KB batch size
      linger-ms: 5                # Wait 5ms for batching
      compression-type: snappy    # Compression algorithm
      buffer-memory: 33554432     # 32MB buffer
      
      # Timeouts
      request-timeout-ms: 30000   # 30 seconds
      delivery-timeout-ms: 120000 # 2 minutes
```

### Consumer Configuration
```yaml
spring:
  kafka:
    consumer:
      # Serialization
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      
      # Consumer group
      group-id: ${spring.application.name}
      auto-offset-reset: latest
      enable-auto-commit: false
      
      # Performance
      fetch-min-size: 1024        # 1KB minimum fetch
      fetch-max-wait: 500         # 500ms max wait
      max-poll-records: 500       # Max records per poll
      
      # Session management
      session-timeout-ms: 30000   # 30 seconds
      heartbeat-interval-ms: 3000 # 3 seconds
      
      # JSON deserialization
      properties:
        spring.json.trusted.packages: "com.nttdata.ndvn"
        spring.json.type.mapping: >
          userEvent:com.nttdata.ndvn.shared.events.UserEvent,
          customerEvent:com.nttdata.ndvn.shared.events.CustomerEvent,
          orderEvent:com.nttdata.ndvn.shared.events.OrderEvent
```

### Topic Configuration
```yaml
# Custom topic configuration
ndvn:
  kafka:
    topics:
      user-events:
        name: user-events
        partitions: 3
        replication-factor: 1
        config:
          retention.ms: 604800000  # 7 days
          cleanup.policy: delete
      
      order-events:
        name: order-events
        partitions: 6
        replication-factor: 1
        config:
          retention.ms: 2592000000  # 30 days
          cleanup.policy: delete
```

## Security Configuration

### OAuth 2.0 Resource Server
```yaml
spring:
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: ${KEYCLOAK_URL}/realms/${KEYCLOAK_REALM}
          jwk-set-uri: ${KEYCLOAK_URL}/realms/${KEYCLOAK_REALM}/protocol/openid-connect/certs
          
# Custom security configuration
ndvn:
  security:
    cors:
      allowed-origins: ${CORS_ALLOWED_ORIGINS:http://localhost:3000}
      allowed-methods: GET,POST,PUT,DELETE,OPTIONS
      allowed-headers: "*"
      allow-credentials: true
      max-age: 3600
    
    jwt:
      # JWT validation settings
      clock-skew: 60  # seconds
      
    rate-limiting:
      enabled: true
      requests-per-minute: 100
      burst-capacity: 200
```

### Method Security
```yaml
spring:
  security:
    method:
      enabled: true
      
# Role-based access control
ndvn:
  security:
    roles:
      admin:
        - ROLE_ADMIN
        - ROLE_USER
      user:
        - ROLE_USER
      service:
        - ROLE_SERVICE
```

## Service Discovery Configuration

### Consul Configuration
```yaml
spring:
  cloud:
    consul:
      # Connection settings
      host: ${CONSUL_HOST:localhost}
      port: ${CONSUL_PORT:8500}
      scheme: http
      
      # Service discovery
      discovery:
        enabled: true
        register: true
        deregister: true
        prefer-ip-address: true
        instance-id: ${spring.application.name}-${random.uuid}
        service-name: ${spring.application.name}
        health-check-path: /actuator/health
        health-check-interval: 30s
        health-check-timeout: 10s
        health-check-critical-timeout: 3m
        
        # Tags for service identification
        tags:
          - version=${project.version:unknown}
          - profile=${spring.profiles.active:default}
          - zone=${DEPLOYMENT_ZONE:default}
      
      # Configuration management
      config:
        enabled: true
        format: YAML
        prefix: config
        default-context: application
        profile-separator: ','
        data-key: data
        watch:
          enabled: true
          delay: 1000
```

## Monitoring Configuration

### Actuator Configuration
```yaml
management:
  # Endpoint configuration
  endpoints:
    web:
      base-path: /actuator
      exposure:
        include: health,info,metrics,prometheus,loggers
        exclude: shutdown
  
  # Health endpoint
  endpoint:
    health:
      show-details: when-authorized
      show-components: always
      probes:
        enabled: true
  
  # Metrics configuration
  metrics:
    export:
      prometheus:
        enabled: true
        step: 30s
        descriptions: true
    distribution:
      percentiles-histogram:
        http.server.requests: true
      percentiles:
        http.server.requests: 0.5,0.9,0.95,0.99
    tags:
      application: ${spring.application.name}
      environment: ${spring.profiles.active}
  
  # Info endpoint
  info:
    env:
      enabled: true
    git:
      mode: full
    build:
      enabled: true
```

### Tracing Configuration
```yaml
spring:
  sleuth:
    sampler:
      probability: 1.0  # Sample 100% in dev, reduce in prod
    zipkin:
      base-url: ${ZIPKIN_URL:http://localhost:9411}
      sender:
        type: web
    
    # Kafka tracing
    kafka:
      enabled: true
    
    # Database tracing
    jdbc:
      enabled: true
      includes:
        - fetch
        - query
        - connection
```

## Logging Configuration

### Logback Configuration
```yaml
logging:
  # Log levels
  level:
    com.nttdata.ndvn: INFO
    org.springframework.kafka: WARN
    org.hibernate.SQL: WARN
    org.springframework.security: DEBUG
  
  # File logging
  file:
    name: /var/log/${spring.application.name}.log
  
  # Rolling policy
  logback:
    rollingpolicy:
      max-file-size: 100MB
      max-history: 30
      total-size-cap: 3GB
  
  # Pattern configuration
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n"
```

### Structured Logging
```yaml
# Custom logging configuration
ndvn:
  logging:
    structured:
      enabled: true
      format: json
      include-mdc: true
      include-arguments: false
      include-exception: true
    
    correlation:
      enabled: true
      header-name: X-Correlation-ID
      mdc-key: correlationId
```

## Custom Properties

### Application-Specific Configuration
```yaml
# NDVN-specific configuration
ndvn:
  # Application metadata
  application:
    name: ${spring.application.name}
    version: ${project.version:unknown}
    description: "NDVN Terasoluna Base Microservice"
  
  # Feature flags
  features:
    event-sourcing:
      enabled: true
    caching:
      enabled: true
      ttl: 3600  # seconds
    rate-limiting:
      enabled: true
      requests-per-minute: 100
  
  # Business configuration
  business:
    order:
      auto-confirm-timeout: 300  # seconds
      max-items-per-order: 100
    
    notification:
      retry-attempts: 3
      retry-delay: 5000  # milliseconds
      batch-size: 50
  
  # Integration settings
  integrations:
    payment-gateway:
      url: ${PAYMENT_GATEWAY_URL}
      timeout: 30000  # milliseconds
      retry-attempts: 3
    
    email-service:
      provider: smtp
      host: ${SMTP_HOST}
      port: ${SMTP_PORT:587}
      username: ${SMTP_USERNAME}
      password: ${SMTP_PASSWORD}
```

### Environment Variables Reference
```bash
# Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=service_db
DB_USERNAME=ndvn_user
DB_PASSWORD=ndvn_password

# Kafka
KAFKA_BOOTSTRAP_SERVERS=localhost:9092

# Consul
CONSUL_HOST=localhost
CONSUL_PORT=8500

# Security
KEYCLOAK_URL=http://localhost:8180
KEYCLOAK_REALM=ndvn-realm
CORS_ALLOWED_ORIGINS=http://localhost:3000

# Monitoring
ZIPKIN_URL=http://localhost:9411
PROMETHEUS_URL=http://localhost:9090

# External Services
PAYMENT_GATEWAY_URL=https://api.payment-provider.com
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
```

For environment-specific configuration examples, see the `config/` directory in each service module.

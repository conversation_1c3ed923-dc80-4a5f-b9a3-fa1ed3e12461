# NDVN Terasoluna Base - Setup and Deployment Guide

## Table of Contents
- [Prerequisites](#prerequisites)
- [Quick Start](#quick-start)
- [Infrastructure Setup](#infrastructure-setup)
- [Application Deployment](#application-deployment)
- [Development Environment](#development-environment)
- [Production Deployment](#production-deployment)
- [Configuration](#configuration)
- [Verification](#verification)

## Prerequisites

### System Requirements
- **Operating System**: Linux, macOS, or Windows with WSL2
- **Memory**: Minimum 8GB RAM (16GB recommended)
- **CPU**: 4+ cores recommended
- **Disk Space**: 20GB+ available space
- **Network**: Internet connection for downloading dependencies

### Required Software
- **Java**: OpenJDK 21 or later
- **Docker**: 20.10+ with Docker Compose v2.0+
- **Git**: Latest version
- **Gradle**: 8.14+ (or use wrapper)

### Optional Tools
- **IDE**: IntelliJ IDEA, Eclipse, or VS Code
- **Postman**: For API testing
- **pgAdmin**: PostgreSQL administration
- **Kafka Tool**: Kafka cluster management

## Quick Start

### 1. Clone the Repository
```bash
git clone https://github.com/dungho11nttdata/ndvn-terasoluna-base.git
cd ndvn-terasoluna-base
```

### 2. Start Infrastructure
```bash
# Start all infrastructure services
./infrastructure/scripts/start-all.sh

# Or start individual components
./infrastructure/kafka/scripts/setup-kafka.sh start
./infrastructure/gateway/scripts/setup-gateway.sh start
./infrastructure/auth/scripts/setup-auth.sh start
./infrastructure/monitoring/scripts/setup-monitoring.sh start
```

### 3. Build and Start Applications
```bash
# Build all services
./gradlew build

# Start all microservices
./scripts/start-services.sh

# Or start individual services
cd user-management-scs && ./gradlew bootRun
cd customer-management-scs && ./gradlew bootRun
cd product-catalog-scs && ./gradlew bootRun
cd order-management-scs && ./gradlew bootRun
cd notification-service-scs && ./gradlew bootRun
```

### 4. Verify Setup
```bash
# Check service health
curl http://localhost:8080/actuator/health
curl http://localhost:8081/actuator/health
curl http://localhost:8082/actuator/health
curl http://localhost:8083/actuator/health
curl http://localhost:8084/actuator/health

# Access Swagger UI
open http://localhost:8080/swagger-ui.html
```

## Infrastructure Setup

### Kafka Message Broker
```bash
# Start Kafka cluster
cd infrastructure/kafka
./scripts/setup-kafka.sh start

# Verify Kafka is running
docker ps | grep kafka
docker logs ndvn-kafka

# Create topics (if not auto-created)
./scripts/create-topics.sh
```

**Kafka Configuration:**
- **Broker**: localhost:9092
- **Zookeeper**: localhost:2181
- **Topics**: Auto-created with 3 partitions, replication factor 1

### PostgreSQL Databases
```bash
# Start PostgreSQL instances
cd infrastructure/databases
./scripts/setup-databases.sh start

# Verify databases
docker ps | grep postgres
psql -h localhost -p 5432 -U ndvn_user -d user_management_db
```

**Database Configuration:**
- **Host**: localhost
- **Ports**: 5432-5436 (one per service)
- **Username**: ndvn_user
- **Password**: ndvn_password

### Service Discovery (Consul)
```bash
# Start Consul
cd infrastructure/service-discovery
./scripts/setup-consul.sh start

# Access Consul UI
open http://localhost:8500
```

### API Gateway
```bash
# Start API Gateway
cd infrastructure/gateway
./scripts/setup-gateway.sh start

# Verify gateway
curl http://localhost:8080/actuator/health
```

### Authentication (Keycloak)
```bash
# Start Keycloak
cd infrastructure/auth
./scripts/setup-auth.sh start

# Access Keycloak Admin Console
open http://localhost:8180/admin
# Username: admin, Password: admin
```

### Monitoring Stack
```bash
# Start monitoring services
cd infrastructure/monitoring
./scripts/setup-monitoring.sh start

# Access monitoring UIs
open http://localhost:3000  # Grafana (admin/admin)
open http://localhost:9090  # Prometheus
open http://localhost:16686 # Jaeger
```

## Application Deployment

### Build Process
```bash
# Clean and build all services
./gradlew clean build

# Build specific service
cd user-management-scs
./gradlew clean build

# Build Docker images
./gradlew buildDockerImage

# Run tests
./gradlew test
./gradlew integrationTest
```

### Service Startup Order
1. **Infrastructure Services** (Kafka, PostgreSQL, Consul)
2. **Gateway Service** (API Gateway)
3. **Core Services** (User Management, Customer Management)
4. **Business Services** (Product Catalog, Order Management)
5. **Support Services** (Notification Service)

### Environment-Specific Deployment

#### Development Environment
```bash
# Use development profiles
export SPRING_PROFILES_ACTIVE=dev

# Start with development configuration
./scripts/start-dev-environment.sh
```

#### Staging Environment
```bash
# Use staging profiles
export SPRING_PROFILES_ACTIVE=staging

# Deploy to staging
./scripts/deploy-staging.sh
```

#### Production Environment
```bash
# Use production profiles
export SPRING_PROFILES_ACTIVE=prod

# Deploy to production
./scripts/deploy-production.sh
```

## Development Environment

### IDE Setup

#### IntelliJ IDEA
1. Import as Gradle project
2. Set Project SDK to Java 21
3. Enable annotation processing
4. Install plugins: Spring Boot, Lombok
5. Configure code style (provided in `.editorconfig`)

#### VS Code
1. Install Java Extension Pack
2. Install Spring Boot Extension Pack
3. Configure Java runtime to Java 21
4. Import workspace settings

### Local Development
```bash
# Start infrastructure only
./infrastructure/scripts/start-all.sh

# Run service in development mode
cd user-management-scs
./gradlew bootRun --args='--spring.profiles.active=dev'

# Enable debug mode
./gradlew bootRun --debug-jvm

# Hot reload with Spring Boot DevTools
# (automatically enabled in development profile)
```

### Testing
```bash
# Run unit tests
./gradlew test

# Run integration tests
./gradlew integrationTest

# Run specific test class
./gradlew test --tests UserServiceTest

# Generate test reports
./gradlew jacocoTestReport
open build/reports/jacoco/test/html/index.html
```

## Production Deployment

### Docker Deployment
```bash
# Build production images
./gradlew buildDockerImage

# Deploy with Docker Compose
docker-compose -f docker-compose.prod.yml up -d

# Scale services
docker-compose -f docker-compose.prod.yml up -d --scale user-management=3
```

### Kubernetes Deployment
```bash
# Apply Kubernetes manifests
kubectl apply -f k8s/namespace.yaml
kubectl apply -f k8s/configmaps/
kubectl apply -f k8s/secrets/
kubectl apply -f k8s/deployments/
kubectl apply -f k8s/services/
kubectl apply -f k8s/ingress/

# Verify deployment
kubectl get pods -n ndvn-scs
kubectl get services -n ndvn-scs
```

### Health Checks
```bash
# Application health
curl http://localhost:8080/actuator/health

# Detailed health information
curl http://localhost:8080/actuator/health/detailed

# Readiness probe
curl http://localhost:8080/actuator/health/readiness

# Liveness probe
curl http://localhost:8080/actuator/health/liveness
```

## Configuration

### Environment Variables
```bash
# Database configuration
export DB_HOST=localhost
export DB_PORT=5432
export DB_USERNAME=ndvn_user
export DB_PASSWORD=ndvn_password

# Kafka configuration
export KAFKA_BOOTSTRAP_SERVERS=localhost:9092

# Service discovery
export CONSUL_HOST=localhost
export CONSUL_PORT=8500

# Authentication
export KEYCLOAK_URL=http://localhost:8180
export KEYCLOAK_REALM=ndvn-realm
```

### Application Properties
```yaml
# application.yml
spring:
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}
  
  datasource:
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME}
    username: ${DB_USERNAME:ndvn_user}
    password: ${DB_PASSWORD:ndvn_password}
  
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:localhost:9092}
  
  cloud:
    consul:
      host: ${CONSUL_HOST:localhost}
      port: ${CONSUL_PORT:8500}
```

### Security Configuration
```yaml
# Security settings
security:
  oauth2:
    resourceserver:
      jwt:
        issuer-uri: ${KEYCLOAK_URL}/realms/${KEYCLOAK_REALM}
  
  cors:
    allowed-origins: ${CORS_ALLOWED_ORIGINS:http://localhost:3000}
    allowed-methods: GET,POST,PUT,DELETE,OPTIONS
```

## Verification

### Service Health Verification
```bash
# Check all service health endpoints
./scripts/health-check.sh

# Individual service checks
curl -f http://localhost:8080/actuator/health || echo "User Management: DOWN"
curl -f http://localhost:8081/actuator/health || echo "Customer Management: DOWN"
curl -f http://localhost:8082/actuator/health || echo "Product Catalog: DOWN"
curl -f http://localhost:8083/actuator/health || echo "Order Management: DOWN"
curl -f http://localhost:8084/actuator/health || echo "Notification Service: DOWN"
```

### Infrastructure Verification
```bash
# Kafka
docker exec ndvn-kafka kafka-topics.sh --bootstrap-server localhost:9092 --list

# PostgreSQL
docker exec ndvn-postgres psql -U ndvn_user -l

# Consul
curl http://localhost:8500/v1/agent/services

# Keycloak
curl http://localhost:8180/realms/ndvn-realm/.well-known/openid_configuration
```

### API Testing
```bash
# Test API endpoints
curl -X GET http://localhost:8080/api/users
curl -X GET http://localhost:8081/api/customers
curl -X GET http://localhost:8082/api/products
curl -X GET http://localhost:8083/api/orders
curl -X GET http://localhost:8084/api/notifications

# Test with authentication
TOKEN=$(curl -X POST http://localhost:8180/realms/ndvn-realm/protocol/openid-connect/token \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=client_credentials&client_id=ndvn-client&client_secret=secret" \
  | jq -r '.access_token')

curl -H "Authorization: Bearer $TOKEN" http://localhost:8080/api/users
```

### Monitoring Verification
```bash
# Check metrics endpoint
curl http://localhost:8080/actuator/prometheus

# Verify Grafana dashboards
open http://localhost:3000/dashboards

# Check Jaeger traces
open http://localhost:16686
```

For troubleshooting common issues, see [TROUBLESHOOTING.md](TROUBLESHOOTING.md).

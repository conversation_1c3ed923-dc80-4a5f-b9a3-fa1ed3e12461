<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NDVN SCS Platform - Development Environment</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; }
        .services { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-top: 30px; }
        .service { padding: 20px; border: 1px solid #ddd; border-radius: 5px; background: #f9f9f9; }
        .service h3 { margin-top: 0; color: #0066cc; }
        .service a { color: #0066cc; text-decoration: none; }
        .service a:hover { text-decoration: underline; }
        .status { display: inline-block; padding: 2px 8px; border-radius: 3px; font-size: 12px; }
        .status.running { background: #d4edda; color: #155724; }
        .status.stopped { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>NDVN SCS Platform - Development Environment</h1>
        <p>Welcome to the NDVN Self-Contained Systems development environment. Below are the available services and tools.</p>
        
        <div class="services">
            <div class="service">
                <h3>Infrastructure Services</h3>
                <ul>
                    <li><a href="http://localhost:8500" target="_blank">Consul</a> - Service Discovery</li>
                    <li><a href="http://localhost:8080" target="_blank">Kafka UI</a> - Message Broker Management</li>
                    <li><a href="http://localhost:8090/admin" target="_blank">Keycloak</a> - Authentication Server</li>
                    <li><a href="http://localhost:6379" target="_blank">Redis</a> - Caching & Session Store</li>
                </ul>
            </div>
            
            <div class="service">
                <h3>Monitoring & Observability</h3>
                <ul>
                    <li><a href="http://localhost:3000" target="_blank">Grafana</a> - Metrics Dashboard</li>
                    <li><a href="http://localhost:9090" target="_blank">Prometheus</a> - Metrics Collection</li>
                    <li><a href="http://localhost:5601" target="_blank">Kibana</a> - Log Analysis</li>
                    <li><a href="http://localhost:16686" target="_blank">Jaeger</a> - Distributed Tracing</li>
                </ul>
            </div>
            
            <div class="service">
                <h3>Development Tools</h3>
                <ul>
                    <li><a href="http://localhost:8082" target="_blank">Adminer</a> - Database Management</li>
                    <li><a href="http://localhost:8083" target="_blank">Redis Commander</a> - Redis Management</li>
                    <li><a href="http://localhost:8025" target="_blank">MailHog</a> - Email Testing</li>
                    <li><a href="http://localhost:9001" target="_blank">MinIO Console</a> - Object Storage</li>
                </ul>
            </div>
            
            <div class="service">
                <h3>SCS Services</h3>
                <ul>
                    <li><a href="http://localhost:8081/actuator/health" target="_blank">User Management</a> - User & Auth Service</li>
                    <li><a href="http://localhost:8082/actuator/health" target="_blank">Customer Management</a> - Customer Service</li>
                    <li><a href="http://localhost:8083/actuator/health" target="_blank">Product Catalog</a> - Product Service</li>
                    <li><a href="http://localhost:8084/actuator/health" target="_blank">Order Management</a> - Order Service</li>
                    <li><a href="http://localhost:8085/actuator/health" target="_blank">Notification</a> - Notification Service</li>
                </ul>
            </div>
        </div>
        
        <div style="margin-top: 30px; padding: 20px; background: #e9ecef; border-radius: 5px;">
            <h3>Quick Start Commands</h3>
            <pre style="background: #f8f9fa; padding: 15px; border-radius: 3px; overflow-x: auto;">
# Start all infrastructure services
./infrastructure/scripts/start-all.sh

# Connect to development tools container
docker exec -it ndvn-dev-tools bash

# Connect to databases
db-connect user      # User Management DB
db-connect customer  # Customer Management DB
db-connect catalog   # Product Catalog DB
db-connect order     # Order Management DB

# Kafka operations
kafka-list-topics
kafka-create-topic test-topic
            </pre>
        </div>
    </div>
</body>
</html>

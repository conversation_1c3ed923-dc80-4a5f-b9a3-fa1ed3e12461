# Notification Self-Contained System (SCS)

## Overview

The Notification SCS is a comprehensive notification management system that provides multi-channel communication capabilities for the NDVN platform. It handles email, SMS, push notifications, and in-app messaging with robust delivery tracking, template management, and event-driven integration.

## Architecture

The service follows the established Terasoluna framework patterns with clean architecture principles:

```
notification-scs/
├── notification-domain/          # Core business logic and entities
├── notification-infrastructure/  # Data access and external integrations
├── notification-application/     # Use cases and application services
├── notification-events/         # Event handling and Kafka integration
└── notification-web/            # REST API and Spring Boot configuration
```

### Key Design Principles

- **Domain-Driven Design**: Clear bounded context with ubiquitous language
- **Clean Architecture**: Dependency inversion and separation of concerns
- **Event-Driven Architecture**: Publishes domain events for loose coupling
- **Multi-Channel Delivery**: Support for email, SMS, push, and in-app notifications
- **Resilient Communication**: Circuit breakers, retries, and fallback mechanisms

## 🚀 Features

### Notification Management
- ✅ Multi-channel notification delivery (email, SMS, push, in-app)
- ✅ Notification scheduling and delayed delivery
- ✅ Priority-based processing
- ✅ Delivery tracking and status monitoring
- ✅ Correlation ID support for tracing

### Event-Driven Integration
- ✅ Kafka-based event consumption from other services
- ✅ Automatic notification triggering based on business events
- ✅ Event publishing for notification status updates
- ✅ Dead letter queue handling for failed events

### Delivery Channels
- ✅ **Email**: SMTP integration with HTML support
- ✅ **SMS**: External SMS provider integration
- ✅ **Push Notifications**: Mobile push notification support
- ✅ **In-App**: Internal application notifications

### Resilience and Reliability
- ✅ Circuit breaker pattern for external services
- ✅ Retry mechanisms with exponential backoff
- ✅ Delivery attempt tracking
- ✅ Failure handling and error reporting

## 🛠️ Technology Stack

- **Framework**: Spring Boot 3.4.1 with Terasoluna 5.10.0
- **Database**: PostgreSQL 15 with JPA/Hibernate
- **Caching**: Redis for performance optimization
- **Messaging**: Apache Kafka for event-driven communication
- **Security**: Spring Security with OAuth 2.0 JWT validation
- **Service Discovery**: Consul integration
- **Resilience**: Resilience4j for circuit breakers and retries
- **Monitoring**: Prometheus metrics, health checks
- **Testing**: JUnit 5, Testcontainers, MockMvc

## 🚦 Quick Start

### Prerequisites
- Java 21+
- Docker and Docker Compose
- Running infrastructure (see `/infrastructure` directory)

### 1. Start Infrastructure
```bash
# Start complete infrastructure
./infrastructure/scripts/start-all.sh

# Or start individual components
docker-compose -f infrastructure/kafka/docker-compose.yml up -d
docker-compose -f infrastructure/auth/docker-compose.yml up -d
```

### 2. Database Setup
```bash
# Create notification database
createdb notification_db

# Run migrations (if using Flyway)
./gradlew flywayMigrate
```

### 3. Build and Run
```bash
# Build the service
./gradlew :notification-scs:build

# Run the service
./gradlew :notification-scs:notification-web:bootRun

# Or run with specific profile
./gradlew :notification-scs:notification-web:bootRun --args='--spring.profiles.active=dev'
```

### 4. Verify Service
```bash
# Check health
curl http://localhost:8084/actuator/health

# Check service registration
curl http://localhost:8500/v1/agent/services
```

## 📡 API Endpoints

### Notification Operations
- `POST /api/v1/notifications/send` - Send notification immediately
- `POST /api/v1/notifications/schedule` - Schedule notification for later
- `GET /api/v1/notifications/{id}` - Get notification details
- `GET /api/v1/notifications` - List notifications (paginated)
- `GET /api/v1/notifications/status/{status}` - Filter by status
- `GET /api/v1/notifications/correlation/{correlationId}` - Filter by correlation ID
- `DELETE /api/v1/notifications/{id}` - Delete notification

### Administrative Operations
- `POST /api/v1/notifications/process-scheduled` - Process scheduled notifications
- `GET /actuator/health` - Health check
- `GET /actuator/metrics` - Metrics
- `GET /swagger-ui.html` - API documentation

## 🔧 Configuration

### Environment Variables
```bash
# Database
DB_USERNAME=notification_user
DB_PASSWORD=notification_pass

# Redis
REDIS_PASSWORD=your_redis_password

# Email
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_app_password

# SMS Provider
SMS_PROVIDER_URL=https://api.sms-provider.com
SMS_API_KEY=your_sms_api_key

# Push Notifications
PUSH_PROVIDER_URL=https://fcm.googleapis.com
PUSH_API_KEY=your_push_api_key
```

### Kafka Topics
- `notification.events` - Notification status events
- `order.events` - Order events (consumed)
- `customer.events` - Customer events (consumed)
- `user.events` - User events (consumed)

## 🧪 Testing

### Unit Tests
```bash
./gradlew :notification-scs:test
```

### Integration Tests
```bash
./gradlew :notification-scs:integrationTest
```

### Test with Testcontainers
```bash
./gradlew :notification-scs:test -Dspring.profiles.active=test
```

## 📊 Monitoring

### Health Checks
- Database connectivity
- Redis connectivity
- Kafka connectivity
- External service availability

### Metrics
- Notification delivery rates
- Channel-specific success rates
- Processing latencies
- Error rates by channel
- Queue depths

### Logging
- Structured logging with correlation IDs
- Delivery attempt tracking
- Error logging with context
- Performance metrics

## 🔒 Security

### Authentication
- OAuth 2.0 JWT token validation
- Role-based access control (RBAC)
- Service-to-service authentication

### Authorization Roles
- `ADMIN`: Full access to all operations
- `SERVICE`: Send notifications and view status
- `USER`: View own notifications only

## 🚀 Deployment

### Docker
```bash
# Build Docker image
docker build -t notification-service:latest .

# Run container
docker run -p 8084:8084 \
  -e SPRING_PROFILES_ACTIVE=prod \
  -e DB_HOST=postgres \
  notification-service:latest
```

### Kubernetes
```bash
# Deploy to Kubernetes
kubectl apply -f k8s/notification-service.yaml
```

## 🔄 Event Integration

### Consumed Events
- **Order Events**: Order placed, shipped, delivered, cancelled
- **Customer Events**: Customer created, updated, status changed
- **User Events**: User created, email verified, password reset

### Published Events
- **NotificationSentEvent**: When notification is successfully delivered
- **NotificationFailedEvent**: When notification delivery fails

## 📈 Performance

### Optimization Features
- Redis caching for frequently accessed data
- Asynchronous processing for delivery
- Connection pooling for database and external services
- Batch processing for bulk notifications

### Scalability
- Horizontal scaling support
- Stateless design
- Event-driven architecture
- Circuit breaker pattern for resilience

This Notification SCS provides a robust, scalable foundation for multi-channel communication in the NDVN platform, following established patterns and best practices for enterprise-grade systems.

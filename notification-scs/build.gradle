plugins {
    id 'java'
    id 'org.springframework.boot' version '3.4.1' apply false
    id 'io.spring.dependency-management' version '1.1.7' apply false
}

allprojects {
    group = 'com.nttdata.ndvn.notification'
    version = '1.0.0-SNAPSHOT'
    
    repositories {
        mavenLocal()
        mavenCentral()
        maven { url = 'https://packages.confluent.io/maven/' }
        maven { url = 'https://repo.spring.io/milestone' }
        maven { url = 'https://repo.spring.io/snapshot' }
    }
}

subprojects {
    apply plugin: 'java'
    apply plugin: 'io.spring.dependency-management'
    
    java {
        sourceCompatibility = JavaVersion.VERSION_21
        targetCompatibility = JavaVersion.VERSION_21
    }
    
    dependencyManagement {
        imports {
            mavenBom 'org.springframework.boot:spring-boot-dependencies:3.4.1'
            mavenBom 'org.springframework.cloud:spring-cloud-dependencies:2024.0.0'
            mavenBom 'org.terasoluna.gfw:terasoluna-gfw-parent:5.10.0.RELEASE'
        }
    }
    
    dependencies {
        // Common dependencies for all modules
        implementation 'org.springframework.boot:spring-boot-starter'
        implementation 'org.springframework.boot:spring-boot-starter-validation'
        implementation 'org.slf4j:slf4j-api'
        
        // Testing
        testImplementation 'org.springframework.boot:spring-boot-starter-test'
        testImplementation 'org.junit.jupiter:junit-jupiter'
        testImplementation 'org.mockito:mockito-core'
        testImplementation 'org.mockito:mockito-junit-jupiter'
        testImplementation 'org.assertj:assertj-core'
    }
    
    test {
        useJUnitPlatform()
    }
    
    // Common configuration
    ext {
        terasolunaVersion = '5.10.0.RELEASE'
        springBootVersion = '3.4.1'
        springCloudVersion = '2024.0.0'
        kafkaVersion = '3.8.1'
        postgresqlVersion = '42.7.4'
        redisVersion = '3.4.1'
        mapstructVersion = '1.6.3'
    }
}

// Root project configuration
description = 'Notification Self-Contained System (SCS) for NDVN Platform'

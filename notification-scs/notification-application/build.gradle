plugins {
    id 'java-library'
}

description = 'Notification Application Layer - Use cases and application services'

dependencies {
    api project(':notification-domain')
    api project(':notification-infrastructure')
    
    // Terasoluna Application
    api "org.terasoluna.gfw:terasoluna-gfw-common:${terasolunaVersion}"
    
    // MapStruct for mapping
    api "org.mapstruct:mapstruct:${mapstructVersion}"
    annotationProcessor "org.mapstruct:mapstruct-processor:${mapstructVersion}"
    
    // Validation
    api 'org.springframework.boot:spring-boot-starter-validation'
    
    // Caching
    api 'org.springframework.boot:spring-boot-starter-cache'
    
    // JSON processing
    api 'com.fasterxml.jackson.core:jackson-databind'
    api 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310'
    
    // Testing
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.mockito:mockito-core'
}

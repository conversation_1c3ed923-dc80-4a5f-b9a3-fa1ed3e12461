package com.nttdata.ndvn.notification.events.consumer;

import com.nttdata.ndvn.notification.application.dto.SendNotificationRequest;
import com.nttdata.ndvn.notification.application.service.NotificationApplicationService;
import com.nttdata.ndvn.notification.domain.model.DeliveryChannel;
import com.nttdata.ndvn.notification.domain.model.NotificationPriority;
import com.nttdata.ndvn.notification.domain.model.Recipient;
import com.nttdata.ndvn.notification.domain.model.RecipientType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Set;

/**
 * Consumer for order-related events to trigger notifications.
 */
@Component
public class OrderEventConsumer {
    
    private static final Logger logger = LoggerFactory.getLogger(OrderEventConsumer.class);
    
    private final NotificationApplicationService notificationService;
    
    public OrderEventConsumer(NotificationApplicationService notificationService) {
        this.notificationService = notificationService;
    }
    
    /**
     * Handle order events from order management service.
     */
    @KafkaListener(topics = "order.events", groupId = "notification-service")
    public void handleOrderEvent(@Payload String eventPayload, 
                                @Header(KafkaHeaders.RECEIVED_KEY) String key,
                                @Header Map<String, Object> headers) {
        
        try {
            // Parse event type from headers or payload
            String eventType = (String) headers.getOrDefault("eventType", "unknown");
            String correlationId = (String) headers.getOrDefault("correlationId", key);
            
            MDC.put("correlationId", correlationId);
            
            logger.info("Received order event: {} with correlation: {}", eventType, correlationId);
            
            switch (eventType) {
                case "OrderPlaced" -> handleOrderPlaced(eventPayload, correlationId);
                case "OrderShipped" -> handleOrderShipped(eventPayload, correlationId);
                case "OrderDelivered" -> handleOrderDelivered(eventPayload, correlationId);
                case "OrderCancelled" -> handleOrderCancelled(eventPayload, correlationId);
                default -> logger.warn("Unknown order event type: {}", eventType);
            }
            
        } catch (Exception e) {
            logger.error("Error processing order event with key: {}", key, e);
            throw e; // Re-throw to trigger retry mechanism
        } finally {
            MDC.clear();
        }
    }
    
    /**
     * Handle order placed event.
     */
    private void handleOrderPlaced(String eventPayload, String correlationId) {
        // Parse order details from event payload
        // For now, using mock data - in real implementation, parse JSON payload
        
        SendNotificationRequest request = new SendNotificationRequest();
        request.setNotificationType("ORDER_CONFIRMATION");
        request.setSubject("Order Confirmation - Your order has been placed");
        request.setContent("Thank you for your order! We have received your order and will process it shortly.");
        request.setPriority(NotificationPriority.HIGH);
        request.setCorrelationId(correlationId);
        request.setSourceService("order-management-service");
        
        // Add recipient (in real implementation, extract from event payload)
        Recipient emailRecipient = new Recipient(RecipientType.EMAIL, "<EMAIL>", "Customer Name");
        request.setRecipients(Set.of(emailRecipient));
        request.setChannels(Set.of(DeliveryChannel.EMAIL));
        
        notificationService.sendNotification(request);
        logger.info("Sent order confirmation notification for correlation: {}", correlationId);
    }
    
    /**
     * Handle order shipped event.
     */
    private void handleOrderShipped(String eventPayload, String correlationId) {
        SendNotificationRequest request = new SendNotificationRequest();
        request.setNotificationType("ORDER_SHIPPED");
        request.setSubject("Your order has been shipped");
        request.setContent("Great news! Your order has been shipped and is on its way to you.");
        request.setPriority(NotificationPriority.NORMAL);
        request.setCorrelationId(correlationId);
        request.setSourceService("order-management-service");
        
        // Add recipient (in real implementation, extract from event payload)
        Recipient emailRecipient = new Recipient(RecipientType.EMAIL, "<EMAIL>", "Customer Name");
        request.setRecipients(Set.of(emailRecipient));
        request.setChannels(Set.of(DeliveryChannel.EMAIL, DeliveryChannel.SMS));
        
        notificationService.sendNotification(request);
        logger.info("Sent order shipped notification for correlation: {}", correlationId);
    }
    
    /**
     * Handle order delivered event.
     */
    private void handleOrderDelivered(String eventPayload, String correlationId) {
        SendNotificationRequest request = new SendNotificationRequest();
        request.setNotificationType("ORDER_DELIVERED");
        request.setSubject("Your order has been delivered");
        request.setContent("Your order has been successfully delivered. Thank you for shopping with us!");
        request.setPriority(NotificationPriority.NORMAL);
        request.setCorrelationId(correlationId);
        request.setSourceService("order-management-service");
        
        // Add recipient (in real implementation, extract from event payload)
        Recipient emailRecipient = new Recipient(RecipientType.EMAIL, "<EMAIL>", "Customer Name");
        request.setRecipients(Set.of(emailRecipient));
        request.setChannels(Set.of(DeliveryChannel.EMAIL));
        
        notificationService.sendNotification(request);
        logger.info("Sent order delivered notification for correlation: {}", correlationId);
    }
    
    /**
     * Handle order cancelled event.
     */
    private void handleOrderCancelled(String eventPayload, String correlationId) {
        SendNotificationRequest request = new SendNotificationRequest();
        request.setNotificationType("ORDER_CANCELLED");
        request.setSubject("Your order has been cancelled");
        request.setContent("Your order has been cancelled as requested. If you have any questions, please contact our support team.");
        request.setPriority(NotificationPriority.HIGH);
        request.setCorrelationId(correlationId);
        request.setSourceService("order-management-service");
        
        // Add recipient (in real implementation, extract from event payload)
        Recipient emailRecipient = new Recipient(RecipientType.EMAIL, "<EMAIL>", "Customer Name");
        request.setRecipients(Set.of(emailRecipient));
        request.setChannels(Set.of(DeliveryChannel.EMAIL));
        
        notificationService.sendNotification(request);
        logger.info("Sent order cancelled notification for correlation: {}", correlationId);
    }
}

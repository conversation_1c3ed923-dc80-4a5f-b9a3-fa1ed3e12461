package com.nttdata.ndvn.notification.events.consumer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.nttdata.ndvn.notification.application.dto.SendNotificationRequest;
import com.nttdata.ndvn.notification.application.service.NotificationApplicationService;
import com.nttdata.ndvn.notification.domain.model.DeliveryChannel;
import com.nttdata.ndvn.notification.domain.model.NotificationPriority;
import com.nttdata.ndvn.notification.domain.model.Recipient;
import com.nttdata.ndvn.notification.domain.model.RecipientType;
import com.nttdata.ndvn.shared.events.order.OrderPlacedEvent;
import com.nttdata.ndvn.shared.events.order.OrderShippedEvent;
import com.nttdata.ndvn.shared.events.order.OrderDeliveredEvent;
import com.nttdata.ndvn.shared.events.order.OrderCancelledEvent;
import com.nttdata.ndvn.shared.events.consumer.BaseEventConsumer;
import com.nttdata.ndvn.shared.events.dlq.DeadLetterQueueHandler;
import io.micrometer.core.instrument.MeterRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Standardized event consumer for order-related events using the shared events framework.
 * 
 * This consumer extends BaseEventConsumer to provide standardized event consumption
 * with error handling, retry logic, and idempotency support.
 */
@Component
public class StandardizedOrderEventConsumer extends BaseEventConsumer {
    
    private static final Logger logger = LoggerFactory.getLogger(StandardizedOrderEventConsumer.class);
    private static final String CONSUMER_GROUP = "notification-service";
    
    private final NotificationApplicationService notificationService;
    private final DeadLetterQueueHandler dlqHandler;
    
    // Simple in-memory store for processed event IDs (in production, use Redis or database)
    private final Set<String> processedEventIds = ConcurrentHashMap.newKeySet();
    
    public StandardizedOrderEventConsumer(ObjectMapper objectMapper,
                                        MeterRegistry meterRegistry,
                                        NotificationApplicationService notificationService,
                                        DeadLetterQueueHandler dlqHandler) {
        super(objectMapper, meterRegistry);
        this.notificationService = notificationService;
        this.dlqHandler = dlqHandler;
    }
    
    /**
     * Handles order events from order management service.
     */
    @KafkaListener(topics = "order.events", groupId = CONSUMER_GROUP)
    public void handleOrderEvent(@Payload String eventPayload,
                                @Header Map<String, Object> headers,
                                Acknowledgment acknowledgment) {
        
        // Determine event type from headers
        String eventType = getEventTypeFromHeaders(headers);
        
        switch (eventType) {
            case "OrderPlaced" -> processEvent(eventPayload, headers, acknowledgment, 
                                             OrderPlacedEvent.class, this::handleOrderPlaced);
            case "OrderShipped" -> processEvent(eventPayload, headers, acknowledgment, 
                                              OrderShippedEvent.class, this::handleOrderShipped);
            case "OrderDelivered" -> processEvent(eventPayload, headers, acknowledgment, 
                                                OrderDeliveredEvent.class, this::handleOrderDelivered);
            case "OrderCancelled" -> processEvent(eventPayload, headers, acknowledgment, 
                                                OrderCancelledEvent.class, this::handleOrderCancelled);
            default -> {
                logger.warn("Unknown order event type: {}", eventType);
                acknowledgment.acknowledge(); // Acknowledge unknown events to prevent reprocessing
            }
        }
    }
    
    /**
     * Handles order placed events.
     */
    private void handleOrderPlaced(OrderPlacedEvent event) {
        logger.info("Processing OrderPlacedEvent for order: {} with correlation: {}", 
                   event.getOrderId(), event.getCorrelationId());
        
        SendNotificationRequest request = new SendNotificationRequest();
        request.setNotificationType("ORDER_CONFIRMATION");
        request.setSubject("Order Confirmation - Your order has been placed");
        request.setContent(String.format(
            "Thank you for your order! Order #%s has been placed successfully and will be processed shortly.",
            event.getOrderNumber()
        ));
        request.setPriority(NotificationPriority.HIGH);
        request.setCorrelationId(event.getCorrelationId());
        request.setSourceService("order-management-service");
        
        // Add recipient
        Recipient emailRecipient = new Recipient(RecipientType.EMAIL, event.getCustomerEmail(), event.getCustomerName());
        request.setRecipients(Set.of(emailRecipient));
        request.setChannels(Set.of(DeliveryChannel.EMAIL));
        
        // Add order metadata
        request.setMetadata(Map.of(
            "orderId", event.getOrderId().toString(),
            "orderNumber", event.getOrderNumber(),
            "orderTotal", event.getTotalAmount().toString()
        ));
        
        notificationService.sendNotification(request);
        logger.info("Sent order confirmation notification for order: {}", event.getOrderId());
    }
    
    /**
     * Handles order shipped events.
     */
    private void handleOrderShipped(OrderShippedEvent event) {
        logger.info("Processing OrderShippedEvent for order: {} with correlation: {}", 
                   event.getOrderId(), event.getCorrelationId());
        
        SendNotificationRequest request = new SendNotificationRequest();
        request.setNotificationType("ORDER_SHIPPED");
        request.setSubject("Your order has been shipped");
        request.setContent(String.format(
            "Great news! Your order #%s has been shipped and is on its way to you. Tracking number: %s",
            event.getOrderNumber(), event.getTrackingNumber()
        ));
        request.setPriority(NotificationPriority.NORMAL);
        request.setCorrelationId(event.getCorrelationId());
        request.setSourceService("order-management-service");
        
        // Add recipient
        Recipient emailRecipient = new Recipient(RecipientType.EMAIL, event.getCustomerEmail(), event.getCustomerName());
        request.setRecipients(Set.of(emailRecipient));
        request.setChannels(Set.of(DeliveryChannel.EMAIL, DeliveryChannel.SMS));
        
        // Add shipping metadata
        request.setMetadata(Map.of(
            "orderId", event.getOrderId().toString(),
            "orderNumber", event.getOrderNumber(),
            "trackingNumber", event.getTrackingNumber(),
            "carrier", event.getCarrier()
        ));
        
        notificationService.sendNotification(request);
        logger.info("Sent order shipped notification for order: {}", event.getOrderId());
    }
    
    /**
     * Handles order delivered events.
     */
    private void handleOrderDelivered(OrderDeliveredEvent event) {
        logger.info("Processing OrderDeliveredEvent for order: {} with correlation: {}", 
                   event.getOrderId(), event.getCorrelationId());
        
        SendNotificationRequest request = new SendNotificationRequest();
        request.setNotificationType("ORDER_DELIVERED");
        request.setSubject("Your order has been delivered");
        request.setContent(String.format(
            "Your order #%s has been successfully delivered. Thank you for shopping with us!",
            event.getOrderNumber()
        ));
        request.setPriority(NotificationPriority.NORMAL);
        request.setCorrelationId(event.getCorrelationId());
        request.setSourceService("order-management-service");
        
        // Add recipient
        Recipient emailRecipient = new Recipient(RecipientType.EMAIL, event.getCustomerEmail(), event.getCustomerName());
        request.setRecipients(Set.of(emailRecipient));
        request.setChannels(Set.of(DeliveryChannel.EMAIL));
        
        // Add delivery metadata
        request.setMetadata(Map.of(
            "orderId", event.getOrderId().toString(),
            "orderNumber", event.getOrderNumber(),
            "deliveredAt", event.getDeliveredAt().toString()
        ));
        
        notificationService.sendNotification(request);
        logger.info("Sent order delivered notification for order: {}", event.getOrderId());
    }
    
    /**
     * Handles order cancelled events.
     */
    private void handleOrderCancelled(OrderCancelledEvent event) {
        logger.info("Processing OrderCancelledEvent for order: {} with correlation: {}", 
                   event.getOrderId(), event.getCorrelationId());
        
        SendNotificationRequest request = new SendNotificationRequest();
        request.setNotificationType("ORDER_CANCELLED");
        request.setSubject("Your order has been cancelled");
        request.setContent(String.format(
            "Your order #%s has been cancelled as requested. Reason: %s. If you have any questions, please contact our support team.",
            event.getOrderNumber(), event.getCancellationReason()
        ));
        request.setPriority(NotificationPriority.HIGH);
        request.setCorrelationId(event.getCorrelationId());
        request.setSourceService("order-management-service");
        
        // Add recipient
        Recipient emailRecipient = new Recipient(RecipientType.EMAIL, event.getCustomerEmail(), event.getCustomerName());
        request.setRecipients(Set.of(emailRecipient));
        request.setChannels(Set.of(DeliveryChannel.EMAIL));
        
        // Add cancellation metadata
        request.setMetadata(Map.of(
            "orderId", event.getOrderId().toString(),
            "orderNumber", event.getOrderNumber(),
            "cancellationReason", event.getCancellationReason(),
            "cancelledAt", event.getCancelledAt().toString()
        ));
        
        notificationService.sendNotification(request);
        logger.info("Sent order cancelled notification for order: {}", event.getOrderId());
    }
    
    /**
     * Gets event type from Kafka headers.
     */
    private String getEventTypeFromHeaders(Map<String, Object> headers) {
        Object eventType = headers.get("eventType");
        if (eventType != null) {
            return eventType.toString();
        }
        
        // Fallback to parsing from payload if needed
        return "UNKNOWN";
    }
    
    @Override
    protected String getConsumerGroup() {
        return CONSUMER_GROUP;
    }
    
    @Override
    protected boolean isDuplicateEvent(com.nttdata.ndvn.shared.events.BaseEvent event) {
        return processedEventIds.contains(event.getEventId().toString());
    }
    
    @Override
    protected void markEventAsProcessed(com.nttdata.ndvn.shared.events.BaseEvent event) {
        processedEventIds.add(event.getEventId().toString());
        
        // In production, you might want to clean up old processed event IDs periodically
        // or use a more sophisticated storage mechanism like Redis with TTL
    }
    
    @Override
    protected void sendToDeadLetterQueue(String eventPayload, String topic, String errorType, String errorMessage) {
        dlqHandler.sendToDeadLetterQueue(topic, eventPayload, errorType, errorMessage)
            .whenComplete((result, ex) -> {
                if (ex != null) {
                    logger.error("Failed to send event to DLQ for topic: {}", topic, ex);
                } else {
                    logger.info("Successfully sent event to DLQ for topic: {}", topic);
                }
            });
    }
}

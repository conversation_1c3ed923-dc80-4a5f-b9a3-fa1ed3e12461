package com.nttdata.ndvn.notification.events.events;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Event published when a notification delivery fails.
 */
public class NotificationFailedEvent {
    
    private UUID eventId;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime eventTime;
    
    private UUID notificationId;
    private String notificationType;
    private String correlationId;
    private String sourceService;
    private String failureReason;
    private String[] channels;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime failedAt;
    
    // Constructors
    public NotificationFailedEvent() {}
    
    public NotificationFailedEvent(UUID notificationId, String notificationType, String correlationId, 
                                  String sourceService, String failureReason, String[] channels, LocalDateTime failedAt) {
        this.eventId = UUID.randomUUID();
        this.eventTime = LocalDateTime.now();
        this.notificationId = notificationId;
        this.notificationType = notificationType;
        this.correlationId = correlationId;
        this.sourceService = sourceService;
        this.failureReason = failureReason;
        this.channels = channels;
        this.failedAt = failedAt;
    }
    
    // Getters and setters
    public UUID getEventId() { return eventId; }
    public void setEventId(UUID eventId) { this.eventId = eventId; }
    
    public LocalDateTime getEventTime() { return eventTime; }
    public void setEventTime(LocalDateTime eventTime) { this.eventTime = eventTime; }
    
    public UUID getNotificationId() { return notificationId; }
    public void setNotificationId(UUID notificationId) { this.notificationId = notificationId; }
    
    public String getNotificationType() { return notificationType; }
    public void setNotificationType(String notificationType) { this.notificationType = notificationType; }
    
    public String getCorrelationId() { return correlationId; }
    public void setCorrelationId(String correlationId) { this.correlationId = correlationId; }
    
    public String getSourceService() { return sourceService; }
    public void setSourceService(String sourceService) { this.sourceService = sourceService; }
    
    public String getFailureReason() { return failureReason; }
    public void setFailureReason(String failureReason) { this.failureReason = failureReason; }
    
    public String[] getChannels() { return channels; }
    public void setChannels(String[] channels) { this.channels = channels; }
    
    public LocalDateTime getFailedAt() { return failedAt; }
    public void setFailedAt(LocalDateTime failedAt) { this.failedAt = failedAt; }
}

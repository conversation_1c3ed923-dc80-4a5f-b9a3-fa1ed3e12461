package com.nttdata.ndvn.notification.events.events;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Event published when a notification is successfully sent.
 */
public class NotificationSentEvent {
    
    private UUID eventId;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime eventTime;
    
    private UUID notificationId;
    private String notificationType;
    private String correlationId;
    private String sourceService;
    private int recipientCount;
    private String[] channels;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime sentAt;
    
    // Constructors
    public NotificationSentEvent() {}
    
    public NotificationSentEvent(UUID notificationId, String notificationType, String correlationId, 
                                String sourceService, int recipientCount, String[] channels, LocalDateTime sentAt) {
        this.eventId = UUID.randomUUID();
        this.eventTime = LocalDateTime.now();
        this.notificationId = notificationId;
        this.notificationType = notificationType;
        this.correlationId = correlationId;
        this.sourceService = sourceService;
        this.recipientCount = recipientCount;
        this.channels = channels;
        this.sentAt = sentAt;
    }
    
    // Getters and setters
    public UUID getEventId() { return eventId; }
    public void setEventId(UUID eventId) { this.eventId = eventId; }
    
    public LocalDateTime getEventTime() { return eventTime; }
    public void setEventTime(LocalDateTime eventTime) { this.eventTime = eventTime; }
    
    public UUID getNotificationId() { return notificationId; }
    public void setNotificationId(UUID notificationId) { this.notificationId = notificationId; }
    
    public String getNotificationType() { return notificationType; }
    public void setNotificationType(String notificationType) { this.notificationType = notificationType; }
    
    public String getCorrelationId() { return correlationId; }
    public void setCorrelationId(String correlationId) { this.correlationId = correlationId; }
    
    public String getSourceService() { return sourceService; }
    public void setSourceService(String sourceService) { this.sourceService = sourceService; }
    
    public int getRecipientCount() { return recipientCount; }
    public void setRecipientCount(int recipientCount) { this.recipientCount = recipientCount; }
    
    public String[] getChannels() { return channels; }
    public void setChannels(String[] channels) { this.channels = channels; }
    
    public LocalDateTime getSentAt() { return sentAt; }
    public void setSentAt(LocalDateTime sentAt) { this.sentAt = sentAt; }
}

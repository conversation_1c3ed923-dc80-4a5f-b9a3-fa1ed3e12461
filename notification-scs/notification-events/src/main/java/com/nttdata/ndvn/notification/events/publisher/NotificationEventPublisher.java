package com.nttdata.ndvn.notification.events.publisher;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nttdata.ndvn.notification.events.events.NotificationFailedEvent;
import com.nttdata.ndvn.notification.events.events.NotificationSentEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

/**
 * Publisher for notification domain events.
 */
@Component
public class NotificationEventPublisher {
    
    private static final Logger logger = LoggerFactory.getLogger(NotificationEventPublisher.class);
    private static final String NOTIFICATION_EVENTS_TOPIC = "notification.events";
    
    private final KafkaTemplate<String, String> kafkaTemplate;
    private final ObjectMapper objectMapper;
    
    public NotificationEventPublisher(KafkaTemplate<String, String> kafkaTemplate, ObjectMapper objectMapper) {
        this.kafkaTemplate = kafkaTemplate;
        this.objectMapper = objectMapper;
    }
    
    /**
     * Publish notification sent event.
     */
    public void publishNotificationSent(NotificationSentEvent event) {
        try {
            String eventJson = objectMapper.writeValueAsString(event);
            String key = event.getNotificationId().toString();
            
            kafkaTemplate.send(NOTIFICATION_EVENTS_TOPIC, key, eventJson)
                    .whenComplete((result, ex) -> {
                        if (ex == null) {
                            logger.info("Published NotificationSentEvent for notification: {}", event.getNotificationId());
                        } else {
                            logger.error("Failed to publish NotificationSentEvent for notification: {}", 
                                       event.getNotificationId(), ex);
                        }
                    });
                    
        } catch (JsonProcessingException e) {
            logger.error("Failed to serialize NotificationSentEvent for notification: {}", 
                        event.getNotificationId(), e);
        }
    }
    
    /**
     * Publish notification failed event.
     */
    public void publishNotificationFailed(NotificationFailedEvent event) {
        try {
            String eventJson = objectMapper.writeValueAsString(event);
            String key = event.getNotificationId().toString();
            
            kafkaTemplate.send(NOTIFICATION_EVENTS_TOPIC, key, eventJson)
                    .whenComplete((result, ex) -> {
                        if (ex == null) {
                            logger.info("Published NotificationFailedEvent for notification: {}", event.getNotificationId());
                        } else {
                            logger.error("Failed to publish NotificationFailedEvent for notification: {}", 
                                       event.getNotificationId(), ex);
                        }
                    });
                    
        } catch (JsonProcessingException e) {
            logger.error("Failed to serialize NotificationFailedEvent for notification: {}", 
                        event.getNotificationId(), e);
        }
    }
}

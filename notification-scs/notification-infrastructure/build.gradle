plugins {
    id 'java-library'
}

description = 'Notification Infrastructure Layer - Data access and external integrations'

dependencies {
    api project(':notification-domain')
    
    // Terasoluna Infrastructure (using standard Spring Data JPA for now)
    // api "org.terasoluna.gfw:terasoluna-gfw-jpa:${terasolunaVersion}"
    
    // Database
    api 'org.springframework.boot:spring-boot-starter-data-jpa'
    api "org.postgresql:postgresql:${postgresqlVersion}"
    
    // Redis
    api 'org.springframework.boot:spring-boot-starter-data-redis'
    api 'org.springframework.boot:spring-boot-starter-cache'
    
    // Email
    api 'org.springframework.boot:spring-boot-starter-mail'
    
    // HTTP Client
    api 'org.springframework.boot:spring-boot-starter-webflux'
    
    // JSON processing
    api 'com.fasterxml.jackson.core:jackson-databind'
    api 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310'
    
    // Resilience
    api 'io.github.resilience4j:resilience4j-spring-boot3'
    api 'io.github.resilience4j:resilience4j-reactor'
    
    // Testing
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.testcontainers:postgresql'
    testImplementation 'org.testcontainers:junit-jupiter'
    testImplementation 'com.h2database:h2'
}

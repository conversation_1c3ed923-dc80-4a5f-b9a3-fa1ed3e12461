package com.nttdata.ndvn.notification.infrastructure.external;

import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import io.github.resilience4j.retry.annotation.Retry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import java.util.concurrent.CompletableFuture;

/**
 * Email delivery service implementation.
 */
@Service
public class EmailService {
    
    private static final Logger logger = LoggerFactory.getLogger(EmailService.class);
    
    private final JavaMailSender mailSender;
    
    public EmailService(JavaMailSender mailSender) {
        this.mailSender = mailSender;
    }
    
    /**
     * Send simple text email.
     */
    @CircuitBreaker(name = "email-service", fallbackMethod = "sendEmailFallback")
    @Retry(name = "email-service")
    public CompletableFuture<String> sendEmail(String to, String subject, String content) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                SimpleMailMessage message = new SimpleMailMessage();
                message.setTo(to);
                message.setSubject(subject);
                message.setText(content);
                
                mailSender.send(message);
                
                String messageId = generateMessageId();
                logger.info("Email sent successfully to: {} with message ID: {}", to, messageId);
                return messageId;
                
            } catch (Exception e) {
                logger.error("Failed to send email to: {}", to, e);
                throw new RuntimeException("Email delivery failed", e);
            }
        });
    }
    
    /**
     * Send HTML email.
     */
    @CircuitBreaker(name = "email-service", fallbackMethod = "sendHtmlEmailFallback")
    @Retry(name = "email-service")
    public CompletableFuture<String> sendHtmlEmail(String to, String subject, String htmlContent) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                MimeMessage message = mailSender.createMimeMessage();
                MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
                
                helper.setTo(to);
                helper.setSubject(subject);
                helper.setText(htmlContent, true);
                
                mailSender.send(message);
                
                String messageId = generateMessageId();
                logger.info("HTML email sent successfully to: {} with message ID: {}", to, messageId);
                return messageId;
                
            } catch (MessagingException e) {
                logger.error("Failed to send HTML email to: {}", to, e);
                throw new RuntimeException("HTML email delivery failed", e);
            }
        });
    }
    
    /**
     * Fallback method for email sending.
     */
    public CompletableFuture<String> sendEmailFallback(String to, String subject, String content, Exception ex) {
        logger.warn("Email service fallback triggered for: {} - {}", to, ex.getMessage());
        return CompletableFuture.failedFuture(new RuntimeException("Email service unavailable", ex));
    }
    
    /**
     * Fallback method for HTML email sending.
     */
    public CompletableFuture<String> sendHtmlEmailFallback(String to, String subject, String htmlContent, Exception ex) {
        logger.warn("HTML email service fallback triggered for: {} - {}", to, ex.getMessage());
        return CompletableFuture.failedFuture(new RuntimeException("HTML email service unavailable", ex));
    }
    
    /**
     * Generate a unique message ID for tracking.
     */
    private String generateMessageId() {
        return "email-" + System.currentTimeMillis() + "-" + Thread.currentThread().getId();
    }
}

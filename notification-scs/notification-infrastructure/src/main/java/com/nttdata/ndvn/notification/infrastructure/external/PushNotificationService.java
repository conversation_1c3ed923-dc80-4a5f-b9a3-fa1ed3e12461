package com.nttdata.ndvn.notification.infrastructure.external;

import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import io.github.resilience4j.retry.annotation.Retry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Push notification service implementation.
 * This is a mock implementation that can be replaced with actual push notification provider integration
 * (e.g., Firebase Cloud Messaging, Apple Push Notification Service).
 */
@Service
public class PushNotificationService {
    
    private static final Logger logger = LoggerFactory.getLogger(PushNotificationService.class);
    
    private final WebClient webClient;
    
    @Value("${app.push.provider.url:http://localhost:8091/push}")
    private String pushProviderUrl;
    
    @Value("${app.push.provider.api-key:mock-push-api-key}")
    private String apiKey;
    
    public PushNotificationService(WebClient.Builder webClientBuilder) {
        this.webClient = webClientBuilder.build();
    }
    
    /**
     * Send push notification.
     */
    @CircuitBreaker(name = "push-service", fallbackMethod = "sendPushNotificationFallback")
    @Retry(name = "push-service")
    public CompletableFuture<String> sendPushNotification(String deviceToken, String title, String body, Map<String, String> data) {
        return webClient.post()
                .uri(pushProviderUrl + "/send")
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + apiKey)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(createPushRequest(deviceToken, title, body, data))
                .retrieve()
                .bodyToMono(PushResponse.class)
                .map(response -> {
                    logger.info("Push notification sent successfully to device: {} with message ID: {}", 
                              maskDeviceToken(deviceToken), response.getMessageId());
                    return response.getMessageId();
                })
                .onErrorMap(throwable -> {
                    logger.error("Failed to send push notification to device: {}", maskDeviceToken(deviceToken), throwable);
                    return new RuntimeException("Push notification delivery failed", throwable);
                })
                .toFuture();
    }
    
    /**
     * Send push notification to multiple devices.
     */
    @CircuitBreaker(name = "push-service", fallbackMethod = "sendMulticastPushNotificationFallback")
    @Retry(name = "push-service")
    public CompletableFuture<String> sendMulticastPushNotification(String[] deviceTokens, String title, String body, Map<String, String> data) {
        return webClient.post()
                .uri(pushProviderUrl + "/multicast")
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + apiKey)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(createMulticastPushRequest(deviceTokens, title, body, data))
                .retrieve()
                .bodyToMono(PushResponse.class)
                .map(response -> {
                    logger.info("Multicast push notification sent to {} devices with message ID: {}", 
                              deviceTokens.length, response.getMessageId());
                    return response.getMessageId();
                })
                .onErrorMap(throwable -> {
                    logger.error("Failed to send multicast push notification to {} devices", deviceTokens.length, throwable);
                    return new RuntimeException("Multicast push notification delivery failed", throwable);
                })
                .toFuture();
    }
    
    /**
     * Check push notification delivery status.
     */
    @CircuitBreaker(name = "push-service", fallbackMethod = "checkPushStatusFallback")
    @Retry(name = "push-service")
    public CompletableFuture<String> checkPushStatus(String messageId) {
        return webClient.get()
                .uri(pushProviderUrl + "/status/{messageId}", messageId)
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + apiKey)
                .retrieve()
                .bodyToMono(PushStatusResponse.class)
                .map(response -> {
                    logger.debug("Push notification status for message {}: {}", messageId, response.getStatus());
                    return response.getStatus();
                })
                .onErrorMap(throwable -> {
                    logger.error("Failed to check push notification status for message: {}", messageId, throwable);
                    return new RuntimeException("Push notification status check failed", throwable);
                })
                .toFuture();
    }
    
    /**
     * Fallback method for push notification sending.
     */
    public CompletableFuture<String> sendPushNotificationFallback(String deviceToken, String title, String body, Map<String, String> data, Exception ex) {
        logger.warn("Push notification service fallback triggered for device: {} - {}", maskDeviceToken(deviceToken), ex.getMessage());
        return CompletableFuture.failedFuture(new RuntimeException("Push notification service unavailable", ex));
    }
    
    /**
     * Fallback method for multicast push notification sending.
     */
    public CompletableFuture<String> sendMulticastPushNotificationFallback(String[] deviceTokens, String title, String body, Map<String, String> data, Exception ex) {
        logger.warn("Multicast push notification service fallback triggered for {} devices - {}", deviceTokens.length, ex.getMessage());
        return CompletableFuture.failedFuture(new RuntimeException("Multicast push notification service unavailable", ex));
    }
    
    /**
     * Fallback method for push notification status checking.
     */
    public CompletableFuture<String> checkPushStatusFallback(String messageId, Exception ex) {
        logger.warn("Push notification status check fallback triggered for: {} - {}", messageId, ex.getMessage());
        return CompletableFuture.completedFuture("UNKNOWN");
    }
    
    /**
     * Create push notification request payload.
     */
    private Map<String, Object> createPushRequest(String deviceToken, String title, String body, Map<String, String> data) {
        Map<String, Object> request = new HashMap<>();
        request.put("to", deviceToken);
        
        Map<String, String> notification = new HashMap<>();
        notification.put("title", title);
        notification.put("body", body);
        request.put("notification", notification);
        
        if (data != null && !data.isEmpty()) {
            request.put("data", data);
        }
        
        return request;
    }
    
    /**
     * Create multicast push notification request payload.
     */
    private Map<String, Object> createMulticastPushRequest(String[] deviceTokens, String title, String body, Map<String, String> data) {
        Map<String, Object> request = new HashMap<>();
        request.put("registration_ids", deviceTokens);
        
        Map<String, String> notification = new HashMap<>();
        notification.put("title", title);
        notification.put("body", body);
        request.put("notification", notification);
        
        if (data != null && !data.isEmpty()) {
            request.put("data", data);
        }
        
        return request;
    }
    
    /**
     * Mask device token for logging security.
     */
    private String maskDeviceToken(String deviceToken) {
        if (deviceToken == null || deviceToken.length() < 8) {
            return "***";
        }
        return deviceToken.substring(0, 4) + "***" + deviceToken.substring(deviceToken.length() - 4);
    }
    
    /**
     * Push notification response DTO.
     */
    public static class PushResponse {
        private String messageId;
        private String status;
        private int successCount;
        private int failureCount;
        
        public String getMessageId() { return messageId; }
        public void setMessageId(String messageId) { this.messageId = messageId; }
        
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        
        public int getSuccessCount() { return successCount; }
        public void setSuccessCount(int successCount) { this.successCount = successCount; }
        
        public int getFailureCount() { return failureCount; }
        public void setFailureCount(int failureCount) { this.failureCount = failureCount; }
    }
    
    /**
     * Push notification status response DTO.
     */
    public static class PushStatusResponse {
        private String messageId;
        private String status;
        private String deliveredAt;
        
        public String getMessageId() { return messageId; }
        public void setMessageId(String messageId) { this.messageId = messageId; }
        
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        
        public String getDeliveredAt() { return deliveredAt; }
        public void setDeliveredAt(String deliveredAt) { this.deliveredAt = deliveredAt; }
    }
}

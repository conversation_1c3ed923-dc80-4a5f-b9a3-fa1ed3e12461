package com.nttdata.ndvn.notification.infrastructure.external;

import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import io.github.resilience4j.retry.annotation.Retry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * SMS delivery service implementation.
 * This is a mock implementation that can be replaced with actual SMS provider integration.
 */
@Service
public class SmsService {
    
    private static final Logger logger = LoggerFactory.getLogger(SmsService.class);
    
    private final WebClient webClient;
    
    @Value("${app.sms.provider.url:http://localhost:8090/sms}")
    private String smsProviderUrl;
    
    @Value("${app.sms.provider.api-key:mock-api-key}")
    private String apiKey;
    
    public SmsService(WebClient.Builder webClientBuilder) {
        this.webClient = webClientBuilder.build();
    }
    
    /**
     * Send SMS message.
     */
    @CircuitBreaker(name = "sms-service", fallbackMethod = "sendSmsFallback")
    @Retry(name = "sms-service")
    public CompletableFuture<String> sendSms(String phoneNumber, String message) {
        return webClient.post()
                .uri(smsProviderUrl + "/send")
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + apiKey)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(createSmsRequest(phoneNumber, message))
                .retrieve()
                .bodyToMono(SmsResponse.class)
                .map(response -> {
                    logger.info("SMS sent successfully to: {} with message ID: {}", phoneNumber, response.getMessageId());
                    return response.getMessageId();
                })
                .onErrorMap(throwable -> {
                    logger.error("Failed to send SMS to: {}", phoneNumber, throwable);
                    return new RuntimeException("SMS delivery failed", throwable);
                })
                .toFuture();
    }
    
    /**
     * Check SMS delivery status.
     */
    @CircuitBreaker(name = "sms-service", fallbackMethod = "checkSmsStatusFallback")
    @Retry(name = "sms-service")
    public CompletableFuture<String> checkSmsStatus(String messageId) {
        return webClient.get()
                .uri(smsProviderUrl + "/status/{messageId}", messageId)
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + apiKey)
                .retrieve()
                .bodyToMono(SmsStatusResponse.class)
                .map(response -> {
                    logger.debug("SMS status for message {}: {}", messageId, response.getStatus());
                    return response.getStatus();
                })
                .onErrorMap(throwable -> {
                    logger.error("Failed to check SMS status for message: {}", messageId, throwable);
                    return new RuntimeException("SMS status check failed", throwable);
                })
                .toFuture();
    }
    
    /**
     * Fallback method for SMS sending.
     */
    public CompletableFuture<String> sendSmsFallback(String phoneNumber, String message, Exception ex) {
        logger.warn("SMS service fallback triggered for: {} - {}", phoneNumber, ex.getMessage());
        return CompletableFuture.failedFuture(new RuntimeException("SMS service unavailable", ex));
    }
    
    /**
     * Fallback method for SMS status checking.
     */
    public CompletableFuture<String> checkSmsStatusFallback(String messageId, Exception ex) {
        logger.warn("SMS status check fallback triggered for: {} - {}", messageId, ex.getMessage());
        return CompletableFuture.completedFuture("UNKNOWN");
    }
    
    /**
     * Create SMS request payload.
     */
    private Map<String, Object> createSmsRequest(String phoneNumber, String message) {
        Map<String, Object> request = new HashMap<>();
        request.put("to", phoneNumber);
        request.put("message", message);
        request.put("from", "NDVN-SCS");
        return request;
    }
    
    /**
     * SMS response DTO.
     */
    public static class SmsResponse {
        private String messageId;
        private String status;
        
        public String getMessageId() { return messageId; }
        public void setMessageId(String messageId) { this.messageId = messageId; }
        
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
    }
    
    /**
     * SMS status response DTO.
     */
    public static class SmsStatusResponse {
        private String messageId;
        private String status;
        private String deliveredAt;
        
        public String getMessageId() { return messageId; }
        public void setMessageId(String messageId) { this.messageId = messageId; }
        
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        
        public String getDeliveredAt() { return deliveredAt; }
        public void setDeliveredAt(String deliveredAt) { this.deliveredAt = deliveredAt; }
    }
}

package com.nttdata.ndvn.notification.infrastructure.repository;

import com.nttdata.ndvn.notification.domain.model.Notification;
import com.nttdata.ndvn.notification.domain.model.NotificationStatus;
import com.nttdata.ndvn.notification.domain.repository.NotificationRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Implementation of NotificationRepository using JPA.
 */
@Repository
public class NotificationRepositoryImpl implements NotificationRepository {
    
    private final JpaNotificationRepository jpaRepository;
    
    public NotificationRepositoryImpl(JpaNotificationRepository jpaRepository) {
        this.jpaRepository = jpaRepository;
    }
    
    @Override
    public Notification save(Notification notification) {
        return jpaRepository.save(notification);
    }
    
    @Override
    public Optional<Notification> findById(UUID id) {
        return jpaRepository.findById(id);
    }
    
    @Override
    public Page<Notification> findAll(Pageable pageable) {
        return jpaRepository.findAll(pageable);
    }
    
    @Override
    public List<Notification> findByStatus(NotificationStatus status) {
        return jpaRepository.findByStatus(status);
    }
    
    @Override
    public List<Notification> findByCorrelationId(String correlationId) {
        return jpaRepository.findByCorrelationId(correlationId);
    }
    
    @Override
    public Page<Notification> findBySourceService(String sourceService, Pageable pageable) {
        return jpaRepository.findBySourceService(sourceService, pageable);
    }
    
    @Override
    public List<Notification> findReadyToSend() {
        return jpaRepository.findReadyToSend(LocalDateTime.now());
    }
    
    @Override
    public List<Notification> findScheduledNotificationsDue(LocalDateTime currentTime) {
        return jpaRepository.findScheduledNotificationsDue(currentTime);
    }
    
    @Override
    public Page<Notification> findByStatusAndCreatedAtBetween(NotificationStatus status, 
                                                             LocalDateTime startDate, 
                                                             LocalDateTime endDate, 
                                                             Pageable pageable) {
        return jpaRepository.findByStatusAndCreatedAtBetween(status, startDate, endDate, pageable);
    }
    
    @Override
    public long countByStatus(NotificationStatus status) {
        return jpaRepository.countByStatus(status);
    }
    
    @Override
    public void deleteById(UUID id) {
        jpaRepository.deleteById(id);
    }
    
    @Override
    public boolean existsById(UUID id) {
        return jpaRepository.existsById(id);
    }
}

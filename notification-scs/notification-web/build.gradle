plugins {
    id 'org.springframework.boot'
    id 'io.spring.dependency-management'
    id 'java'
}

description = 'Notification Web Layer - REST API and Spring Boot configuration'

dependencies {
    implementation project(':notification-domain')
    implementation project(':notification-infrastructure')
    implementation project(':notification-application')
    implementation project(':notification-events')

    // Web
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    
    // Terasoluna Web
    implementation 'org.terasoluna.gfw:terasoluna-gfw-web'
    implementation 'org.terasoluna.gfw:terasoluna-gfw-security-web'
    
    // Service Discovery
    implementation 'org.springframework.cloud:spring-cloud-starter-consul-discovery'
    
    // Documentation
    implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.7.0'
    
    // Database
    runtimeOnly "org.postgresql:postgresql:${postgresqlVersion}"
    
    // Testing
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.security:spring-security-test'
    testImplementation 'org.testcontainers:postgresql'
    testImplementation 'org.testcontainers:kafka'
    testImplementation 'com.h2database:h2'
}

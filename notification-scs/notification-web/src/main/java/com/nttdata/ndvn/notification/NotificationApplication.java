package com.nttdata.ndvn.notification;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Main application class for Notification SCS.
 * 
 * This Self-Contained System (SCS) provides comprehensive notification
 * management capabilities including multi-channel delivery (email, SMS, push),
 * template management, delivery tracking, and event-driven communication.
 * 
 * Key Features:
 * - Multi-channel notification delivery (email, SMS, push, in-app)
 * - Notification template management and versioning
 * - Delivery tracking and analytics
 * - Event-driven architecture with Kafka integration
 * - Scheduled notification support
 * - Resilient delivery with circuit breakers and retries
 * - RESTful APIs with OpenAPI documentation
 * 
 * Architecture:
 * - Domain-Driven Design (DDD) with clean architecture
 * - Multi-module structure (domain, infrastructure, application, events, web)
 * - Terasoluna framework patterns
 * - Event sourcing for domain events
 * - Asynchronous processing for delivery
 */
@SpringBootApplication(scanBasePackages = "com.nttdata.ndvn.notification")
@EnableJpaRepositories(basePackages = "com.nttdata.ndvn.notification.infrastructure.repository")
@EnableJpaAuditing
@EnableTransactionManagement
@EnableCaching
@EnableKafka
@EnableAsync
@EnableScheduling
@EnableDiscoveryClient
public class NotificationApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(NotificationApplication.class, args);
    }
}

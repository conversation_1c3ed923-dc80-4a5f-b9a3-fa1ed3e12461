package com.nttdata.ndvn.notification.web.controller;

import com.nttdata.ndvn.notification.application.dto.NotificationDto;
import com.nttdata.ndvn.notification.application.dto.SendNotificationRequest;
import com.nttdata.ndvn.notification.application.service.NotificationApplicationService;
import com.nttdata.ndvn.notification.domain.model.NotificationStatus;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * REST controller for notification operations.
 */
@RestController
@RequestMapping("/api/v1/notifications")
@Tag(name = "Notifications", description = "Notification management operations")
public class NotificationController {
    
    private static final Logger logger = LoggerFactory.getLogger(NotificationController.class);
    
    private final NotificationApplicationService notificationService;
    
    public NotificationController(NotificationApplicationService notificationService) {
        this.notificationService = notificationService;
    }
    
    /**
     * Send notification immediately.
     */
    @PostMapping("/send")
    @Operation(summary = "Send notification", description = "Send a notification immediately through specified channels")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Notification sent successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PreAuthorize("hasRole('ADMIN') or hasRole('SERVICE')")
    public ResponseEntity<NotificationDto> sendNotification(
            @Valid @RequestBody SendNotificationRequest request) {
        
        logger.info("Sending notification: {} for correlation: {}", 
                   request.getNotificationType(), request.getCorrelationId());
        
        NotificationDto notification = notificationService.sendNotification(request);
        return ResponseEntity.status(HttpStatus.CREATED).body(notification);
    }
    
    /**
     * Schedule notification for later delivery.
     */
    @PostMapping("/schedule")
    @Operation(summary = "Schedule notification", description = "Schedule a notification for later delivery")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Notification scheduled successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PreAuthorize("hasRole('ADMIN') or hasRole('SERVICE')")
    public ResponseEntity<NotificationDto> scheduleNotification(
            @Valid @RequestBody SendNotificationRequest request) {
        
        logger.info("Scheduling notification: {} for {} with correlation: {}", 
                   request.getNotificationType(), request.getScheduledAt(), request.getCorrelationId());
        
        NotificationDto notification = notificationService.scheduleNotification(request);
        return ResponseEntity.status(HttpStatus.CREATED).body(notification);
    }
    
    /**
     * Get notification by ID.
     */
    @GetMapping("/{id}")
    @Operation(summary = "Get notification", description = "Get notification details by ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Notification found"),
        @ApiResponse(responseCode = "404", description = "Notification not found"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @PreAuthorize("hasRole('ADMIN') or hasRole('SERVICE') or hasRole('USER')")
    public ResponseEntity<NotificationDto> getNotification(
            @Parameter(description = "Notification ID") @PathVariable UUID id) {
        
        Optional<NotificationDto> notification = notificationService.getNotification(id);
        return notification.map(ResponseEntity::ok)
                          .orElse(ResponseEntity.notFound().build());
    }
    
    /**
     * Get all notifications with pagination.
     */
    @GetMapping
    @Operation(summary = "Get all notifications", description = "Get all notifications with pagination")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Notifications retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Page<NotificationDto>> getAllNotifications(
            @PageableDefault(size = 20) Pageable pageable) {
        
        Page<NotificationDto> notifications = notificationService.getAllNotifications(pageable);
        return ResponseEntity.ok(notifications);
    }
    
    /**
     * Get notifications by status.
     */
    @GetMapping("/status/{status}")
    @Operation(summary = "Get notifications by status", description = "Get notifications filtered by status")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Notifications retrieved successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid status"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @PreAuthorize("hasRole('ADMIN') or hasRole('SERVICE')")
    public ResponseEntity<List<NotificationDto>> getNotificationsByStatus(
            @Parameter(description = "Notification status") @PathVariable NotificationStatus status) {
        
        List<NotificationDto> notifications = notificationService.getNotificationsByStatus(status);
        return ResponseEntity.ok(notifications);
    }
    
    /**
     * Get notifications by correlation ID.
     */
    @GetMapping("/correlation/{correlationId}")
    @Operation(summary = "Get notifications by correlation ID", description = "Get notifications filtered by correlation ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Notifications retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @PreAuthorize("hasRole('ADMIN') or hasRole('SERVICE')")
    public ResponseEntity<List<NotificationDto>> getNotificationsByCorrelationId(
            @Parameter(description = "Correlation ID") @PathVariable String correlationId) {
        
        List<NotificationDto> notifications = notificationService.getNotificationsByCorrelationId(correlationId);
        return ResponseEntity.ok(notifications);
    }
    
    /**
     * Delete notification.
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "Delete notification", description = "Delete a notification by ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Notification deleted successfully"),
        @ApiResponse(responseCode = "404", description = "Notification not found"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Forbidden")
    })
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteNotification(
            @Parameter(description = "Notification ID") @PathVariable UUID id) {
        
        notificationService.deleteNotification(id);
        return ResponseEntity.noContent().build();
    }
    
    /**
     * Process scheduled notifications manually.
     */
    @PostMapping("/process-scheduled")
    @Operation(summary = "Process scheduled notifications", description = "Manually trigger processing of scheduled notifications")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Scheduled notifications processed"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Forbidden")
    })
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<String> processScheduledNotifications() {
        
        notificationService.processScheduledNotifications();
        return ResponseEntity.ok("Scheduled notifications processing initiated");
    }
}

server:
  port: 8084

spring:
  application:
    name: notification-service
  
  profiles:
    active: local
  
  # Database Configuration
  datasource:
    url: ************************************************
    username: ${DB_USERNAME:notification_user}
    password: ${DB_PASSWORD:notification_pass}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      leak-detection-threshold: 60000
  
  # JPA Configuration
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
  
  # Redis Configuration
  data:
    redis:
      host: localhost
      port: 6379
      password: ${REDIS_PASSWORD:}
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
  
  # Cache Configuration
  cache:
    type: redis
    redis:
      time-to-live: 600000
      cache-null-values: false
  
  # Kafka Configuration
  kafka:
    bootstrap-servers: localhost:9092
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      acks: 1
      retries: 3
      properties:
        enable.idempotence: true
        max.in.flight.requests.per.connection: 5
    consumer:
      group-id: notification-service
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      auto-offset-reset: earliest
      enable-auto-commit: false
      properties:
        session.timeout.ms: 30000
        heartbeat.interval.ms: 3000
    listener:
      ack-mode: manual_immediate
  
  # Mail Configuration
  mail:
    host: ${MAIL_HOST:localhost}
    port: ${MAIL_PORT:587}
    username: ${MAIL_USERNAME:}
    password: ${MAIL_PASSWORD:}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
          connectiontimeout: 5000
          timeout: 5000
          writetimeout: 5000
  
  # Security Configuration
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: http://localhost:8081/realms/ndvn-scs
          jwk-set-uri: http://localhost:8081/realms/ndvn-scs/protocol/openid-connect/certs

# Service Discovery
  cloud:
    consul:
      host: localhost
      port: 8500
      discovery:
        enabled: true
        register: true
        health-check-enabled: true
        health-check-path: /actuator/health
        health-check-interval: 30s
        instance-id: ${spring.application.name}-${server.port}
        service-name: ${spring.application.name}
        hostname: localhost
        port: ${server.port}
        prefer-ip-address: false
        tags:
          - notification
          - scs
          - ndvn-platform

# Management and Monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when_authorized
  metrics:
    export:
      prometheus:
        enabled: true

# Logging Configuration
logging:
  level:
    com.nttdata.ndvn.notification: INFO
    org.springframework.kafka: WARN
    org.hibernate.SQL: WARN
    org.hibernate.type.descriptor.sql.BasicBinder: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/notification-service.log

# Application Configuration
app:
  notification:
    async:
      core-pool-size: 5
      max-pool-size: 10
      queue-capacity: 100
    retry:
      max-attempts: 3
      delay: 1000
      multiplier: 2
  
  sms:
    provider:
      url: ${SMS_PROVIDER_URL:http://localhost:8090/sms}
      api-key: ${SMS_API_KEY:mock-api-key}
  
  push:
    provider:
      url: ${PUSH_PROVIDER_URL:http://localhost:8091/push}
      api-key: ${PUSH_API_KEY:mock-push-api-key}

# Resilience4j Configuration
resilience4j:
  circuitbreaker:
    instances:
      email-service:
        sliding-window-size: 10
        minimum-number-of-calls: 5
        failure-rate-threshold: 50
        wait-duration-in-open-state: 30s
        permitted-number-of-calls-in-half-open-state: 3
      sms-service:
        sliding-window-size: 10
        minimum-number-of-calls: 5
        failure-rate-threshold: 50
        wait-duration-in-open-state: 30s
        permitted-number-of-calls-in-half-open-state: 3
      push-service:
        sliding-window-size: 10
        minimum-number-of-calls: 5
        failure-rate-threshold: 50
        wait-duration-in-open-state: 30s
        permitted-number-of-calls-in-half-open-state: 3
  
  retry:
    instances:
      email-service:
        max-attempts: 3
        wait-duration: 1s
        exponential-backoff-multiplier: 2
      sms-service:
        max-attempts: 3
        wait-duration: 1s
        exponential-backoff-multiplier: 2
      push-service:
        max-attempts: 3
        wait-duration: 1s
        exponential-backoff-multiplier: 2

---
# Development Profile
spring:
  config:
    activate:
      on-profile: dev
  datasource:
    url: ************************************************_dev
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true

---
# Test Profile
spring:
  config:
    activate:
      on-profile: test
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
  jpa:
    hibernate:
      ddl-auto: create-drop
  kafka:
    bootstrap-servers: ${spring.embedded.kafka.brokers}

---
# Production Profile
spring:
  config:
    activate:
      on-profile: prod
  datasource:
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:notification_db}
  cloud:
    consul:
      host: ${CONSUL_HOST:consul-service}
      port: ${CONSUL_PORT:8500}

package com.nttdata.ndvn.order.application.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * Request DTO for cancelling an order.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-29
 */
public class CancelOrderRequest {
    
    @NotBlank(message = "Cancellation reason is required")
    @Size(max = 500, message = "Cancellation reason must not exceed 500 characters")
    private String reason;
    
    // Constructors
    public CancelOrderRequest() {}
    
    public CancelOrderRequest(String reason) {
        this.reason = reason;
    }
    
    // Getters and setters
    public String getReason() {
        return reason;
    }
    
    public void setReason(String reason) {
        this.reason = reason;
    }
    
    @Override
    public String toString() {
        return "CancelOrderRequest{" +
                "reason='" + reason + '\'' +
                '}';
    }
}

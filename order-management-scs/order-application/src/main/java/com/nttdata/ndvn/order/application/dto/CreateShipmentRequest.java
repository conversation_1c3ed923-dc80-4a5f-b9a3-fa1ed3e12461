package com.nttdata.ndvn.order.application.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Create shipment request DTO for order management.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-28
 */
public class CreateShipmentRequest {
    
    @NotNull(message = "Order ID is required")
    private UUID orderId;
    
    @NotBlank(message = "Carrier is required")
    private String carrier;
    
    @NotBlank(message = "Shipping address is required")
    private String shippingAddress;
    
    private BigDecimal shippingCost;
    private LocalDateTime estimatedDelivery;
    private String specialInstructions;
    private boolean requiresSignature;
    private boolean isExpress;
    
    // Default constructor
    public CreateShipmentRequest() {}
    
    // Constructor with required fields
    public CreateShipmentRequest(UUID orderId, String carrier, String shippingAddress) {
        this.orderId = orderId;
        this.carrier = carrier;
        this.shippingAddress = shippingAddress;
    }
    
    // Getters and Setters
    public UUID getOrderId() {
        return orderId;
    }
    
    public void setOrderId(UUID orderId) {
        this.orderId = orderId;
    }
    
    public String getCarrier() {
        return carrier;
    }
    
    public void setCarrier(String carrier) {
        this.carrier = carrier;
    }
    
    public String getShippingAddress() {
        return shippingAddress;
    }
    
    public void setShippingAddress(String shippingAddress) {
        this.shippingAddress = shippingAddress;
    }
    
    public BigDecimal getShippingCost() {
        return shippingCost;
    }
    
    public void setShippingCost(BigDecimal shippingCost) {
        this.shippingCost = shippingCost;
    }
    
    public LocalDateTime getEstimatedDelivery() {
        return estimatedDelivery;
    }
    
    public void setEstimatedDelivery(LocalDateTime estimatedDelivery) {
        this.estimatedDelivery = estimatedDelivery;
    }
    
    public String getSpecialInstructions() {
        return specialInstructions;
    }
    
    public void setSpecialInstructions(String specialInstructions) {
        this.specialInstructions = specialInstructions;
    }
    
    public boolean isRequiresSignature() {
        return requiresSignature;
    }
    
    public void setRequiresSignature(boolean requiresSignature) {
        this.requiresSignature = requiresSignature;
    }
    
    public boolean isExpress() {
        return isExpress;
    }
    
    public void setExpress(boolean express) {
        isExpress = express;
    }
    
    @Override
    public String toString() {
        return "CreateShipmentRequest{" +
                "orderId=" + orderId +
                ", carrier='" + carrier + '\'' +
                ", shippingAddress='" + shippingAddress + '\'' +
                ", shippingCost=" + shippingCost +
                ", estimatedDelivery=" + estimatedDelivery +
                ", isExpress=" + isExpress +
                '}';
    }
}

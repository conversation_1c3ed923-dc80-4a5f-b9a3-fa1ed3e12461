package com.nttdata.ndvn.order.application.dto;

import com.nttdata.ndvn.order.domain.model.ShipmentStatus;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * Shipment tracking response DTO for order management.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-29
 */
public class ShipmentTrackingResponse {
    
    private UUID shipmentId;
    private String trackingNumber;
    private ShipmentStatus status;
    private String carrier;
    private List<TrackingEventDto> trackingEvents;
    private LocalDateTime estimatedDelivery;
    private LocalDateTime actualDelivery;
    private String shippingAddress;
    
    // Constructors
    public ShipmentTrackingResponse() {}
    
    public ShipmentTrackingResponse(UUID shipmentId, String trackingNumber, ShipmentStatus status, String carrier) {
        this.shipmentId = shipmentId;
        this.trackingNumber = trackingNumber;
        this.status = status;
        this.carrier = carrier;
    }
    
    // Getters and setters
    public UUID getShipmentId() {
        return shipmentId;
    }
    
    public void setShipmentId(UUID shipmentId) {
        this.shipmentId = shipmentId;
    }
    
    public String getTrackingNumber() {
        return trackingNumber;
    }
    
    public void setTrackingNumber(String trackingNumber) {
        this.trackingNumber = trackingNumber;
    }
    
    public ShipmentStatus getStatus() {
        return status;
    }
    
    public void setStatus(ShipmentStatus status) {
        this.status = status;
    }
    
    public String getCarrier() {
        return carrier;
    }
    
    public void setCarrier(String carrier) {
        this.carrier = carrier;
    }
    
    public List<TrackingEventDto> getTrackingEvents() {
        return trackingEvents;
    }
    
    public void setTrackingEvents(List<TrackingEventDto> trackingEvents) {
        this.trackingEvents = trackingEvents;
    }
    
    public LocalDateTime getEstimatedDelivery() {
        return estimatedDelivery;
    }
    
    public void setEstimatedDelivery(LocalDateTime estimatedDelivery) {
        this.estimatedDelivery = estimatedDelivery;
    }
    
    public LocalDateTime getActualDelivery() {
        return actualDelivery;
    }
    
    public void setActualDelivery(LocalDateTime actualDelivery) {
        this.actualDelivery = actualDelivery;
    }
    
    public String getShippingAddress() {
        return shippingAddress;
    }
    
    public void setShippingAddress(String shippingAddress) {
        this.shippingAddress = shippingAddress;
    }
    
    @Override
    public String toString() {
        return "ShipmentTrackingResponse{" +
                "shipmentId=" + shipmentId +
                ", trackingNumber='" + trackingNumber + '\'' +
                ", status=" + status +
                ", carrier='" + carrier + '\'' +
                ", estimatedDelivery=" + estimatedDelivery +
                '}';
    }
    
    /**
     * DTO for tracking events.
     */
    public static class TrackingEventDto {
        private UUID id;
        private ShipmentStatus status;
        private String description;
        private String location;
        private LocalDateTime eventTime;
        
        // Constructors
        public TrackingEventDto() {}
        
        public TrackingEventDto(ShipmentStatus status, String description, LocalDateTime eventTime) {
            this.status = status;
            this.description = description;
            this.eventTime = eventTime;
        }
        
        // Getters and setters
        public UUID getId() {
            return id;
        }
        
        public void setId(UUID id) {
            this.id = id;
        }
        
        public ShipmentStatus getStatus() {
            return status;
        }
        
        public void setStatus(ShipmentStatus status) {
            this.status = status;
        }
        
        public String getDescription() {
            return description;
        }
        
        public void setDescription(String description) {
            this.description = description;
        }
        
        public String getLocation() {
            return location;
        }
        
        public void setLocation(String location) {
            this.location = location;
        }
        
        public LocalDateTime getEventTime() {
            return eventTime;
        }
        
        public void setEventTime(LocalDateTime eventTime) {
            this.eventTime = eventTime;
        }
        
        @Override
        public String toString() {
            return "TrackingEventDto{" +
                    "status=" + status +
                    ", description='" + description + '\'' +
                    ", location='" + location + '\'' +
                    ", eventTime=" + eventTime +
                    '}';
        }
    }
}

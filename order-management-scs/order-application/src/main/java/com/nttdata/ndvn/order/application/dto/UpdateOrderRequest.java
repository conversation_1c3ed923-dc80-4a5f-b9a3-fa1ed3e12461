package com.nttdata.ndvn.order.application.dto;

import com.nttdata.ndvn.order.domain.model.OrderStatus;
import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * Update order request DTO for order management.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-28
 */
public class UpdateOrderRequest {
    
    @NotNull(message = "Order ID is required")
    private UUID orderId;
    
    private OrderStatus status;
    private BigDecimal totalAmount;
    private BigDecimal discountAmount;
    private BigDecimal taxAmount;
    private String notes;
    private LocalDateTime expectedDeliveryDate;
    private List<OrderItemUpdateRequest> items;
    private ShippingAddressRequest shippingAddress;
    private BillingAddressRequest billingAddress;
    
    // Default constructor
    public UpdateOrderRequest() {}
    
    // Constructor with required fields
    public UpdateOrderRequest(UUID orderId) {
        this.orderId = orderId;
    }
    
    // Getters and Setters
    public UUID getOrderId() {
        return orderId;
    }
    
    public void setOrderId(UUID orderId) {
        this.orderId = orderId;
    }
    
    public OrderStatus getStatus() {
        return status;
    }
    
    public void setStatus(OrderStatus status) {
        this.status = status;
    }
    
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }
    
    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }
    
    public BigDecimal getDiscountAmount() {
        return discountAmount;
    }
    
    public void setDiscountAmount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount;
    }
    
    public BigDecimal getTaxAmount() {
        return taxAmount;
    }
    
    public void setTaxAmount(BigDecimal taxAmount) {
        this.taxAmount = taxAmount;
    }
    
    public String getNotes() {
        return notes;
    }
    
    public void setNotes(String notes) {
        this.notes = notes;
    }
    
    public LocalDateTime getExpectedDeliveryDate() {
        return expectedDeliveryDate;
    }
    
    public void setExpectedDeliveryDate(LocalDateTime expectedDeliveryDate) {
        this.expectedDeliveryDate = expectedDeliveryDate;
    }
    
    public List<OrderItemUpdateRequest> getItems() {
        return items;
    }
    
    public void setItems(List<OrderItemUpdateRequest> items) {
        this.items = items;
    }
    
    public ShippingAddressRequest getShippingAddress() {
        return shippingAddress;
    }
    
    public void setShippingAddress(ShippingAddressRequest shippingAddress) {
        this.shippingAddress = shippingAddress;
    }
    
    public BillingAddressRequest getBillingAddress() {
        return billingAddress;
    }
    
    public void setBillingAddress(BillingAddressRequest billingAddress) {
        this.billingAddress = billingAddress;
    }
    
    @Override
    public String toString() {
        return "UpdateOrderRequest{" +
                "orderId=" + orderId +
                ", status=" + status +
                ", totalAmount=" + totalAmount +
                ", notes='" + notes + '\'' +
                ", expectedDeliveryDate=" + expectedDeliveryDate +
                '}';
    }
    
    /**
     * Inner class for order item updates
     */
    public static class OrderItemUpdateRequest {
        private UUID itemId;
        private Integer quantity;
        private BigDecimal unitPrice;
        
        public OrderItemUpdateRequest() {}
        
        public OrderItemUpdateRequest(UUID itemId, Integer quantity, BigDecimal unitPrice) {
            this.itemId = itemId;
            this.quantity = quantity;
            this.unitPrice = unitPrice;
        }
        
        public UUID getItemId() {
            return itemId;
        }
        
        public void setItemId(UUID itemId) {
            this.itemId = itemId;
        }
        
        public Integer getQuantity() {
            return quantity;
        }
        
        public void setQuantity(Integer quantity) {
            this.quantity = quantity;
        }
        
        public BigDecimal getUnitPrice() {
            return unitPrice;
        }
        
        public void setUnitPrice(BigDecimal unitPrice) {
            this.unitPrice = unitPrice;
        }
    }
}

package com.nttdata.ndvn.order.application.dto;

import com.nttdata.ndvn.order.domain.model.OrderStatus;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * Request DTO for updating order status.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-29
 */
public class UpdateOrderStatusRequest {
    
    @NotNull(message = "Order status is required")
    private OrderStatus status;
    
    @Size(max = 500, message = "Reason must not exceed 500 characters")
    private String reason;
    
    // Constructors
    public UpdateOrderStatusRequest() {}
    
    public UpdateOrderStatusRequest(OrderStatus status, String reason) {
        this.status = status;
        this.reason = reason;
    }
    
    // Getters and setters
    public OrderStatus getStatus() {
        return status;
    }
    
    public void setStatus(OrderStatus status) {
        this.status = status;
    }
    
    public String getReason() {
        return reason;
    }
    
    public void setReason(String reason) {
        this.reason = reason;
    }
    
    @Override
    public String toString() {
        return "UpdateOrderStatusRequest{" +
                "status=" + status +
                ", reason='" + reason + '\'' +
                '}';
    }
}

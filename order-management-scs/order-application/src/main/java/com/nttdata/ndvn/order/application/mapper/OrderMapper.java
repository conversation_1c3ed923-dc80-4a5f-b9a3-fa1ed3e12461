package com.nttdata.ndvn.order.application.mapper;

import com.nttdata.ndvn.order.application.dto.*;
import com.nttdata.ndvn.order.domain.model.Order;
import com.nttdata.ndvn.order.domain.model.OrderItem;
import com.nttdata.ndvn.order.domain.model.Payment;
import com.nttdata.ndvn.order.domain.model.Shipment;
import org.mapstruct.*;

import java.util.List;

/**
 * MapStruct mapper for converting between domain entities and DTOs.
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OrderMapper {
    
    /**
     * Maps Order entity to OrderResponse DTO.
     * 
     * @param order the order entity
     * @return the order response DTO
     */
    @Mapping(target = "items", source = "items")
    @Mapping(target = "payments", source = "payments")
    @Mapping(target = "shipments", source = "shipments")
    OrderResponse mapToOrderResponse(Order order);
    
    /**
     * Maps list of Order entities to list of OrderResponse DTOs.
     * 
     * @param orders the order entities
     * @return the order response DTOs
     */
    List<OrderResponse> mapToOrderResponseList(List<Order> orders);
    
    /**
     * Maps OrderItem entity to OrderItemResponse DTO.
     * 
     * @param orderItem the order item entity
     * @return the order item response DTO
     */
    OrderItemResponse mapToOrderItemResponse(OrderItem orderItem);
    
    /**
     * Maps Payment entity to PaymentResponse DTO.
     * 
     * @param payment the payment entity
     * @return the payment response DTO
     */
    PaymentResponse mapToPaymentResponse(Payment payment);
    
    /**
     * Maps Shipment entity to ShipmentResponse DTO.
     *
     * @param shipment the shipment entity
     * @return the shipment response DTO
     */
    ShipmentResponse mapToShipmentResponse(Shipment shipment);

    /**
     * Maps Shipment entity to ShipmentTrackingResponse DTO.
     *
     * @param shipment the shipment entity
     * @return the shipment tracking response DTO
     */
    @Mapping(target = "shipmentId", source = "id")
    @Mapping(target = "trackingEvents", source = "trackingEvents")
    @Mapping(target = "estimatedDelivery", source = "estimatedDeliveryDate")
    @Mapping(target = "actualDelivery", source = "actualDeliveryDate")
    @Mapping(target = "shippingAddress", expression = "java(buildShippingAddress(shipment))")
    ShipmentTrackingResponse mapToShipmentTrackingResponse(Shipment shipment);

    /**
     * Maps ShipmentTracking entity to TrackingEventDto.
     *
     * @param tracking the shipment tracking entity
     * @return the tracking event DTO
     */
    @Mapping(target = "eventTime", source = "createdAt")
    ShipmentTrackingResponse.TrackingEventDto mapToTrackingEventDto(com.nttdata.ndvn.order.domain.model.ShipmentTracking tracking);

    /**
     * Builds shipping address string from shipment entity.
     *
     * @param shipment the shipment entity
     * @return the formatted shipping address
     */
    default String buildShippingAddress(Shipment shipment) {
        if (shipment == null) return null;

        StringBuilder address = new StringBuilder();
        if (shipment.getRecipientName() != null) {
            address.append(shipment.getRecipientName()).append("\n");
        }
        if (shipment.getAddressLine1() != null) {
            address.append(shipment.getAddressLine1()).append("\n");
        }
        if (shipment.getAddressLine2() != null && !shipment.getAddressLine2().trim().isEmpty()) {
            address.append(shipment.getAddressLine2()).append("\n");
        }
        if (shipment.getCity() != null) {
            address.append(shipment.getCity());
        }
        if (shipment.getStateProvince() != null) {
            address.append(", ").append(shipment.getStateProvince());
        }
        if (shipment.getPostalCode() != null) {
            address.append(" ").append(shipment.getPostalCode());
        }
        if (shipment.getCountry() != null) {
            address.append("\n").append(shipment.getCountry());
        }

        return address.toString().trim();
    }

    /**
     * Maps CreateOrderRequest.OrderItemRequest to OrderItem entity.
     * 
     * @param itemRequest the order item request DTO
     * @return the order item entity
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "orderId", ignore = true)
    @Mapping(target = "discountAmount", constant = "0")
    @Mapping(target = "taxAmount", constant = "0")
    @Mapping(target = "taxRate", constant = "0")
    @Mapping(target = "totalAmount", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    OrderItem mapToOrderItem(CreateOrderRequest.OrderItemRequest itemRequest);
    
    /**
     * Maps billing address from request to order entity.
     * 
     * @param billingAddress the billing address request
     * @param order the order entity to update
     */
    @Mapping(target = "billingName", source = "name")
    @Mapping(target = "billingCompany", source = "company")
    @Mapping(target = "billingAddressLine1", source = "addressLine1")
    @Mapping(target = "billingAddressLine2", source = "addressLine2")
    @Mapping(target = "billingCity", source = "city")
    @Mapping(target = "billingStateProvince", source = "stateProvince")
    @Mapping(target = "billingPostalCode", source = "postalCode")
    @Mapping(target = "billingCountry", source = "country")
    void mapBillingAddress(BillingAddressRequest billingAddress, @MappingTarget Order order);
    
    /**
     * Maps CreatePaymentRequest to Payment entity.
     * 
     * @param request the payment request
     * @return the payment entity
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "status", constant = "PENDING")
    @Mapping(target = "transactionId", ignore = true)
    @Mapping(target = "authorizationCode", ignore = true)
    @Mapping(target = "gatewayTransactionId", ignore = true)
    @Mapping(target = "refundedAmount", constant = "0")
    @Mapping(target = "authorizedAt", ignore = true)
    @Mapping(target = "capturedAt", ignore = true)
    @Mapping(target = "failedAt", ignore = true)
    @Mapping(target = "refundedAt", ignore = true)
    @Mapping(target = "failureReason", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    Payment mapToPayment(CreatePaymentRequest request);
    
    /**
     * Maps CreateShipmentRequest to Shipment entity.
     * 
     * @param request the shipment request
     * @return the shipment entity
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "status", constant = "PREPARING")
    @Mapping(target = "trackingNumber", ignore = true)
    @Mapping(target = "shippingCost", constant = "0")
    @Mapping(target = "totalWeight", ignore = true)
    @Mapping(target = "estimatedDeliveryDate", ignore = true)
    @Mapping(target = "actualDeliveryDate", ignore = true)
    @Mapping(target = "shippedAt", ignore = true)
    @Mapping(target = "deliveredAt", ignore = true)
    @Mapping(target = "trackingEvents", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    Shipment mapToShipment(CreateShipmentRequest request);
    
    /**
     * Maps shipping address from request to shipment entity.
     * 
     * @param shippingAddress the shipping address request
     * @param shipment the shipment entity to update
     */
    void mapShippingAddress(ShippingAddressRequest shippingAddress, @MappingTarget Shipment shipment);
    
    /**
     * Updates order entity from UpdateOrderRequest.
     * 
     * @param request the update request
     * @param order the order entity to update
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "orderNumber", ignore = true)
    @Mapping(target = "customerId", ignore = true)
    @Mapping(target = "userId", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "currency", ignore = true)
    @Mapping(target = "subtotal", ignore = true)
    @Mapping(target = "taxAmount", ignore = true)
    @Mapping(target = "shippingAmount", ignore = true)
    @Mapping(target = "discountAmount", ignore = true)
    @Mapping(target = "totalAmount", ignore = true)
    @Mapping(target = "items", ignore = true)
    @Mapping(target = "payments", ignore = true)
    @Mapping(target = "shipments", ignore = true)
    @Mapping(target = "confirmedAt", ignore = true)
    @Mapping(target = "cancelledAt", ignore = true)
    @Mapping(target = "completedAt", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    void updateOrderFromRequest(UpdateOrderRequest request, @MappingTarget Order order);
    
    /**
     * Custom mapping method for calculating order item total amount.
     * This method is called after mapping to ensure total amount is calculated.
     */
    @AfterMapping
    default void calculateOrderItemTotal(@MappingTarget OrderItem orderItem) {
        if (orderItem.getUnitPrice() != null && orderItem.getQuantity() != null) {
            orderItem.calculateTotalAmount();
        }
    }
}

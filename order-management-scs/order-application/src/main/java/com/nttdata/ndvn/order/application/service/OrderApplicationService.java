package com.nttdata.ndvn.order.application.service;

import com.nttdata.ndvn.order.application.dto.*;
import com.nttdata.ndvn.order.application.mapper.OrderMapper;
import com.nttdata.ndvn.order.domain.event.OrderCreatedEvent;
import com.nttdata.ndvn.order.domain.event.OrderStatusChangedEvent;
import com.nttdata.ndvn.order.domain.model.Order;
import com.nttdata.ndvn.order.domain.model.OrderItem;
import com.nttdata.ndvn.order.domain.model.OrderStatus;
import com.nttdata.ndvn.order.domain.repository.OrderRepository;
import com.nttdata.ndvn.order.domain.service.OrderNumberGeneratorService;
import com.nttdata.ndvn.order.domain.service.OrderValidationService;
import com.nttdata.ndvn.order.domain.service.OrderWorkflowService;
import com.nttdata.ndvn.order.infrastructure.cache.OrderCacheService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Application service for order management operations.
 * 
 * This service orchestrates order-related business workflows and coordinates
 * between domain services, repositories, and external integrations.
 */
@Service
@Transactional
public class OrderApplicationService {
    
    private static final Logger logger = LoggerFactory.getLogger(OrderApplicationService.class);
    
    private final OrderRepository orderRepository;
    private final OrderNumberGeneratorService orderNumberGenerator;
    private final OrderValidationService orderValidationService;
    private final OrderWorkflowService orderWorkflowService;
    private final OrderCacheService orderCacheService;
    private final OrderMapper orderMapper;
    private final ApplicationEventPublisher eventPublisher;
    
    @Autowired
    public OrderApplicationService(OrderRepository orderRepository,
                                 OrderNumberGeneratorService orderNumberGenerator,
                                 OrderValidationService orderValidationService,
                                 OrderWorkflowService orderWorkflowService,
                                 OrderCacheService orderCacheService,
                                 OrderMapper orderMapper,
                                 ApplicationEventPublisher eventPublisher) {
        this.orderRepository = orderRepository;
        this.orderNumberGenerator = orderNumberGenerator;
        this.orderValidationService = orderValidationService;
        this.orderWorkflowService = orderWorkflowService;
        this.orderCacheService = orderCacheService;
        this.orderMapper = orderMapper;
        this.eventPublisher = eventPublisher;
    }
    
    /**
     * Creates a new order.
     * 
     * @param request the create order request
     * @return the created order response
     */
    public OrderResponse createOrder(CreateOrderRequest request) {
        logger.info("Creating order for customer: {}", request.getCustomerId());
        
        // Generate unique order number
        String orderNumber = orderNumberGenerator.generateOrderNumber();
        
        // Create order entity
        Order order = new Order(orderNumber, request.getCustomerId(), request.getUserId(), request.getCurrency());
        
        // Set billing address
        if (request.getBillingAddress() != null) {
            orderMapper.mapBillingAddress(request.getBillingAddress(), order);
        }
        
        // Set notes and instructions
        order.setNotes(request.getNotes());
        order.setSpecialInstructions(request.getSpecialInstructions());
        
        // Add order items
        for (CreateOrderRequest.OrderItemRequest itemRequest : request.getItems()) {
            OrderItem item = orderMapper.mapToOrderItem(itemRequest);
            order.addItem(item);
        }
        
        // Apply discount if provided
        if (request.getDiscountCode() != null && !request.getDiscountCode().trim().isEmpty()) {
            // TODO: Integrate with discount service to calculate discount amount
            // For now, we'll skip discount application
        }
        
        // Validate order
        List<String> validationErrors = orderValidationService.validateOrderForConfirmation(order);
        if (!validationErrors.isEmpty()) {
            throw new OrderValidationException("Order validation failed: " + String.join(", ", validationErrors));
        }
        
        // Save order
        Order savedOrder = orderRepository.save(order);
        
        // Cache the order
        orderCacheService.cacheOrder(savedOrder);
        orderCacheService.cacheOrderStatus(savedOrder.getId(), savedOrder.getStatus());
        
        // Publish domain event
        OrderCreatedEvent event = new OrderCreatedEvent(
            savedOrder.getId(),
            savedOrder.getOrderNumber(),
            savedOrder.getCustomerId(),
            savedOrder.getUserId(),
            savedOrder.getStatus(),
            savedOrder.getTotalAmount(),
            savedOrder.getCurrency(),
            savedOrder.getItems().size()
        );
        eventPublisher.publishEvent(event);
        
        logger.info("Order created successfully: {} for customer: {}", orderNumber, request.getCustomerId());
        
        return orderMapper.mapToOrderResponse(savedOrder);
    }
    
    /**
     * Retrieves an order by ID.
     * 
     * @param orderId the order ID
     * @return the order response
     */
    @Transactional(readOnly = true)
    public OrderResponse getOrder(UUID orderId) {
        logger.debug("Retrieving order: {}", orderId);
        
        // Try cache first
        Optional<Order> cachedOrder = orderCacheService.getOrder(orderId);
        if (cachedOrder.isPresent()) {
            logger.debug("Order found in cache: {}", orderId);
            return orderMapper.mapToOrderResponse(cachedOrder.get());
        }
        
        // Fallback to database
        Order order = orderRepository.findByIdWithAllRelations(orderId)
            .orElseThrow(() -> new OrderNotFoundException("Order not found: " + orderId));
        
        // Cache the order
        orderCacheService.cacheOrder(order);
        
        return orderMapper.mapToOrderResponse(order);
    }
    
    /**
     * Retrieves an order by order number.
     * 
     * @param orderNumber the order number
     * @return the order response
     */
    @Transactional(readOnly = true)
    public OrderResponse getOrderByNumber(String orderNumber) {
        logger.debug("Retrieving order by number: {}", orderNumber);
        
        // Try cache first
        Optional<Order> cachedOrder = orderCacheService.getOrderByNumber(orderNumber);
        if (cachedOrder.isPresent()) {
            logger.debug("Order found in cache by number: {}", orderNumber);
            return orderMapper.mapToOrderResponse(cachedOrder.get());
        }
        
        // Fallback to database
        Order order = orderRepository.findByOrderNumber(orderNumber)
            .orElseThrow(() -> new OrderNotFoundException("Order not found: " + orderNumber));
        
        // Cache the order
        orderCacheService.cacheOrder(order);
        
        return orderMapper.mapToOrderResponse(order);
    }
    
    /**
     * Retrieves orders for a customer.
     *
     * @param customerId the customer ID
     * @param pageable pagination information
     * @return page of order responses
     */
    @Transactional(readOnly = true)
    public Page<OrderResponse> getCustomerOrders(UUID customerId, Pageable pageable) {
        logger.debug("Retrieving orders for customer: {}", customerId);

        Page<Order> orders = orderRepository.findByCustomerId(customerId, pageable);
        return orders.map(orderMapper::mapToOrderResponse);
    }

    /**
     * Finds pending orders for a customer.
     *
     * @param customerId the customer ID as string
     * @return list of pending order responses
     */
    @Transactional(readOnly = true)
    public List<OrderResponse> findPendingOrdersByCustomerId(String customerId) {
        logger.debug("Finding pending orders for customer: {}", customerId);

        UUID customerUuid = UUID.fromString(customerId);
        List<OrderStatus> pendingStatuses = List.of(OrderStatus.DRAFT, OrderStatus.PENDING, OrderStatus.CONFIRMED);

        Page<Order> orders = orderRepository.findByStatusIn(pendingStatuses, Pageable.unpaged());
        List<Order> customerOrders = orders.getContent().stream()
            .filter(order -> order.getCustomerId().equals(customerUuid))
            .toList();

        return customerOrders.stream()
            .map(orderMapper::mapToOrderResponse)
            .toList();
    }

    /**
     * Finds all orders for a customer.
     *
     * @param customerId the customer ID as string
     * @return list of order responses
     */
    @Transactional(readOnly = true)
    public List<OrderResponse> findOrdersByCustomerId(String customerId) {
        logger.debug("Finding all orders for customer: {}", customerId);

        UUID customerUuid = UUID.fromString(customerId);
        Page<Order> orders = orderRepository.findByCustomerId(customerUuid, Pageable.unpaged());

        return orders.getContent().stream()
            .map(orderMapper::mapToOrderResponse)
            .toList();
    }
    
    /**
     * Confirms an order (changes status from DRAFT to PENDING).
     * 
     * @param orderId the order ID
     * @return the updated order response
     */
    public OrderResponse confirmOrder(UUID orderId) {
        logger.info("Confirming order: {}", orderId);
        
        Order order = orderRepository.findById(orderId)
            .orElseThrow(() -> new OrderNotFoundException("Order not found: " + orderId));
        
        OrderStatus previousStatus = order.getStatus();
        
        // Validate order can be confirmed
        List<String> validationErrors = orderValidationService.validateOrderForConfirmation(order);
        if (!validationErrors.isEmpty()) {
            throw new OrderValidationException("Order cannot be confirmed: " + String.join(", ", validationErrors));
        }
        
        // Confirm the order
        order.confirm();
        
        // Save order
        Order savedOrder = orderRepository.save(order);
        
        // Update cache
        orderCacheService.cacheOrder(savedOrder);
        orderCacheService.cacheOrderStatus(savedOrder.getId(), savedOrder.getStatus());
        
        // Publish status change event
        OrderStatusChangedEvent event = new OrderStatusChangedEvent(
            savedOrder.getId(),
            savedOrder.getOrderNumber(),
            savedOrder.getCustomerId(),
            previousStatus,
            savedOrder.getStatus(),
            "Order confirmed by customer"
        );
        eventPublisher.publishEvent(event);
        
        logger.info("Order confirmed successfully: {}", orderId);
        
        return orderMapper.mapToOrderResponse(savedOrder);
    }
    
    /**
     * Cancels an order.
     * 
     * @param orderId the order ID
     * @param reason the cancellation reason
     * @return the updated order response
     */
    public OrderResponse cancelOrder(UUID orderId, String reason) {
        logger.info("Cancelling order: {} with reason: {}", orderId, reason);
        
        Order order = orderRepository.findByIdWithAllRelations(orderId)
            .orElseThrow(() -> new OrderNotFoundException("Order not found: " + orderId));
        
        OrderStatus previousStatus = order.getStatus();
        
        // Validate order can be cancelled
        OrderWorkflowService.OrderCancellationResult cancellationResult = 
            orderWorkflowService.canCancelOrder(order);
        
        if (!cancellationResult.canCancel()) {
            throw new OrderValidationException("Order cannot be cancelled: " + 
                String.join(", ", cancellationResult.getReasons()));
        }
        
        // Cancel the order
        order.cancel(reason);
        
        // Save order
        Order savedOrder = orderRepository.save(order);
        
        // Update cache
        orderCacheService.cacheOrder(savedOrder);
        orderCacheService.cacheOrderStatus(savedOrder.getId(), savedOrder.getStatus());
        
        // Publish status change event
        OrderStatusChangedEvent event = new OrderStatusChangedEvent(
            savedOrder.getId(),
            savedOrder.getOrderNumber(),
            savedOrder.getCustomerId(),
            previousStatus,
            savedOrder.getStatus(),
            reason
        );
        eventPublisher.publishEvent(event);
        
        logger.info("Order cancelled successfully: {}", orderId);
        
        return orderMapper.mapToOrderResponse(savedOrder);
    }
    
    /**
     * Updates order status.
     * 
     * @param orderId the order ID
     * @param newStatus the new status
     * @param reason the reason for status change
     * @return the updated order response
     */
    public OrderResponse updateOrderStatus(UUID orderId, OrderStatus newStatus, String reason) {
        logger.info("Updating order status: {} to {}", orderId, newStatus);
        
        Order order = orderRepository.findById(orderId)
            .orElseThrow(() -> new OrderNotFoundException("Order not found: " + orderId));
        
        OrderStatus previousStatus = order.getStatus();
        
        // Validate status transition
        if (!orderWorkflowService.isValidStatusTransition(previousStatus, newStatus)) {
            throw new OrderValidationException("Invalid status transition from " + 
                previousStatus + " to " + newStatus);
        }
        
        // Update status
        order.setStatus(newStatus);
        
        // Save order
        Order savedOrder = orderRepository.save(order);
        
        // Update cache
        orderCacheService.cacheOrder(savedOrder);
        orderCacheService.cacheOrderStatus(savedOrder.getId(), savedOrder.getStatus());
        
        // Publish status change event
        OrderStatusChangedEvent event = new OrderStatusChangedEvent(
            savedOrder.getId(),
            savedOrder.getOrderNumber(),
            savedOrder.getCustomerId(),
            previousStatus,
            savedOrder.getStatus(),
            reason
        );
        eventPublisher.publishEvent(event);
        
        logger.info("Order status updated successfully: {} to {}", orderId, newStatus);
        
        return orderMapper.mapToOrderResponse(savedOrder);
    }
    
    /**
     * Custom exception for order validation errors.
     */
    public static class OrderValidationException extends RuntimeException {
        public OrderValidationException(String message) {
            super(message);
        }
    }
    
    /**
     * Custom exception for order not found errors.
     */
    public static class OrderNotFoundException extends RuntimeException {
        public OrderNotFoundException(String message) {
            super(message);
        }
    }
}

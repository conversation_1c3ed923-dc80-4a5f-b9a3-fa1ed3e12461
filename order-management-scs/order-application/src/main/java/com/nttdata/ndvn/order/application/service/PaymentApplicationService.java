package com.nttdata.ndvn.order.application.service;

import com.nttdata.ndvn.order.application.dto.CreatePaymentRequest;
import com.nttdata.ndvn.order.application.dto.PaymentResponse;
import com.nttdata.ndvn.order.application.mapper.OrderMapper;
import com.nttdata.ndvn.order.domain.event.PaymentProcessedEvent;
import com.nttdata.ndvn.order.domain.model.Order;
import com.nttdata.ndvn.order.domain.model.Payment;
import com.nttdata.ndvn.order.domain.model.PaymentStatus;
import com.nttdata.ndvn.order.domain.repository.OrderRepository;
import com.nttdata.ndvn.order.domain.repository.PaymentRepository;
import com.nttdata.ndvn.order.domain.service.OrderValidationService;
import com.nttdata.ndvn.order.infrastructure.client.PaymentServiceClient;
import com.nttdata.ndvn.order.infrastructure.client.dto.PaymentRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

/**
 * Application service for payment processing operations.
 * 
 * This service orchestrates payment-related workflows and integrates
 * with external payment gateways.
 */
@Service
@Transactional
public class PaymentApplicationService {
    
    private static final Logger logger = LoggerFactory.getLogger(PaymentApplicationService.class);
    
    private final PaymentRepository paymentRepository;
    private final OrderRepository orderRepository;
    private final PaymentServiceClient paymentServiceClient;
    private final OrderValidationService orderValidationService;
    private final OrderMapper orderMapper;
    private final ApplicationEventPublisher eventPublisher;
    
    @Autowired
    public PaymentApplicationService(PaymentRepository paymentRepository,
                                   OrderRepository orderRepository,
                                   PaymentServiceClient paymentServiceClient,
                                   OrderValidationService orderValidationService,
                                   OrderMapper orderMapper,
                                   ApplicationEventPublisher eventPublisher) {
        this.paymentRepository = paymentRepository;
        this.orderRepository = orderRepository;
        this.paymentServiceClient = paymentServiceClient;
        this.orderValidationService = orderValidationService;
        this.orderMapper = orderMapper;
        this.eventPublisher = eventPublisher;
    }
    
    /**
     * Processes a payment for an order.
     * 
     * @param request the payment request
     * @return the payment response
     */
    public PaymentResponse processPayment(CreatePaymentRequest request) {
        logger.info("Processing payment for order: {} amount: {}", request.getOrderId(), request.getAmount());
        
        // Retrieve order
        Order order = orderRepository.findById(request.getOrderId())
            .orElseThrow(() -> new OrderNotFoundException("Order not found: " + request.getOrderId()));
        
        // Create payment entity
        Payment payment = orderMapper.mapToPayment(request);
        
        // Validate payment
        List<String> validationErrors = orderValidationService.validatePayment(order, payment);
        if (!validationErrors.isEmpty()) {
            throw new PaymentValidationException("Payment validation failed: " + String.join(", ", validationErrors));
        }
        
        try {
            // Process payment through external service
            com.nttdata.ndvn.order.infrastructure.client.dto.PaymentResponse externalResponse;
            
            if ("AUTHORIZE".equals(request.getAction())) {
                externalResponse = paymentServiceClient.authorizePayment(
                    request.getOrderId(),
                    request.getAmount(),
                    request.getCurrency(),
                    request.getPaymentMethod().name(),
                    request.getPaymentDetails()
                ).get();
            } else {
                externalResponse = paymentServiceClient.processPayment(
                    request.getOrderId(),
                    request.getAmount(),
                    request.getCurrency(),
                    request.getPaymentMethod().name(),
                    request.getPaymentDetails()
                ).get();
            }
            
            // Update payment with external response
            if (externalResponse.isSuccess()) {
                if ("AUTHORIZE".equals(request.getAction())) {
                    payment.authorize(externalResponse.getAuthorizationCode(), externalResponse.getGatewayTransactionId());
                } else {
                    payment.capture(externalResponse.getTransactionId());
                }
                payment.setPaymentGateway(externalResponse.getPaymentGateway());
            } else {
                payment.markAsFailed(externalResponse.getErrorMessage());
            }
            
        } catch (PaymentServiceClient.PaymentServiceException e) {
            logger.error("Payment processing failed for order: {}", request.getOrderId(), e);
            payment.markAsFailed(e.getMessage());
        } catch (InterruptedException e) {
            logger.error("Payment processing interrupted for order: {}", request.getOrderId(), e);
            Thread.currentThread().interrupt();
            payment.markAsFailed("Payment processing was interrupted");
        } catch (java.util.concurrent.ExecutionException e) {
            logger.error("Payment processing execution failed for order: {}", request.getOrderId(), e);
            payment.markAsFailed("Payment processing failed: " + e.getCause().getMessage());
        }
        
        // Save payment
        Payment savedPayment = paymentRepository.save(payment);
        
        // Update order with payment
        order.processPayment(savedPayment);
        orderRepository.save(order);
        
        // Publish payment event
        PaymentProcessedEvent event = new PaymentProcessedEvent(
            order.getId(),
            savedPayment.getId(),
            order.getOrderNumber(),
            order.getCustomerId(),
            savedPayment.getAmount(),
            savedPayment.getCurrency(),
            savedPayment.getStatus(),
            savedPayment.getPaymentMethod().name(),
            savedPayment.getTransactionId()
        );
        eventPublisher.publishEvent(event);
        
        logger.info("Payment processed for order: {} status: {}", request.getOrderId(), savedPayment.getStatus());
        
        return orderMapper.mapToPaymentResponse(savedPayment);
    }
    
    /**
     * Captures a previously authorized payment.
     * 
     * @param paymentId the payment ID
     * @param amount the amount to capture (optional, defaults to full amount)
     * @return the updated payment response
     */
    public PaymentResponse capturePayment(UUID paymentId, BigDecimal amount) {
        logger.info("Capturing payment: {} amount: {}", paymentId, amount);
        
        Payment payment = paymentRepository.findById(paymentId)
            .orElseThrow(() -> new PaymentNotFoundException("Payment not found: " + paymentId));
        
        if (!payment.getStatus().allowsCapture()) {
            throw new PaymentValidationException("Payment cannot be captured in current status: " + payment.getStatus());
        }
        
        BigDecimal captureAmount = amount != null ? amount : payment.getAmount();
        
        try {
            // Capture payment through external service
            com.nttdata.ndvn.order.infrastructure.client.dto.PaymentResponse externalResponse =
                paymentServiceClient.capturePayment(payment.getGatewayTransactionId(), captureAmount).get();
            
            if (externalResponse.isSuccess()) {
                payment.capture(externalResponse.getTransactionId());
            } else {
                payment.markAsFailed(externalResponse.getErrorMessage());
            }
            
        } catch (PaymentServiceClient.PaymentServiceException e) {
            logger.error("Payment capture failed for payment: {}", paymentId, e);
            payment.markAsFailed(e.getMessage());
        } catch (InterruptedException e) {
            logger.error("Payment capture interrupted for payment: {}", paymentId, e);
            Thread.currentThread().interrupt();
            payment.markAsFailed("Payment capture was interrupted");
        } catch (java.util.concurrent.ExecutionException e) {
            logger.error("Payment capture execution failed for payment: {}", paymentId, e);
            payment.markAsFailed("Payment capture failed: " + e.getCause().getMessage());
        }
        
        // Save payment
        Payment savedPayment = paymentRepository.save(payment);
        
        // Update order status if payment is successful
        if (savedPayment.isSuccessful()) {
            Order order = orderRepository.findById(savedPayment.getOrderId())
                .orElseThrow(() -> new OrderNotFoundException("Order not found: " + savedPayment.getOrderId()));
            
            if (order.getStatus().allowsPayment()) {
                order.setStatus(order.getStatus() == com.nttdata.ndvn.order.domain.model.OrderStatus.PAYMENT_AUTHORIZED ? 
                    com.nttdata.ndvn.order.domain.model.OrderStatus.PAID : order.getStatus());
                orderRepository.save(order);
            }
        }
        
        logger.info("Payment capture completed for payment: {} status: {}", paymentId, savedPayment.getStatus());
        
        return orderMapper.mapToPaymentResponse(savedPayment);
    }
    
    /**
     * Refunds a payment.
     * 
     * @param paymentId the payment ID
     * @param amount the refund amount
     * @param reason the refund reason
     * @return the updated payment response
     */
    public PaymentResponse refundPayment(UUID paymentId, BigDecimal amount, String reason) {
        logger.info("Refunding payment: {} amount: {} reason: {}", paymentId, amount, reason);
        
        Payment payment = paymentRepository.findById(paymentId)
            .orElseThrow(() -> new PaymentNotFoundException("Payment not found: " + paymentId));
        
        if (!payment.getStatus().allowsRefund()) {
            throw new PaymentValidationException("Payment cannot be refunded in current status: " + payment.getStatus());
        }
        
        try {
            // Process refund through external service
            com.nttdata.ndvn.order.infrastructure.client.dto.RefundResponse externalResponse =
                paymentServiceClient.refundPayment(payment.getTransactionId(), amount, reason).get();
            
            if (externalResponse.isSuccess()) {
                payment.refund(amount);
            } else {
                throw new PaymentValidationException("Refund failed: " + externalResponse.getErrorMessage());
            }
            
        } catch (PaymentServiceClient.PaymentServiceException e) {
            logger.error("Payment refund failed for payment: {}", paymentId, e);
            throw new PaymentValidationException("Refund failed: " + e.getMessage());
        } catch (InterruptedException e) {
            logger.error("Payment refund interrupted for payment: {}", paymentId, e);
            Thread.currentThread().interrupt();
            throw new PaymentValidationException("Refund was interrupted");
        } catch (java.util.concurrent.ExecutionException e) {
            logger.error("Payment refund execution failed for payment: {}", paymentId, e);
            throw new PaymentValidationException("Refund failed: " + e.getCause().getMessage());
        }
        
        // Save payment
        Payment savedPayment = paymentRepository.save(payment);
        
        logger.info("Payment refund completed for payment: {} status: {}", paymentId, savedPayment.getStatus());
        
        return orderMapper.mapToPaymentResponse(savedPayment);
    }
    
    /**
     * Retrieves payments for an order.
     * 
     * @param orderId the order ID
     * @return list of payment responses
     */
    @Transactional(readOnly = true)
    public List<PaymentResponse> getOrderPayments(UUID orderId) {
        logger.debug("Retrieving payments for order: {}", orderId);
        
        List<Payment> payments = paymentRepository.findByOrderId(orderId);
        return payments.stream()
            .map(orderMapper::mapToPaymentResponse)
            .toList();
    }
    
    /**
     * Retrieves payments by status.
     * 
     * @param status the payment status
     * @param pageable pagination information
     * @return page of payment responses
     */
    @Transactional(readOnly = true)
    public Page<PaymentResponse> getPaymentsByStatus(PaymentStatus status, Pageable pageable) {
        logger.debug("Retrieving payments by status: {}", status);
        
        Page<Payment> payments = paymentRepository.findByStatus(status, pageable);
        return payments.map(orderMapper::mapToPaymentResponse);
    }
    
    /**
     * Custom exception for payment validation errors.
     */
    public static class PaymentValidationException extends RuntimeException {
        public PaymentValidationException(String message) {
            super(message);
        }
    }
    
    /**
     * Custom exception for payment not found errors.
     */
    public static class PaymentNotFoundException extends RuntimeException {
        public PaymentNotFoundException(String message) {
            super(message);
        }
    }
    
    /**
     * Custom exception for order not found errors.
     */
    public static class OrderNotFoundException extends RuntimeException {
        public OrderNotFoundException(String message) {
            super(message);
        }
    }
}

package com.nttdata.ndvn.order.application.service;

import com.nttdata.ndvn.order.application.dto.CreateShipmentRequest;
import com.nttdata.ndvn.order.application.dto.ShipmentResponse;
import com.nttdata.ndvn.order.application.dto.ShipmentTrackingResponse;
import com.nttdata.ndvn.order.application.mapper.OrderMapper;
import com.nttdata.ndvn.order.domain.model.Order;
import com.nttdata.ndvn.order.domain.model.Shipment;
import com.nttdata.ndvn.order.domain.model.ShipmentStatus;
import com.nttdata.ndvn.order.domain.repository.OrderRepository;
import com.nttdata.ndvn.order.domain.repository.ShipmentRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Application service for shipment management operations.
 * 
 * This service handles shipment creation, tracking, status updates,
 * and delivery confirmation workflows.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-29
 */
@Service
@Transactional
public class ShipmentApplicationService {
    
    private static final Logger logger = LoggerFactory.getLogger(ShipmentApplicationService.class);
    
    private final ShipmentRepository shipmentRepository;
    private final OrderRepository orderRepository;
    private final OrderMapper orderMapper;
    
    @Autowired
    public ShipmentApplicationService(
            ShipmentRepository shipmentRepository,
            OrderRepository orderRepository,
            OrderMapper orderMapper) {
        this.shipmentRepository = shipmentRepository;
        this.orderRepository = orderRepository;
        this.orderMapper = orderMapper;
    }
    
    /**
     * Creates a new shipment for an order.
     * 
     * @param request the create shipment request
     * @return the created shipment response
     * @throws OrderNotFoundException if the order is not found
     * @throws ShipmentValidationException if validation fails
     */
    public ShipmentResponse createShipment(CreateShipmentRequest request) {
        logger.info("Creating shipment for order: {}", request.getOrderId());
        
        // Validate order exists and can be shipped
        Order order = orderRepository.findById(request.getOrderId())
            .orElseThrow(() -> new OrderNotFoundException("Order not found: " + request.getOrderId()));
        
        if (!order.getStatus().allowsShipping()) {
            throw new ShipmentValidationException("Order cannot be shipped in current status: " + order.getStatus());
        }
        
        // Create shipment entity
        // Parse shipping address or use default values
        String[] addressParts = parseShippingAddress(request.getShippingAddress());

        Shipment shipment = new Shipment(
            request.getOrderId(),
            addressParts[0], // recipientName
            addressParts[1], // addressLine1
            addressParts[2], // city
            addressParts[3], // postalCode
            addressParts[4]  // country
        );

        // Set carrier after creation
        shipment.setCarrier(request.getCarrier());
        
        // Set optional fields
        if (request.getShippingCost() != null) {
            shipment.setShippingCost(request.getShippingCost());
        }
        if (request.getEstimatedDelivery() != null) {
            shipment.setEstimatedDeliveryDate(request.getEstimatedDelivery());
        }
        if (request.getSpecialInstructions() != null) {
            shipment.setSpecialInstructions(request.getSpecialInstructions());
        }
        
        // Generate tracking number
        String trackingNumber = generateTrackingNumber();
        shipment.setTrackingNumber(trackingNumber);
        
        // Save shipment
        Shipment savedShipment = shipmentRepository.save(shipment);
        
        logger.info("Shipment created successfully: {} for order: {}", 
            savedShipment.getId(), request.getOrderId());
        
        return orderMapper.mapToShipmentResponse(savedShipment);
    }
    
    /**
     * Retrieves all shipments for an order.
     * 
     * @param orderId the order ID
     * @return list of shipment responses
     */
    @Transactional(readOnly = true)
    public List<ShipmentResponse> getOrderShipments(UUID orderId) {
        logger.debug("Retrieving shipments for order: {}", orderId);
        
        List<Shipment> shipments = shipmentRepository.findByOrderId(orderId);
        return shipments.stream()
            .map(orderMapper::mapToShipmentResponse)
            .collect(Collectors.toList());
    }
    
    /**
     * Tracks a shipment by tracking number.
     * 
     * @param trackingNumber the tracking number
     * @return the shipment tracking response
     * @throws ShipmentNotFoundException if shipment is not found
     */
    @Transactional(readOnly = true)
    public ShipmentTrackingResponse trackShipment(String trackingNumber) {
        logger.debug("Tracking shipment: {}", trackingNumber);
        
        Shipment shipment = shipmentRepository.findByTrackingNumber(trackingNumber)
            .orElseThrow(() -> new ShipmentNotFoundException("Shipment not found with tracking number: " + trackingNumber));
        
        return orderMapper.mapToShipmentTrackingResponse(shipment);
    }
    
    /**
     * Updates shipment status.
     * 
     * @param shipmentId the shipment ID
     * @param status the new status
     * @param notes optional notes
     * @return the updated shipment response
     * @throws ShipmentNotFoundException if shipment is not found
     * @throws ShipmentValidationException if status update is invalid
     */
    public ShipmentResponse updateShipmentStatus(UUID shipmentId, ShipmentStatus status, String notes) {
        logger.info("Updating shipment status: {} to {}", shipmentId, status);
        
        Shipment shipment = shipmentRepository.findById(shipmentId)
            .orElseThrow(() -> new ShipmentNotFoundException("Shipment not found: " + shipmentId));
        
        try {
            shipment.updateStatus(status, notes);
            Shipment savedShipment = shipmentRepository.save(shipment);
            
            logger.info("Shipment status updated successfully: {} to {}", shipmentId, status);
            return orderMapper.mapToShipmentResponse(savedShipment);
        } catch (IllegalStateException e) {
            throw new ShipmentValidationException("Invalid status update: " + e.getMessage());
        }
    }
    
    /**
     * Marks a shipment as delivered.
     * 
     * @param shipmentId the shipment ID
     * @param deliveryNotes optional delivery notes
     * @return the updated shipment response
     * @throws ShipmentNotFoundException if shipment is not found
     * @throws ShipmentValidationException if delivery is invalid
     */
    public ShipmentResponse markAsDelivered(UUID shipmentId, String deliveryNotes) {
        logger.info("Marking shipment as delivered: {}", shipmentId);
        
        Shipment shipment = shipmentRepository.findById(shipmentId)
            .orElseThrow(() -> new ShipmentNotFoundException("Shipment not found: " + shipmentId));
        
        try {
            shipment.markAsDelivered(deliveryNotes);
            Shipment savedShipment = shipmentRepository.save(shipment);
            
            logger.info("Shipment marked as delivered successfully: {}", shipmentId);
            return orderMapper.mapToShipmentResponse(savedShipment);
        } catch (IllegalStateException e) {
            throw new ShipmentValidationException("Cannot mark shipment as delivered: " + e.getMessage());
        }
    }
    
    /**
     * Retrieves shipments by status.
     * 
     * @param status the shipment status
     * @param pageable pagination information
     * @return page of shipment responses
     */
    @Transactional(readOnly = true)
    public Page<ShipmentResponse> getShipmentsByStatus(ShipmentStatus status, Pageable pageable) {
        logger.debug("Retrieving shipments by status: {}", status);
        
        Page<Shipment> shipments = shipmentRepository.findByStatus(status, pageable);
        return shipments.map(orderMapper::mapToShipmentResponse);
    }
    
    /**
     * Generates a unique tracking number.
     *
     * @return the generated tracking number
     */
    private String generateTrackingNumber() {
        return "TRK-" + System.currentTimeMillis() + "-" +
               UUID.randomUUID().toString().substring(0, 4).toUpperCase();
    }

    /**
     * Parses shipping address string into components.
     *
     * @param shippingAddress the shipping address string
     * @return array of [recipientName, addressLine1, city, postalCode, country]
     */
    private String[] parseShippingAddress(String shippingAddress) {
        // Simple parsing - in real implementation, this would be more sophisticated
        String[] result = new String[5];

        if (shippingAddress == null || shippingAddress.trim().isEmpty()) {
            // Default values
            result[0] = "Unknown Recipient";
            result[1] = "Unknown Address";
            result[2] = "Unknown City";
            result[3] = "00000";
            result[4] = "Unknown Country";
        } else {
            // Simple split by lines - this is a basic implementation
            String[] lines = shippingAddress.split("\n");
            result[0] = lines.length > 0 ? lines[0].trim() : "Unknown Recipient";
            result[1] = lines.length > 1 ? lines[1].trim() : "Unknown Address";
            result[2] = lines.length > 2 ? lines[2].trim() : "Unknown City";
            result[3] = "00000"; // Default postal code
            result[4] = lines.length > 3 ? lines[3].trim() : "Unknown Country";
        }

        return result;
    }
    
    /**
     * Exception thrown when an order is not found.
     */
    public static class OrderNotFoundException extends RuntimeException {
        public OrderNotFoundException(String message) {
            super(message);
        }
    }
    
    /**
     * Exception thrown when a shipment is not found.
     */
    public static class ShipmentNotFoundException extends RuntimeException {
        public ShipmentNotFoundException(String message) {
            super(message);
        }
    }
    
    /**
     * Exception thrown when shipment validation fails.
     */
    public static class ShipmentValidationException extends RuntimeException {
        public ShipmentValidationException(String message) {
            super(message);
        }
    }
}

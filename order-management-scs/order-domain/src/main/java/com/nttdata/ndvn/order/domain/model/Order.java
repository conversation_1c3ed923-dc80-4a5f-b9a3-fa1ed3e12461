package com.nttdata.ndvn.order.domain.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * Order aggregate root representing a customer order in the system.
 * 
 * This entity encapsulates the complete order lifecycle including items,
 * payments, shipments, and status management. It serves as the main
 * aggregate root for the order bounded context.
 */
@Entity
@Table(name = "orders", indexes = {
    @Index(name = "idx_order_number", columnList = "orderNumber", unique = true),
    @Index(name = "idx_order_customer_id", columnList = "customerId"),
    @Index(name = "idx_order_status", columnList = "status"),
    @Index(name = "idx_order_created_at", columnList = "createdAt"),
    @Index(name = "idx_order_user_id", columnList = "userId")
})
public class Order {
    
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;
    
    @NotBlank(message = "Order number is required")
    @Size(max = 50, message = "Order number must not exceed 50 characters")
    @Column(name = "order_number", unique = true, nullable = false, length = 50)
    private String orderNumber;
    
    @Column(name = "customer_id")
    private UUID customerId;
    
    @Column(name = "user_id")
    private UUID userId;
    
    @NotNull(message = "Order status is required")
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private OrderStatus status = OrderStatus.DRAFT;
    
    @NotBlank(message = "Currency is required")
    @Size(min = 3, max = 3, message = "Currency must be exactly 3 characters")
    @Column(name = "currency", nullable = false, length = 3)
    private String currency = "USD";
    
    @DecimalMin(value = "0.0", message = "Subtotal must be non-negative")
    @Digits(integer = 12, fraction = 2, message = "Subtotal must have at most 12 integer digits and 2 decimal places")
    @Column(name = "subtotal", precision = 14, scale = 2)
    private BigDecimal subtotal = BigDecimal.ZERO;
    
    @DecimalMin(value = "0.0", message = "Tax amount must be non-negative")
    @Digits(integer = 10, fraction = 2, message = "Tax amount must have at most 10 integer digits and 2 decimal places")
    @Column(name = "tax_amount", precision = 12, scale = 2)
    private BigDecimal taxAmount = BigDecimal.ZERO;
    
    @DecimalMin(value = "0.0", message = "Shipping amount must be non-negative")
    @Digits(integer = 10, fraction = 2, message = "Shipping amount must have at most 10 integer digits and 2 decimal places")
    @Column(name = "shipping_amount", precision = 12, scale = 2)
    private BigDecimal shippingAmount = BigDecimal.ZERO;
    
    @DecimalMin(value = "0.0", message = "Discount amount must be non-negative")
    @Digits(integer = 10, fraction = 2, message = "Discount amount must have at most 10 integer digits and 2 decimal places")
    @Column(name = "discount_amount", precision = 12, scale = 2)
    private BigDecimal discountAmount = BigDecimal.ZERO;
    
    @NotNull(message = "Total amount is required")
    @DecimalMin(value = "0.0", message = "Total amount must be non-negative")
    @Digits(integer = 12, fraction = 2, message = "Total amount must have at most 12 integer digits and 2 decimal places")
    @Column(name = "total_amount", nullable = false, precision = 14, scale = 2)
    private BigDecimal totalAmount = BigDecimal.ZERO;
    
    @Size(max = 100, message = "Discount code must not exceed 100 characters")
    @Column(name = "discount_code", length = 100)
    private String discountCode;
    
    @Size(max = 1000, message = "Notes must not exceed 1000 characters")
    @Column(name = "notes", length = 1000)
    private String notes;
    
    @Size(max = 1000, message = "Special instructions must not exceed 1000 characters")
    @Column(name = "special_instructions", length = 1000)
    private String specialInstructions;
    
    // Billing address
    @Size(max = 100, message = "Billing name must not exceed 100 characters")
    @Column(name = "billing_name", length = 100)
    private String billingName;
    
    @Size(max = 100, message = "Billing company must not exceed 100 characters")
    @Column(name = "billing_company", length = 100)
    private String billingCompany;
    
    @Size(max = 255, message = "Billing address line 1 must not exceed 255 characters")
    @Column(name = "billing_address_line1")
    private String billingAddressLine1;
    
    @Size(max = 255, message = "Billing address line 2 must not exceed 255 characters")
    @Column(name = "billing_address_line2")
    private String billingAddressLine2;
    
    @Size(max = 100, message = "Billing city must not exceed 100 characters")
    @Column(name = "billing_city", length = 100)
    private String billingCity;
    
    @Size(max = 100, message = "Billing state/province must not exceed 100 characters")
    @Column(name = "billing_state_province", length = 100)
    private String billingStateProvince;
    
    @Size(max = 20, message = "Billing postal code must not exceed 20 characters")
    @Column(name = "billing_postal_code", length = 20)
    private String billingPostalCode;
    
    @Size(max = 100, message = "Billing country must not exceed 100 characters")
    @Column(name = "billing_country", length = 100)
    private String billingCountry;
    
    @OneToMany(mappedBy = "orderId", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<OrderItem> items = new ArrayList<>();
    
    @OneToMany(mappedBy = "orderId", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Payment> payments = new ArrayList<>();
    
    @OneToMany(mappedBy = "orderId", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Shipment> shipments = new ArrayList<>();
    
    @Column(name = "confirmed_at")
    private LocalDateTime confirmedAt;
    
    @Column(name = "cancelled_at")
    private LocalDateTime cancelledAt;
    
    @Column(name = "completed_at")
    private LocalDateTime completedAt;
    
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    // Constructors
    
    protected Order() {
        // JPA constructor
    }
    
    public Order(String orderNumber, UUID customerId, UUID userId, String currency) {
        this.orderNumber = orderNumber;
        this.customerId = customerId;
        this.userId = userId;
        this.currency = currency;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    // Business methods
    
    /**
     * Adds an item to the order.
     * 
     * @param item the order item to add
     * @throws IllegalStateException if order is not modifiable
     */
    public void addItem(OrderItem item) {
        if (!status.isModifiable()) {
            throw new IllegalStateException("Cannot modify order in status: " + status);
        }
        
        item.setOrderId(this.id);
        this.items.add(item);
        recalculateAmounts();
    }
    
    /**
     * Removes an item from the order.
     * 
     * @param itemId the ID of the item to remove
     * @throws IllegalStateException if order is not modifiable
     */
    public void removeItem(UUID itemId) {
        if (!status.isModifiable()) {
            throw new IllegalStateException("Cannot modify order in status: " + status);
        }
        
        this.items.removeIf(item -> item.getId().equals(itemId));
        recalculateAmounts();
    }
    
    /**
     * Updates the quantity of an order item.
     * 
     * @param itemId the ID of the item to update
     * @param newQuantity the new quantity
     * @throws IllegalStateException if order is not modifiable
     */
    public void updateItemQuantity(UUID itemId, Integer newQuantity) {
        if (!status.isModifiable()) {
            throw new IllegalStateException("Cannot modify order in status: " + status);
        }
        
        OrderItem item = items.stream()
            .filter(i -> i.getId().equals(itemId))
            .findFirst()
            .orElseThrow(() -> new IllegalArgumentException("Item not found: " + itemId));
        
        item.updateQuantity(newQuantity);
        recalculateAmounts();
    }
    
    /**
     * Confirms the order and changes status to PENDING.
     * 
     * @throws IllegalStateException if order cannot be confirmed
     */
    public void confirm() {
        if (status != OrderStatus.DRAFT) {
            throw new IllegalStateException("Order can only be confirmed from DRAFT status");
        }
        
        if (items.isEmpty()) {
            throw new IllegalStateException("Cannot confirm order without items");
        }
        
        this.status = OrderStatus.PENDING;
        this.confirmedAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Cancels the order.
     * 
     * @param reason the reason for cancellation
     * @throws IllegalStateException if order cannot be cancelled
     */
    public void cancel(String reason) {
        if (!status.isCancellable()) {
            throw new IllegalStateException("Order cannot be cancelled in status: " + status);
        }
        
        this.status = OrderStatus.CANCELLED;
        this.cancelledAt = LocalDateTime.now();
        this.notes = (notes != null ? notes + "\n" : "") + "Cancelled: " + reason;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Processes payment for the order.
     * 
     * @param payment the payment to process
     * @throws IllegalStateException if order doesn't allow payment
     */
    public void processPayment(Payment payment) {
        if (!status.allowsPayment()) {
            throw new IllegalStateException("Order does not allow payment in status: " + status);
        }
        
        payment.setOrderId(this.id);
        this.payments.add(payment);
        
        // Update order status based on payment
        if (payment.isSuccessful()) {
            this.status = OrderStatus.PAID;
        } else if (payment.getStatus() == PaymentStatus.AUTHORIZED) {
            this.status = OrderStatus.PAYMENT_AUTHORIZED;
        }
        
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * Marks the order as processing.
     *
     * @throws IllegalStateException if order cannot be processed
     */
    public void startProcessing() {
        if (status != OrderStatus.PAID) {
            throw new IllegalStateException("Order must be paid before processing");
        }

        this.status = OrderStatus.PROCESSING;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * Marks the order as fulfilled.
     *
     * @throws IllegalStateException if order cannot be fulfilled
     */
    public void fulfill() {
        if (!status.allowsFulfillment()) {
            throw new IllegalStateException("Order cannot be fulfilled in status: " + status);
        }

        this.status = OrderStatus.FULFILLED;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * Ships the order.
     *
     * @param shipment the shipment information
     * @throws IllegalStateException if order cannot be shipped
     */
    public void ship(Shipment shipment) {
        if (!status.allowsShipping()) {
            throw new IllegalStateException("Order cannot be shipped in status: " + status);
        }

        shipment.setOrderId(this.id);
        this.shipments.add(shipment);
        this.status = OrderStatus.SHIPPED;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * Marks the order as delivered.
     *
     * @throws IllegalStateException if order is not shipped
     */
    public void markAsDelivered() {
        if (status != OrderStatus.SHIPPED) {
            throw new IllegalStateException("Order must be shipped before marking as delivered");
        }

        this.status = OrderStatus.DELIVERED;
        this.completedAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * Applies a discount to the order.
     *
     * @param discountCode the discount code
     * @param discountAmount the discount amount
     * @throws IllegalStateException if order is not modifiable
     */
    public void applyDiscount(String discountCode, BigDecimal discountAmount) {
        if (!status.isModifiable()) {
            throw new IllegalStateException("Cannot modify order in status: " + status);
        }

        if (discountAmount.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("Discount amount cannot be negative");
        }

        this.discountCode = discountCode;
        this.discountAmount = discountAmount;
        recalculateAmounts();
    }

    /**
     * Sets the shipping amount for the order.
     *
     * @param shippingAmount the shipping amount
     * @throws IllegalStateException if order is not modifiable
     */
    public void setShippingAmount(BigDecimal shippingAmount) {
        if (!status.isModifiable()) {
            throw new IllegalStateException("Cannot modify order in status: " + status);
        }

        this.shippingAmount = shippingAmount;
        recalculateAmounts();
    }

    /**
     * Recalculates all order amounts based on items, discounts, taxes, and shipping.
     */
    private void recalculateAmounts() {
        // Calculate subtotal from items
        this.subtotal = items.stream()
            .map(OrderItem::getTotalAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // Calculate total: subtotal + tax + shipping - discount
        this.totalAmount = subtotal
            .add(taxAmount)
            .add(shippingAmount)
            .subtract(discountAmount);

        // Ensure total is not negative
        if (totalAmount.compareTo(BigDecimal.ZERO) < 0) {
            totalAmount = BigDecimal.ZERO;
        }

        this.updatedAt = LocalDateTime.now();
    }

    /**
     * Calculates and applies tax to the order.
     *
     * @param taxRate the tax rate to apply
     */
    public void calculateTax(BigDecimal taxRate) {
        if (taxRate.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("Tax rate cannot be negative");
        }

        BigDecimal taxableAmount = subtotal.subtract(discountAmount);
        this.taxAmount = taxableAmount.multiply(taxRate);
        recalculateAmounts();
    }

    /**
     * Gets the total paid amount for this order.
     *
     * @return the total amount paid
     */
    public BigDecimal getTotalPaidAmount() {
        return payments.stream()
            .filter(Payment::isSuccessful)
            .map(Payment::getAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * Gets the remaining amount to be paid.
     *
     * @return the remaining amount
     */
    public BigDecimal getRemainingAmount() {
        return totalAmount.subtract(getTotalPaidAmount());
    }

    /**
     * Checks if the order is fully paid.
     *
     * @return true if the order is fully paid
     */
    public boolean isFullyPaid() {
        return getRemainingAmount().compareTo(BigDecimal.ZERO) <= 0;
    }

    /**
     * Checks if the order requires shipping.
     *
     * @return true if any item requires shipping
     */
    public boolean requiresShipping() {
        return items.stream().anyMatch(OrderItem::isRequiresShipping);
    }

    /**
     * Gets the total weight of the order.
     *
     * @return the total weight
     */
    public BigDecimal getTotalWeight() {
        return items.stream()
            .map(OrderItem::getTotalWeight)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    // JPA lifecycle callbacks

    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        createdAt = now;
        updatedAt = now;
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    // Getters and setters

    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public UUID getCustomerId() {
        return customerId;
    }

    public void setCustomerId(UUID customerId) {
        this.customerId = customerId;
    }

    public UUID getUserId() {
        return userId;
    }

    public void setUserId(UUID userId) {
        this.userId = userId;
    }

    public OrderStatus getStatus() {
        return status;
    }

    public void setStatus(OrderStatus status) {
        this.status = status;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public BigDecimal getSubtotal() {
        return subtotal;
    }

    public void setSubtotal(BigDecimal subtotal) {
        this.subtotal = subtotal;
    }

    public BigDecimal getTaxAmount() {
        return taxAmount;
    }

    public void setTaxAmount(BigDecimal taxAmount) {
        this.taxAmount = taxAmount;
    }

    public BigDecimal getShippingAmount() {
        return shippingAmount;
    }

    public BigDecimal getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getDiscountCode() {
        return discountCode;
    }

    public void setDiscountCode(String discountCode) {
        this.discountCode = discountCode;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public String getSpecialInstructions() {
        return specialInstructions;
    }

    public void setSpecialInstructions(String specialInstructions) {
        this.specialInstructions = specialInstructions;
    }

    public String getBillingName() {
        return billingName;
    }

    public void setBillingName(String billingName) {
        this.billingName = billingName;
    }

    public String getBillingCompany() {
        return billingCompany;
    }

    public void setBillingCompany(String billingCompany) {
        this.billingCompany = billingCompany;
    }

    public String getBillingAddressLine1() {
        return billingAddressLine1;
    }

    public void setBillingAddressLine1(String billingAddressLine1) {
        this.billingAddressLine1 = billingAddressLine1;
    }

    public String getBillingAddressLine2() {
        return billingAddressLine2;
    }

    public void setBillingAddressLine2(String billingAddressLine2) {
        this.billingAddressLine2 = billingAddressLine2;
    }

    public String getBillingCity() {
        return billingCity;
    }

    public void setBillingCity(String billingCity) {
        this.billingCity = billingCity;
    }

    public String getBillingStateProvince() {
        return billingStateProvince;
    }

    public void setBillingStateProvince(String billingStateProvince) {
        this.billingStateProvince = billingStateProvince;
    }

    public String getBillingPostalCode() {
        return billingPostalCode;
    }

    public void setBillingPostalCode(String billingPostalCode) {
        this.billingPostalCode = billingPostalCode;
    }

    public String getBillingCountry() {
        return billingCountry;
    }

    public void setBillingCountry(String billingCountry) {
        this.billingCountry = billingCountry;
    }

    public List<OrderItem> getItems() {
        return items;
    }

    public void setItems(List<OrderItem> items) {
        this.items = items;
    }

    public List<Payment> getPayments() {
        return payments;
    }

    public void setPayments(List<Payment> payments) {
        this.payments = payments;
    }

    public List<Shipment> getShipments() {
        return shipments;
    }

    public void setShipments(List<Shipment> shipments) {
        this.shipments = shipments;
    }

    public LocalDateTime getConfirmedAt() {
        return confirmedAt;
    }

    public void setConfirmedAt(LocalDateTime confirmedAt) {
        this.confirmedAt = confirmedAt;
    }

    public LocalDateTime getCancelledAt() {
        return cancelledAt;
    }

    public void setCancelledAt(LocalDateTime cancelledAt) {
        this.cancelledAt = cancelledAt;
    }

    public LocalDateTime getCompletedAt() {
        return completedAt;
    }

    public void setCompletedAt(LocalDateTime completedAt) {
        this.completedAt = completedAt;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    // Business methods

    /**
     * Updates the order status and sets appropriate timestamps.
     */
    public void updateStatus(OrderStatus newStatus) {
        if (newStatus == null) {
            throw new IllegalArgumentException("Order status cannot be null");
        }

        OrderStatus previousStatus = this.status;
        this.status = newStatus;
        this.updatedAt = LocalDateTime.now();

        // Set specific timestamps based on status
        switch (newStatus) {
            case CONFIRMED:
                if (this.confirmedAt == null) {
                    this.confirmedAt = LocalDateTime.now();
                }
                break;
            case CANCELLED:
                if (this.cancelledAt == null) {
                    this.cancelledAt = LocalDateTime.now();
                }
                break;
            case DELIVERED:
                if (this.completedAt == null) {
                    this.completedAt = LocalDateTime.now();
                }
                break;
        }
    }

    /**
     * Gets the order items (alias for getItems for consistency).
     */
    public List<OrderItem> getOrderItems() {
        return this.items;
    }
}

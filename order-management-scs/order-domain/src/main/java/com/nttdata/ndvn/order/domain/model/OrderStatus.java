package com.nttdata.ndvn.order.domain.model;

/**
 * Enumeration representing the various states of an order in its lifecycle.
 * 
 * This enum defines the complete order state machine and supports complex
 * order processing workflows including payment, fulfillment, and shipping.
 */
public enum OrderStatus {
    
    /**
     * Order has been created but not yet confirmed by the customer.
     */
    DRAFT("Draft", "Order created but not confirmed"),
    
    /**
     * Order has been confirmed by the customer and is pending payment.
     */
    PENDING("Pending", "Order confirmed, awaiting payment"),

    /**
     * Order has been confirmed and validated.
     */
    CONFIRMED("Confirmed", "Order confirmed and validated"),
    
    /**
     * Payment has been authorized but not yet captured.
     */
    PAYMENT_AUTHORIZED("Payment Authorized", "Payment authorized, pending capture"),
    
    /**
     * Payment has been successfully processed and captured.
     */
    PAID("Paid", "Payment completed successfully"),
    
    /**
     * Payment failed or was declined.
     */
    PAYMENT_FAILED("Payment Failed", "Payment processing failed"),
    
    /**
     * Order is being processed and prepared for fulfillment.
     */
    PROCESSING("Processing", "Order being processed"),
    
    /**
     * Order has been fulfilled and is ready for shipment.
     */
    FULFILLED("Fulfilled", "Order fulfilled, ready for shipment"),
    
    /**
     * Order has been shipped to the customer.
     */
    SHIPPED("Shipped", "Order shipped to customer"),
    
    /**
     * Order has been delivered to the customer.
     */
    DELIVERED("Delivered", "Order delivered successfully"),
    
    /**
     * Order has been cancelled by customer or system.
     */
    CANCELLED("Cancelled", "Order cancelled"),
    
    /**
     * Order has been returned by the customer.
     */
    RETURNED("Returned", "Order returned by customer"),
    
    /**
     * Order has been refunded.
     */
    REFUNDED("Refunded", "Order refunded"),
    
    /**
     * Order processing failed due to system error.
     */
    FAILED("Failed", "Order processing failed");
    
    private final String displayName;
    private final String description;
    
    OrderStatus(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * Checks if the order status allows cancellation.
     * 
     * @return true if the order can be cancelled in this status
     */
    public boolean isCancellable() {
        return this == DRAFT || this == PENDING || this == PAYMENT_AUTHORIZED || this == PAID || this == PROCESSING;
    }
    
    /**
     * Checks if the order status allows modification.
     * 
     * @return true if the order can be modified in this status
     */
    public boolean isModifiable() {
        return this == DRAFT || this == PENDING;
    }
    
    /**
     * Checks if the order status represents a completed state.
     * 
     * @return true if the order is in a final state
     */
    public boolean isCompleted() {
        return this == DELIVERED || this == CANCELLED || this == RETURNED || this == REFUNDED || this == FAILED;
    }
    
    /**
     * Checks if the order status allows payment processing.
     * 
     * @return true if payment can be processed in this status
     */
    public boolean allowsPayment() {
        return this == PENDING || this == PAYMENT_FAILED;
    }
    
    /**
     * Checks if the order status allows fulfillment.
     * 
     * @return true if the order can be fulfilled in this status
     */
    public boolean allowsFulfillment() {
        return this == PAID || this == PROCESSING;
    }
    
    /**
     * Checks if the order status allows shipping.
     * 
     * @return true if the order can be shipped in this status
     */
    public boolean allowsShipping() {
        return this == FULFILLED;
    }
}

package com.nttdata.ndvn.order.domain.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Payment entity representing a payment transaction for an order.
 * 
 * This entity encapsulates payment processing information, status tracking,
 * and supports complex payment workflows including authorization, capture,
 * and refunds.
 */
@Entity
@Table(name = "payments", indexes = {
    @Index(name = "idx_payment_order_id", columnList = "orderId"),
    @Index(name = "idx_payment_status", columnList = "status"),
    @Index(name = "idx_payment_method", columnList = "paymentMethod"),
    @Index(name = "idx_payment_transaction_id", columnList = "transactionId"),
    @Index(name = "idx_payment_created_at", columnList = "createdAt")
})
public class Payment {
    
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;
    
    @Column(name = "order_id", nullable = false)
    private UUID orderId;
    
    @NotNull(message = "Payment amount is required")
    @DecimalMin(value = "0.0", message = "Payment amount must be non-negative")
    @Digits(integer = 12, fraction = 2, message = "Payment amount must have at most 12 integer digits and 2 decimal places")
    @Column(name = "amount", nullable = false, precision = 14, scale = 2)
    private BigDecimal amount;
    
    @NotBlank(message = "Currency is required")
    @Size(min = 3, max = 3, message = "Currency must be exactly 3 characters")
    @Column(name = "currency", nullable = false, length = 3)
    private String currency = "USD";
    
    @NotNull(message = "Payment status is required")
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private PaymentStatus status = PaymentStatus.PENDING;
    
    @NotNull(message = "Payment method is required")
    @Enumerated(EnumType.STRING)
    @Column(name = "payment_method", nullable = false, length = 50)
    private PaymentMethod paymentMethod;
    
    @Size(max = 100, message = "Transaction ID must not exceed 100 characters")
    @Column(name = "transaction_id", length = 100)
    private String transactionId;
    
    @Size(max = 100, message = "Authorization code must not exceed 100 characters")
    @Column(name = "authorization_code", length = 100)
    private String authorizationCode;
    
    @Size(max = 100, message = "Gateway transaction ID must not exceed 100 characters")
    @Column(name = "gateway_transaction_id", length = 100)
    private String gatewayTransactionId;
    
    @Size(max = 50, message = "Payment gateway must not exceed 50 characters")
    @Column(name = "payment_gateway", length = 50)
    private String paymentGateway;
    
    @DecimalMin(value = "0.0", message = "Refunded amount must be non-negative")
    @Digits(integer = 12, fraction = 2, message = "Refunded amount must have at most 12 integer digits and 2 decimal places")
    @Column(name = "refunded_amount", precision = 14, scale = 2)
    private BigDecimal refundedAmount = BigDecimal.ZERO;
    
    @Column(name = "authorized_at")
    private LocalDateTime authorizedAt;
    
    @Column(name = "captured_at")
    private LocalDateTime capturedAt;
    
    @Column(name = "failed_at")
    private LocalDateTime failedAt;
    
    @Column(name = "refunded_at")
    private LocalDateTime refundedAt;
    
    @Size(max = 1000, message = "Failure reason must not exceed 1000 characters")
    @Column(name = "failure_reason", length = 1000)
    private String failureReason;
    
    @Size(max = 500, message = "Notes must not exceed 500 characters")
    @Column(name = "notes", length = 500)
    private String notes;
    
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    // Constructors
    
    protected Payment() {
        // JPA constructor
    }
    
    public Payment(UUID orderId, BigDecimal amount, String currency, PaymentMethod paymentMethod) {
        this.orderId = orderId;
        this.amount = amount;
        this.currency = currency;
        this.paymentMethod = paymentMethod;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    // Business methods
    
    /**
     * Authorizes the payment.
     * 
     * @param authorizationCode the authorization code from the payment gateway
     * @param gatewayTransactionId the transaction ID from the payment gateway
     * @throws IllegalStateException if payment is not in a state that allows authorization
     */
    public void authorize(String authorizationCode, String gatewayTransactionId) {
        if (!status.allowsCapture() && status != PaymentStatus.PENDING) {
            throw new IllegalStateException("Payment cannot be authorized in current status: " + status);
        }
        
        this.status = PaymentStatus.AUTHORIZED;
        this.authorizationCode = authorizationCode;
        this.gatewayTransactionId = gatewayTransactionId;
        this.authorizedAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Captures the authorized payment.
     * 
     * @param transactionId the final transaction ID
     * @throws IllegalStateException if payment is not authorized
     */
    public void capture(String transactionId) {
        if (!status.allowsCapture()) {
            throw new IllegalStateException("Payment cannot be captured in current status: " + status);
        }
        
        this.status = PaymentStatus.CAPTURED;
        this.transactionId = transactionId;
        this.capturedAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Marks the payment as failed.
     * 
     * @param failureReason the reason for the failure
     */
    public void markAsFailed(String failureReason) {
        this.status = PaymentStatus.FAILED;
        this.failureReason = failureReason;
        this.failedAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Processes a refund for this payment.
     * 
     * @param refundAmount the amount to refund
     * @throws IllegalStateException if payment cannot be refunded
     * @throws IllegalArgumentException if refund amount is invalid
     */
    public void refund(BigDecimal refundAmount) {
        if (!status.allowsRefund()) {
            throw new IllegalStateException("Payment cannot be refunded in current status: " + status);
        }
        
        if (refundAmount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("Refund amount must be positive");
        }
        
        BigDecimal totalRefundable = amount.subtract(refundedAmount);
        if (refundAmount.compareTo(totalRefundable) > 0) {
            throw new IllegalArgumentException("Refund amount exceeds refundable amount");
        }
        
        this.refundedAmount = this.refundedAmount.add(refundAmount);
        
        if (this.refundedAmount.compareTo(amount) == 0) {
            this.status = PaymentStatus.REFUNDED;
        } else {
            this.status = PaymentStatus.PARTIALLY_REFUNDED;
        }
        
        this.refundedAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Cancels the payment.
     * 
     * @throws IllegalStateException if payment cannot be cancelled
     */
    public void cancel() {
        if (!status.allowsCancellation()) {
            throw new IllegalStateException("Payment cannot be cancelled in current status: " + status);
        }
        
        this.status = PaymentStatus.CANCELLED;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Gets the remaining refundable amount.
     * 
     * @return the amount that can still be refunded
     */
    public BigDecimal getRefundableAmount() {
        return amount.subtract(refundedAmount);
    }
    
    /**
     * Checks if the payment is successful.
     * 
     * @return true if payment is captured
     */
    public boolean isSuccessful() {
        return status.isSuccessful();
    }
    
    // JPA lifecycle callbacks
    
    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        createdAt = now;
        updatedAt = now;
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    // Getters and setters

    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public UUID getOrderId() {
        return orderId;
    }

    public void setOrderId(UUID orderId) {
        this.orderId = orderId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public PaymentStatus getStatus() {
        return status;
    }

    public void setStatus(PaymentStatus status) {
        this.status = status;
    }

    public PaymentMethod getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(PaymentMethod paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getAuthorizationCode() {
        return authorizationCode;
    }

    public void setAuthorizationCode(String authorizationCode) {
        this.authorizationCode = authorizationCode;
    }

    public String getGatewayTransactionId() {
        return gatewayTransactionId;
    }

    public void setGatewayTransactionId(String gatewayTransactionId) {
        this.gatewayTransactionId = gatewayTransactionId;
    }

    public String getPaymentGateway() {
        return paymentGateway;
    }

    public void setPaymentGateway(String paymentGateway) {
        this.paymentGateway = paymentGateway;
    }

    public BigDecimal getRefundedAmount() {
        return refundedAmount;
    }

    public void setRefundedAmount(BigDecimal refundedAmount) {
        this.refundedAmount = refundedAmount;
    }

    public LocalDateTime getAuthorizedAt() {
        return authorizedAt;
    }

    public void setAuthorizedAt(LocalDateTime authorizedAt) {
        this.authorizedAt = authorizedAt;
    }

    public LocalDateTime getCapturedAt() {
        return capturedAt;
    }

    public void setCapturedAt(LocalDateTime capturedAt) {
        this.capturedAt = capturedAt;
    }

    public LocalDateTime getFailedAt() {
        return failedAt;
    }

    public void setFailedAt(LocalDateTime failedAt) {
        this.failedAt = failedAt;
    }

    public LocalDateTime getRefundedAt() {
        return refundedAt;
    }

    public void setRefundedAt(LocalDateTime refundedAt) {
        this.refundedAt = refundedAt;
    }

    public String getFailureReason() {
        return failureReason;
    }

    public void setFailureReason(String failureReason) {
        this.failureReason = failureReason;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
}

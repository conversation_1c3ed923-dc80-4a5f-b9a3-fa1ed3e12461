package com.nttdata.ndvn.order.domain.model;

/**
 * Enumeration representing the various payment methods supported by the system.
 * 
 * This enum defines the available payment methods for order processing
 * and supports different payment workflows and validations.
 */
public enum PaymentMethod {
    
    /**
     * Credit card payment.
     */
    CREDIT_CARD("Credit Card", "Payment via credit card"),
    
    /**
     * Debit card payment.
     */
    DEBIT_CARD("Debit Card", "Payment via debit card"),
    
    /**
     * PayPal payment.
     */
    PAYPAL("PayPal", "Payment via PayPal"),
    
    /**
     * Bank transfer payment.
     */
    BANK_TRANSFER("Bank Transfer", "Payment via bank transfer"),
    
    /**
     * Digital wallet payment.
     */
    DIGITAL_WALLET("Digital Wallet", "Payment via digital wallet"),
    
    /**
     * Cash on delivery.
     */
    CASH_ON_DELIVERY("Cash on Delivery", "Payment upon delivery"),
    
    /**
     * Apple Pay payment.
     */
    APPLE_PAY("Apple Pay", "Payment via Apple Pay"),
    
    /**
     * Google Pay payment.
     */
    GOOGLE_PAY("Google Pay", "Payment via Google Pay"),
    
    /**
     * Cryptocurrency payment.
     */
    CRYPTOCURRENCY("Cryptocurrency", "Payment via cryptocurrency"),
    
    /**
     * Buy now, pay later services.
     */
    BUY_NOW_PAY_LATER("Buy Now Pay Later", "Payment via BNPL services");
    
    private final String displayName;
    private final String description;
    
    PaymentMethod(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * Checks if the payment method requires card details.
     * 
     * @return true if card details are required
     */
    public boolean requiresCardDetails() {
        return this == CREDIT_CARD || this == DEBIT_CARD;
    }
    
    /**
     * Checks if the payment method supports immediate processing.
     * 
     * @return true if payment can be processed immediately
     */
    public boolean supportsImmediateProcessing() {
        return this != CASH_ON_DELIVERY && this != BANK_TRANSFER;
    }
    
    /**
     * Checks if the payment method requires external authorization.
     * 
     * @return true if external authorization is required
     */
    public boolean requiresExternalAuth() {
        return this == PAYPAL || this == APPLE_PAY || this == GOOGLE_PAY || 
               this == DIGITAL_WALLET || this == CRYPTOCURRENCY;
    }
    
    /**
     * Gets the payment method from string value.
     * 
     * @param value the string value
     * @return the corresponding PaymentMethod
     * @throws IllegalArgumentException if value is not valid
     */
    public static PaymentMethod fromString(String value) {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException("Payment method value cannot be null or empty");
        }
        
        try {
            return PaymentMethod.valueOf(value.toUpperCase());
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid payment method: " + value);
        }
    }
}

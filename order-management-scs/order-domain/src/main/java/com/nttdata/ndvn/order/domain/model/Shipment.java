package com.nttdata.ndvn.order.domain.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * Shipment entity representing the shipping and delivery of an order.
 * 
 * This entity encapsulates shipping information, tracking details,
 * and supports complex shipping workflows including carrier integration
 * and delivery tracking.
 */
@Entity
@Table(name = "shipments", indexes = {
    @Index(name = "idx_shipment_order_id", columnList = "orderId"),
    @Index(name = "idx_shipment_status", columnList = "status"),
    @Index(name = "idx_shipment_tracking_number", columnList = "trackingNumber"),
    @Index(name = "idx_shipment_carrier", columnList = "carrier"),
    @Index(name = "idx_shipment_created_at", columnList = "createdAt")
})
public class Shipment {
    
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;
    
    @Column(name = "order_id", nullable = false)
    private UUID orderId;
    
    @Size(max = 100, message = "Tracking number must not exceed 100 characters")
    @Column(name = "tracking_number", length = 100)
    private String trackingNumber;
    
    @NotNull(message = "Shipment status is required")
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private ShipmentStatus status = ShipmentStatus.PREPARING;
    
    @Size(max = 50, message = "Carrier must not exceed 50 characters")
    @Column(name = "carrier", length = 50)
    private String carrier;
    
    @Size(max = 50, message = "Service type must not exceed 50 characters")
    @Column(name = "service_type", length = 50)
    private String serviceType;
    
    @DecimalMin(value = "0.0", message = "Shipping cost must be non-negative")
    @Digits(integer = 10, fraction = 2, message = "Shipping cost must have at most 10 integer digits and 2 decimal places")
    @Column(name = "shipping_cost", precision = 12, scale = 2)
    private BigDecimal shippingCost = BigDecimal.ZERO;
    
    @DecimalMin(value = "0.0", message = "Total weight must be non-negative")
    @Digits(integer = 8, fraction = 3, message = "Total weight must have at most 8 integer digits and 3 decimal places")
    @Column(name = "total_weight", precision = 11, scale = 3)
    private BigDecimal totalWeight;
    
    @Size(max = 10, message = "Weight unit must not exceed 10 characters")
    @Column(name = "weight_unit", length = 10)
    private String weightUnit = "kg";
    
    // Shipping address
    @NotBlank(message = "Recipient name is required")
    @Size(max = 100, message = "Recipient name must not exceed 100 characters")
    @Column(name = "recipient_name", nullable = false, length = 100)
    private String recipientName;
    
    @Size(max = 100, message = "Company name must not exceed 100 characters")
    @Column(name = "recipient_company", length = 100)
    private String recipientCompany;
    
    @NotBlank(message = "Address line 1 is required")
    @Size(max = 255, message = "Address line 1 must not exceed 255 characters")
    @Column(name = "address_line1", nullable = false)
    private String addressLine1;
    
    @Size(max = 255, message = "Address line 2 must not exceed 255 characters")
    @Column(name = "address_line2")
    private String addressLine2;
    
    @NotBlank(message = "City is required")
    @Size(max = 100, message = "City must not exceed 100 characters")
    @Column(name = "city", nullable = false, length = 100)
    private String city;
    
    @Size(max = 100, message = "State/Province must not exceed 100 characters")
    @Column(name = "state_province", length = 100)
    private String stateProvince;
    
    @NotBlank(message = "Postal code is required")
    @Size(max = 20, message = "Postal code must not exceed 20 characters")
    @Column(name = "postal_code", nullable = false, length = 20)
    private String postalCode;
    
    @NotBlank(message = "Country is required")
    @Size(max = 100, message = "Country must not exceed 100 characters")
    @Column(name = "country", nullable = false, length = 100)
    private String country;
    
    @Size(max = 20, message = "Phone must not exceed 20 characters")
    @Column(name = "phone", length = 20)
    private String phone;
    
    @Size(max = 100, message = "Email must not exceed 100 characters")
    @Email(message = "Email must be valid")
    @Column(name = "email", length = 100)
    private String email;
    
    @Column(name = "estimated_delivery_date")
    private LocalDateTime estimatedDeliveryDate;
    
    @Column(name = "actual_delivery_date")
    private LocalDateTime actualDeliveryDate;
    
    @Column(name = "shipped_at")
    private LocalDateTime shippedAt;
    
    @Column(name = "delivered_at")
    private LocalDateTime deliveredAt;
    
    @Size(max = 1000, message = "Special instructions must not exceed 1000 characters")
    @Column(name = "special_instructions", length = 1000)
    private String specialInstructions;
    
    @Size(max = 500, message = "Notes must not exceed 500 characters")
    @Column(name = "notes", length = 500)
    private String notes;
    
    @OneToMany(mappedBy = "shipment", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<ShipmentTracking> trackingEvents = new ArrayList<>();
    
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    // Constructors
    
    protected Shipment() {
        // JPA constructor
    }
    
    public Shipment(UUID orderId, String recipientName, String addressLine1, 
                    String city, String postalCode, String country) {
        this.orderId = orderId;
        this.recipientName = recipientName;
        this.addressLine1 = addressLine1;
        this.city = city;
        this.postalCode = postalCode;
        this.country = country;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    // Business methods
    
    /**
     * Ships the order with carrier information.
     * 
     * @param carrier the shipping carrier
     * @param trackingNumber the tracking number
     * @param serviceType the service type
     * @throws IllegalStateException if shipment cannot be shipped
     */
    public void ship(String carrier, String trackingNumber, String serviceType) {
        if (!status.allowsShipping() && status != ShipmentStatus.READY_FOR_PICKUP) {
            throw new IllegalStateException("Shipment cannot be shipped in current status: " + status);
        }
        
        this.carrier = carrier;
        this.trackingNumber = trackingNumber;
        this.serviceType = serviceType;
        this.status = ShipmentStatus.PICKED_UP;
        this.shippedAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
        
        // Add tracking event
        addTrackingEvent(ShipmentStatus.PICKED_UP, "Package picked up by " + carrier);
    }
    
    /**
     * Updates the shipment status.
     * 
     * @param newStatus the new status
     * @param notes optional notes for the status change
     */
    public void updateStatus(ShipmentStatus newStatus, String notes) {
        if (this.status == newStatus) {
            return; // No change needed
        }
        
        ShipmentStatus oldStatus = this.status;
        this.status = newStatus;
        this.updatedAt = LocalDateTime.now();
        
        // Set specific timestamps based on status
        switch (newStatus) {
            case DELIVERED:
                this.deliveredAt = LocalDateTime.now();
                this.actualDeliveryDate = this.deliveredAt;
                break;
            case PICKED_UP:
                if (this.shippedAt == null) {
                    this.shippedAt = LocalDateTime.now();
                }
                break;
        }
        
        // Add tracking event
        String eventNotes = notes != null ? notes : 
            "Status changed from " + oldStatus.getDisplayName() + " to " + newStatus.getDisplayName();
        addTrackingEvent(newStatus, eventNotes);
    }
    
    /**
     * Marks the shipment as delivered.
     * 
     * @param deliveryNotes optional delivery notes
     */
    public void markAsDelivered(String deliveryNotes) {
        updateStatus(ShipmentStatus.DELIVERED, deliveryNotes);
    }
    
    /**
     * Adds a tracking event to the shipment.
     * 
     * @param status the status for the tracking event
     * @param description the description of the event
     */
    public void addTrackingEvent(ShipmentStatus status, String description) {
        ShipmentTracking tracking = new ShipmentTracking(this, status, description);
        this.trackingEvents.add(tracking);
    }
    
    /**
     * Calculates the estimated delivery date based on service type.
     * 
     * @param businessDays the number of business days for delivery
     */
    public void calculateEstimatedDelivery(int businessDays) {
        if (shippedAt != null) {
            // Simple calculation - add business days (ignoring weekends/holidays for now)
            this.estimatedDeliveryDate = shippedAt.plusDays(businessDays);
        }
    }
    
    /**
     * Checks if the shipment is delivered.
     * 
     * @return true if the shipment has been delivered
     */
    public boolean isDelivered() {
        return status.isDelivered();
    }
    
    /**
     * Checks if the shipment has tracking information.
     * 
     * @return true if tracking number is available
     */
    public boolean hasTracking() {
        return trackingNumber != null && !trackingNumber.trim().isEmpty();
    }
    
    // JPA lifecycle callbacks
    
    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        createdAt = now;
        updatedAt = now;
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    // Getters and setters

    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public UUID getOrderId() {
        return orderId;
    }

    public void setOrderId(UUID orderId) {
        this.orderId = orderId;
    }

    public String getTrackingNumber() {
        return trackingNumber;
    }

    public void setTrackingNumber(String trackingNumber) {
        this.trackingNumber = trackingNumber;
    }

    public ShipmentStatus getStatus() {
        return status;
    }

    public void setStatus(ShipmentStatus status) {
        this.status = status;
    }

    public String getCarrier() {
        return carrier;
    }

    public void setCarrier(String carrier) {
        this.carrier = carrier;
    }

    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    public BigDecimal getShippingCost() {
        return shippingCost;
    }

    public void setShippingCost(BigDecimal shippingCost) {
        this.shippingCost = shippingCost;
    }

    public BigDecimal getTotalWeight() {
        return totalWeight;
    }

    public void setTotalWeight(BigDecimal totalWeight) {
        this.totalWeight = totalWeight;
    }

    public String getWeightUnit() {
        return weightUnit;
    }

    public void setWeightUnit(String weightUnit) {
        this.weightUnit = weightUnit;
    }

    public String getRecipientName() {
        return recipientName;
    }

    public void setRecipientName(String recipientName) {
        this.recipientName = recipientName;
    }

    public String getRecipientCompany() {
        return recipientCompany;
    }

    public void setRecipientCompany(String recipientCompany) {
        this.recipientCompany = recipientCompany;
    }

    public String getAddressLine1() {
        return addressLine1;
    }

    public void setAddressLine1(String addressLine1) {
        this.addressLine1 = addressLine1;
    }

    public String getAddressLine2() {
        return addressLine2;
    }

    public void setAddressLine2(String addressLine2) {
        this.addressLine2 = addressLine2;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getStateProvince() {
        return stateProvince;
    }

    public void setStateProvince(String stateProvince) {
        this.stateProvince = stateProvince;
    }

    public String getPostalCode() {
        return postalCode;
    }

    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public LocalDateTime getEstimatedDeliveryDate() {
        return estimatedDeliveryDate;
    }

    public void setEstimatedDeliveryDate(LocalDateTime estimatedDeliveryDate) {
        this.estimatedDeliveryDate = estimatedDeliveryDate;
    }

    public LocalDateTime getActualDeliveryDate() {
        return actualDeliveryDate;
    }

    public void setActualDeliveryDate(LocalDateTime actualDeliveryDate) {
        this.actualDeliveryDate = actualDeliveryDate;
    }

    public LocalDateTime getShippedAt() {
        return shippedAt;
    }

    public void setShippedAt(LocalDateTime shippedAt) {
        this.shippedAt = shippedAt;
    }

    public LocalDateTime getDeliveredAt() {
        return deliveredAt;
    }

    public void setDeliveredAt(LocalDateTime deliveredAt) {
        this.deliveredAt = deliveredAt;
    }

    public String getSpecialInstructions() {
        return specialInstructions;
    }

    public void setSpecialInstructions(String specialInstructions) {
        this.specialInstructions = specialInstructions;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public List<ShipmentTracking> getTrackingEvents() {
        return trackingEvents;
    }

    public void setTrackingEvents(List<ShipmentTracking> trackingEvents) {
        this.trackingEvents = trackingEvents;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
}

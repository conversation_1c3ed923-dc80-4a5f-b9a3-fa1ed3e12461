package com.nttdata.ndvn.order.domain.model;

/**
 * Enumeration representing the various states of shipment processing.
 * 
 * This enum defines the shipment state machine and supports complex
 * shipping workflows including preparation, transit, and delivery.
 */
public enum ShipmentStatus {
    
    /**
     * Shipment is being prepared.
     */
    PREPARING("Preparing", "Shipment is being prepared"),
    
    /**
     * Shipment is ready for pickup.
     */
    READY_FOR_PICKUP("Ready for Pickup", "Shipment ready for carrier pickup"),
    
    /**
     * Shipment has been picked up by carrier.
     */
    PICKED_UP("Picked Up", "Shipment picked up by carrier"),
    
    /**
     * Shipment is in transit.
     */
    IN_TRANSIT("In Transit", "Shipment is in transit"),
    
    /**
     * Shipment is out for delivery.
     */
    OUT_FOR_DELIVERY("Out for Delivery", "Shipment out for delivery"),
    
    /**
     * Shipment has been delivered.
     */
    DELIVERED("Delivered", "Shipment delivered successfully"),
    
    /**
     * Delivery attempt failed.
     */
    DELIVERY_FAILED("Delivery Failed", "Delivery attempt failed"),
    
    /**
     * Shipment has been returned to sender.
     */
    RETURNED_TO_SENDER("Returned to Sender", "Shipment returned to sender"),
    
    /**
     * Shipment is lost or missing.
     */
    LOST("Lost", "Shipment is lost or missing"),
    
    /**
     * Shipment has been damaged.
     */
    DAMAGED("Damaged", "Shipment has been damaged"),
    
    /**
     * Shipment has been cancelled.
     */
    CANCELLED("Cancelled", "Shipment cancelled");
    
    private final String displayName;
    private final String description;
    
    ShipmentStatus(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * Checks if the shipment status allows tracking updates.
     * 
     * @return true if tracking updates can be added in this status
     */
    public boolean allowsTracking() {
        return this != CANCELLED && this != RETURNED_TO_SENDER && this != LOST;
    }
    
    /**
     * Checks if the shipment status allows cancellation.
     * 
     * @return true if the shipment can be cancelled in this status
     */
    public boolean allowsCancellation() {
        return this == PREPARING || this == READY_FOR_PICKUP;
    }
    
    /**
     * Checks if the shipment status represents a completed delivery.
     * 
     * @return true if the shipment has been delivered
     */
    public boolean isDelivered() {
        return this == DELIVERED;
    }
    
    /**
     * Checks if the shipment status represents a final state.
     * 
     * @return true if the shipment is in a final state
     */
    public boolean isFinal() {
        return this == DELIVERED || this == RETURNED_TO_SENDER || 
               this == LOST || this == DAMAGED || this == CANCELLED;
    }
    
    /**
     * Checks if the shipment status indicates a problem.
     *
     * @return true if there's an issue with the shipment
     */
    public boolean hasIssue() {
        return this == DELIVERY_FAILED || this == LOST || this == DAMAGED;
    }

    /**
     * Checks if the shipment status allows shipping operations.
     *
     * @return true if shipping operations can be performed in this status
     */
    public boolean allowsShipping() {
        return this == PREPARING || this == READY_FOR_PICKUP;
    }
}

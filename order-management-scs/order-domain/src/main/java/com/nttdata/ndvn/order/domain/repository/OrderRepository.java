package com.nttdata.ndvn.order.domain.repository;

import com.nttdata.ndvn.order.domain.model.Order;
import com.nttdata.ndvn.order.domain.model.OrderStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for Order aggregate root.
 * 
 * This interface defines the contract for order data access operations
 * within the order domain. It follows the Repository pattern and provides
 * domain-specific query methods.
 */
public interface OrderRepository {
    
    /**
     * Saves an order.
     * 
     * @param order the order to save
     * @return the saved order
     */
    Order save(Order order);
    
    /**
     * Finds an order by its ID.
     *
     * @param id the order ID
     * @return the order if found
     */
    Optional<Order> findById(UUID id);

    /**
     * Finds an order by its ID with all related entities loaded.
     *
     * @param id the order ID
     * @return the order with all relations if found
     */
    Optional<Order> findByIdWithAllRelations(UUID id);
    
    /**
     * Finds an order by its order number.
     * 
     * @param orderNumber the order number
     * @return the order if found
     */
    Optional<Order> findByOrderNumber(String orderNumber);
    
    /**
     * Finds all orders for a specific customer.
     * 
     * @param customerId the customer ID
     * @param pageable pagination information
     * @return page of orders
     */
    Page<Order> findByCustomerId(UUID customerId, Pageable pageable);
    
    /**
     * Finds all orders for a specific user.
     * 
     * @param userId the user ID
     * @param pageable pagination information
     * @return page of orders
     */
    Page<Order> findByUserId(UUID userId, Pageable pageable);
    
    /**
     * Finds orders by status.
     * 
     * @param status the order status
     * @param pageable pagination information
     * @return page of orders
     */
    Page<Order> findByStatus(OrderStatus status, Pageable pageable);
    
    /**
     * Finds orders by multiple statuses.
     * 
     * @param statuses the order statuses
     * @param pageable pagination information
     * @return page of orders
     */
    Page<Order> findByStatusIn(List<OrderStatus> statuses, Pageable pageable);
    
    /**
     * Finds orders created within a date range.
     * 
     * @param startDate the start date
     * @param endDate the end date
     * @param pageable pagination information
     * @return page of orders
     */
    Page<Order> findByCreatedAtBetween(LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);
    
    /**
     * Finds orders for a customer with specific status.
     * 
     * @param customerId the customer ID
     * @param status the order status
     * @param pageable pagination information
     * @return page of orders
     */
    Page<Order> findByCustomerIdAndStatus(UUID customerId, OrderStatus status, Pageable pageable);
    
    /**
     * Finds orders for a customer within a date range.
     * 
     * @param customerId the customer ID
     * @param startDate the start date
     * @param endDate the end date
     * @param pageable pagination information
     * @return page of orders
     */
    Page<Order> findByCustomerIdAndCreatedAtBetween(UUID customerId, LocalDateTime startDate, 
                                                   LocalDateTime endDate, Pageable pageable);
    
    /**
     * Finds orders that need processing (paid status).
     * 
     * @param pageable pagination information
     * @return page of orders ready for processing
     */
    Page<Order> findOrdersReadyForProcessing(Pageable pageable);
    
    /**
     * Finds orders that need fulfillment.
     * 
     * @param pageable pagination information
     * @return page of orders ready for fulfillment
     */
    Page<Order> findOrdersReadyForFulfillment(Pageable pageable);
    
    /**
     * Finds orders that need shipping.
     * 
     * @param pageable pagination information
     * @return page of orders ready for shipping
     */
    Page<Order> findOrdersReadyForShipping(Pageable pageable);
    
    /**
     * Finds abandoned orders (draft status older than specified hours).
     * 
     * @param hoursAgo the number of hours ago
     * @return list of abandoned orders
     */
    List<Order> findAbandonedOrders(int hoursAgo);
    
    /**
     * Counts orders by status.
     * 
     * @param status the order status
     * @return count of orders
     */
    long countByStatus(OrderStatus status);
    
    /**
     * Counts orders for a customer.
     * 
     * @param customerId the customer ID
     * @return count of orders
     */
    long countByCustomerId(UUID customerId);
    
    /**
     * Checks if an order exists by order number.
     * 
     * @param orderNumber the order number
     * @return true if order exists
     */
    boolean existsByOrderNumber(String orderNumber);
    
    /**
     * Deletes an order by ID.
     * 
     * @param id the order ID
     */
    void deleteById(UUID id);
    
    /**
     * Finds all orders.
     * 
     * @param pageable pagination information
     * @return page of all orders
     */
    Page<Order> findAll(Pageable pageable);
}

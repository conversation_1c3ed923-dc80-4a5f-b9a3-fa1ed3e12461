package com.nttdata.ndvn.order.domain.saga;

import com.nttdata.ndvn.order.domain.model.Order;
import com.nttdata.ndvn.order.domain.model.OrderStatus;
import com.nttdata.ndvn.order.domain.repository.OrderRepository;
import com.nttdata.ndvn.shared.infrastructure.saga.SagaContext;
import com.nttdata.ndvn.shared.infrastructure.saga.SagaStep;
import com.nttdata.ndvn.shared.infrastructure.saga.SagaStepResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Saga step for creating shipment in order workflow.
 * 
 * This step handles shipment creation and maintains compensation
 * logic for shipment cancellation in case of saga failure.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-28
 */
@Component
public class CreateShipmentStep extends SagaStep {
    
    private static final Logger logger = LoggerFactory.getLogger(CreateShipmentStep.class);
    
    private final OrderRepository orderRepository;
    // Note: In real implementation, inject ShippingServiceClient here
    
    public CreateShipmentStep(OrderRepository orderRepository) {
        super("CREATE_SHIPMENT", 4);
        this.orderRepository = orderRepository;
    }
    
    @Override
    public SagaStepResult execute(SagaContext context) {
        try {
            UUID orderId = context.getData("orderId");
            String paymentId = context.getData("paymentId");
            
            logger.info("Creating shipment for order: {} paymentId: {}", orderId, paymentId);
            
            // Get order
            Order order = orderRepository.findById(orderId)
                    .orElseThrow(() -> new IllegalArgumentException("Order not found: " + orderId));
            
            // Create shipment
            boolean shipmentSuccess = createShipment(order);
            
            if (shipmentSuccess) {
                // Update order status
                order.updateStatus(OrderStatus.SHIPPED);
                orderRepository.save(order);
                
                // Store shipment details for compensation
                String shipmentId = generateShipmentId();
                String trackingNumber = generateTrackingNumber();
                
                Map<String, Object> stepData = new HashMap<>();
                stepData.put("shipmentId", shipmentId);
                stepData.put("trackingNumber", trackingNumber);
                stepData.put("carrier", "FedEx");
                stepData.put("createdAt", System.currentTimeMillis());
                
                // Add to context for next steps
                context.addData("shipmentId", shipmentId);
                context.addData("trackingNumber", trackingNumber);
                context.addData("shipmentCreated", true);
                
                logger.info("Shipment created successfully for order: {} shipmentId: {} trackingNumber: {}", 
                        orderId, shipmentId, trackingNumber);
                
                return SagaStepResult.success(stepData);
            } else {
                logger.warn("Shipment creation failed for order: {}", orderId);
                return SagaStepResult.failure("Shipment creation failed");
            }
            
        } catch (Exception e) {
            logger.error("Error during shipment creation", e);
            return SagaStepResult.failure("Shipment creation error: " + e.getMessage());
        }
    }
    
    @Override
    public SagaStepResult compensate(SagaContext context) {
        try {
            UUID orderId = context.getData("orderId");
            String shipmentId = context.getData("shipmentId");
            
            logger.info("Compensating shipment creation for order: {} shipmentId: {}", orderId, shipmentId);
            
            if (shipmentId != null) {
                // Cancel shipment
                boolean cancellationSuccess = cancelShipment(shipmentId);
                
                if (cancellationSuccess) {
                    // Update order status back
                    Order order = orderRepository.findById(orderId)
                            .orElseThrow(() -> new IllegalArgumentException("Order not found: " + orderId));
                    
                    order.updateStatus(OrderStatus.PAID);
                    orderRepository.save(order);
                    
                    // Remove shipment data from context
                    context.addData("shipmentId", null);
                    context.addData("trackingNumber", null);
                    context.addData("shipmentCreated", false);
                    
                    logger.info("Shipment compensation successful for order: {} shipmentId: {}", 
                            orderId, shipmentId);
                    
                    return SagaStepResult.success();
                } else {
                    logger.warn("Shipment cancellation failed for order: {} shipmentId: {}", 
                            orderId, shipmentId);
                    return SagaStepResult.failure("Shipment cancellation failed");
                }
            } else {
                logger.warn("No shipment ID found for compensation");
                return SagaStepResult.success();
            }
            
        } catch (Exception e) {
            logger.error("Error during shipment compensation", e);
            return SagaStepResult.failure("Shipment compensation error: " + e.getMessage());
        }
    }
    
    /**
     * Create shipment for the order.
     * In real implementation, this would call the shipping service.
     */
    private boolean createShipment(Order order) {
        // Simulate shipment creation logic
        // In real implementation, call ShippingServiceClient.createShipment() here
        
        // For demo purposes, assume shipment creation succeeds for orders with total amount > 50
        return order.getTotalAmount().compareTo(java.math.BigDecimal.valueOf(50)) > 0;
    }
    
    /**
     * Cancel shipment.
     * In real implementation, this would call the shipping service.
     */
    private boolean cancelShipment(String shipmentId) {
        // Simulate shipment cancellation logic
        // In real implementation, call ShippingServiceClient.cancelShipment() here
        
        // For demo purposes, assume cancellation always succeeds
        return true;
    }
    
    /**
     * Generate a unique shipment ID.
     */
    private String generateShipmentId() {
        return "SHIP-" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }
    
    /**
     * Generate a tracking number.
     */
    private String generateTrackingNumber() {
        return "TRK-" + System.currentTimeMillis() + "-" + 
               UUID.randomUUID().toString().substring(0, 4).toUpperCase();
    }
}

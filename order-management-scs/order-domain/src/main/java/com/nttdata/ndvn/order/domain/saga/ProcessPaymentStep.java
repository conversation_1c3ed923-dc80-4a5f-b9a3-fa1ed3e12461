package com.nttdata.ndvn.order.domain.saga;

import com.nttdata.ndvn.order.domain.model.Order;
import com.nttdata.ndvn.order.domain.model.OrderStatus;
import com.nttdata.ndvn.order.domain.model.PaymentMethod;
import com.nttdata.ndvn.order.domain.repository.OrderRepository;
import com.nttdata.ndvn.shared.infrastructure.saga.SagaContext;
import com.nttdata.ndvn.shared.infrastructure.saga.SagaStep;
import com.nttdata.ndvn.shared.infrastructure.saga.SagaStepResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Saga step for processing payment in order workflow.
 * 
 * This step handles payment processing and maintains compensation
 * logic for payment reversal in case of saga failure.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-28
 */
@Component
public class ProcessPaymentStep extends SagaStep {
    
    private static final Logger logger = LoggerFactory.getLogger(ProcessPaymentStep.class);
    
    private final OrderRepository orderRepository;
    // Note: In real implementation, inject PaymentServiceClient here
    
    public ProcessPaymentStep(OrderRepository orderRepository) {
        super("PROCESS_PAYMENT", 3);
        this.orderRepository = orderRepository;
    }
    
    @Override
    public SagaStepResult execute(SagaContext context) {
        try {
            UUID orderId = context.getData("orderId");
            PaymentMethod paymentMethod = PaymentMethod.fromString(context.getData("paymentMethod", "CREDIT_CARD"));
            
            logger.info("Processing payment for order: {} method: {}", orderId, paymentMethod);
            
            // Get order
            Order order = orderRepository.findById(orderId)
                    .orElseThrow(() -> new IllegalArgumentException("Order not found: " + orderId));
            
            // Simulate payment processing
            // In real implementation, call PaymentServiceClient here
            boolean paymentSuccess = processPayment(order, paymentMethod);
            
            if (paymentSuccess) {
                // Update order status
                order.updateStatus(OrderStatus.PAID);
                orderRepository.save(order);
                
                // Store payment details for compensation
                Map<String, Object> stepData = new HashMap<>();
                stepData.put("paymentId", generatePaymentId());
                stepData.put("amount", order.getTotalAmount());
                stepData.put("paymentMethod", paymentMethod);
                
                // Add to context for next steps
                context.addData("paymentId", stepData.get("paymentId"));
                context.addData("paymentCompleted", true);
                
                logger.info("Payment processed successfully for order: {} paymentId: {}", 
                        orderId, stepData.get("paymentId"));
                
                return SagaStepResult.success(stepData);
                
            } else {
                logger.error("Payment processing failed for order: {}", orderId);
                return SagaStepResult.failure("Payment processing failed");
            }
            
        } catch (Exception e) {
            logger.error("Error processing payment", e);
            return SagaStepResult.failure("Payment processing error: " + e.getMessage());
        }
    }
    
    @Override
    public SagaStepResult compensate(SagaContext context) {
        try {
            UUID orderId = context.getData("orderId");
            String paymentId = context.getData("paymentId");
            
            logger.info("Compensating payment for order: {} paymentId: {}", orderId, paymentId);
            
            if (paymentId != null) {
                // Simulate payment reversal
                // In real implementation, call PaymentServiceClient.refundPayment() here
                boolean refundSuccess = refundPayment(paymentId);
                
                if (refundSuccess) {
                    // Update order status back
                    Order order = orderRepository.findById(orderId)
                            .orElseThrow(() -> new IllegalArgumentException("Order not found: " + orderId));
                    
                    order.updateStatus(OrderStatus.CONFIRMED);
                    orderRepository.save(order);
                    
                    // Remove payment data from context
                    context.addData("paymentId", null);
                    context.addData("paymentCompleted", false);
                    
                    logger.info("Payment compensation successful for order: {} paymentId: {}", 
                            orderId, paymentId);
                    
                    return SagaStepResult.success();
                    
                } else {
                    logger.error("Payment refund failed for order: {} paymentId: {}", orderId, paymentId);
                    return SagaStepResult.failure("Payment refund failed");
                }
            } else {
                // No payment to compensate
                logger.info("No payment to compensate for order: {}", orderId);
                return SagaStepResult.success();
            }
            
        } catch (Exception e) {
            logger.error("Error compensating payment", e);
            return SagaStepResult.failure("Payment compensation error: " + e.getMessage());
        }
    }
    
    /**
     * Simulate payment processing.
     * In real implementation, this would call the payment service.
     */
    private boolean processPayment(Order order, PaymentMethod paymentMethod) {
        // Simulate payment processing logic
        // Return true for success, false for failure
        
        // For demo purposes, assume payment succeeds for orders with even-numbered total amounts
        return order.getTotalAmount().intValue() % 2 == 0;
    }
    
    /**
     * Simulate payment refund.
     * In real implementation, this would call the payment service.
     */
    private boolean refundPayment(String paymentId) {
        // Simulate refund logic
        // For demo purposes, assume refund always succeeds
        return true;
    }
    
    /**
     * Generate a mock payment ID.
     */
    private String generatePaymentId() {
        return "PAY-" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }
}

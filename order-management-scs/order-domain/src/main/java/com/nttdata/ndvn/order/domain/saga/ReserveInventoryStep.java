package com.nttdata.ndvn.order.domain.saga;

import com.nttdata.ndvn.order.domain.model.Order;
import com.nttdata.ndvn.order.domain.model.OrderItem;
import com.nttdata.ndvn.order.domain.repository.OrderRepository;
import com.nttdata.ndvn.shared.infrastructure.saga.SagaContext;
import com.nttdata.ndvn.shared.infrastructure.saga.SagaStep;
import com.nttdata.ndvn.shared.infrastructure.saga.SagaStepResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Saga step for reserving inventory in order workflow.
 * 
 * This step handles inventory reservation and maintains compensation
 * logic for inventory release in case of saga failure.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-28
 */
@Component
public class ReserveInventoryStep extends SagaStep {
    
    private static final Logger logger = LoggerFactory.getLogger(ReserveInventoryStep.class);
    
    private final OrderRepository orderRepository;
    // Note: In real implementation, inject InventoryServiceClient here
    
    public ReserveInventoryStep(OrderRepository orderRepository) {
        super("RESERVE_INVENTORY", 2);
        this.orderRepository = orderRepository;
    }
    
    @Override
    public SagaStepResult execute(SagaContext context) {
        try {
            UUID orderId = context.getData("orderId");
            
            logger.info("Reserving inventory for order: {}", orderId);
            
            // Get order
            Order order = orderRepository.findById(orderId)
                    .orElseThrow(() -> new IllegalArgumentException("Order not found: " + orderId));
            
            // Reserve inventory for each order item
            Map<String, Object> reservationData = new HashMap<>();
            boolean allReservationsSuccessful = true;
            
            for (OrderItem item : order.getOrderItems()) {
                boolean reservationSuccess = reserveInventoryForItem(item);
                
                if (reservationSuccess) {
                    String reservationId = generateReservationId();
                    reservationData.put("reservation_" + item.getProductId(), reservationId);
                    logger.info("Inventory reserved for product: {} quantity: {} reservationId: {}", 
                            item.getProductId(), item.getQuantity(), reservationId);
                } else {
                    allReservationsSuccessful = false;
                    logger.warn("Failed to reserve inventory for product: {} quantity: {}", 
                            item.getProductId(), item.getQuantity());
                    break;
                }
            }
            
            if (allReservationsSuccessful) {
                // Store reservation details for compensation
                Map<String, Object> stepData = new HashMap<>();
                stepData.put("reservations", reservationData);
                stepData.put("reservedAt", System.currentTimeMillis());
                
                // Add to context for next steps
                context.addData("inventoryReserved", true);
                context.addData("reservations", reservationData);
                
                logger.info("Inventory reservation successful for order: {}", orderId);
                
                return SagaStepResult.success(stepData);
            } else {
                // Release any successful reservations
                releaseReservations(reservationData);
                logger.warn("Inventory reservation failed for order: {}", orderId);
                return SagaStepResult.failure("Inventory reservation failed");
            }
            
        } catch (Exception e) {
            logger.error("Error during inventory reservation", e);
            return SagaStepResult.failure("Inventory reservation error: " + e.getMessage());
        }
    }
    
    @Override
    public SagaStepResult compensate(SagaContext context) {
        try {
            UUID orderId = context.getData("orderId");
            Map<String, Object> reservations = context.getData("reservations");
            
            logger.info("Compensating inventory reservation for order: {}", orderId);
            
            if (reservations != null && !reservations.isEmpty()) {
                // Release all reservations
                releaseReservations(reservations);
                
                // Remove reservation data from context
                context.addData("inventoryReserved", false);
                context.addData("reservations", null);
                
                logger.info("Inventory reservation compensation successful for order: {}", orderId);
                
                return SagaStepResult.success();
            } else {
                logger.warn("No reservations found for compensation");
                return SagaStepResult.success();
            }
            
        } catch (Exception e) {
            logger.error("Error during inventory reservation compensation", e);
            return SagaStepResult.failure("Inventory reservation compensation error: " + e.getMessage());
        }
    }
    
    /**
     * Reserve inventory for a specific order item.
     * In real implementation, this would call the inventory service.
     */
    private boolean reserveInventoryForItem(OrderItem item) {
        // Simulate inventory reservation logic
        // In real implementation, call InventoryServiceClient.reserveStock() here
        
        // For demo purposes, assume reservation succeeds for products with even IDs
        return item.getProductId().toString().hashCode() % 2 == 0;
    }
    
    /**
     * Release inventory reservations.
     * In real implementation, this would call the inventory service.
     */
    private void releaseReservations(Map<String, Object> reservations) {
        // Simulate inventory release logic
        // In real implementation, call InventoryServiceClient.releaseReservation() here
        
        for (Map.Entry<String, Object> entry : reservations.entrySet()) {
            String reservationKey = entry.getKey();
            String reservationId = (String) entry.getValue();
            logger.info("Released reservation: {} id: {}", reservationKey, reservationId);
        }
    }
    
    /**
     * Generate a unique reservation ID.
     */
    private String generateReservationId() {
        return "RES-" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }
}

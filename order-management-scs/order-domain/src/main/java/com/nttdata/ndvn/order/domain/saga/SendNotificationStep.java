package com.nttdata.ndvn.order.domain.saga;

import com.nttdata.ndvn.order.domain.model.Order;
import com.nttdata.ndvn.order.domain.repository.OrderRepository;
import com.nttdata.ndvn.shared.infrastructure.saga.SagaContext;
import com.nttdata.ndvn.shared.infrastructure.saga.SagaStep;
import com.nttdata.ndvn.shared.infrastructure.saga.SagaStepResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Saga step for sending notifications in order workflow.
 * 
 * This step handles notification sending and maintains compensation
 * logic for notification reversal in case of saga failure.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-28
 */
@Component
public class SendNotificationStep extends SagaStep {
    
    private static final Logger logger = LoggerFactory.getLogger(SendNotificationStep.class);
    
    private final OrderRepository orderRepository;
    // Note: In real implementation, inject NotificationServiceClient here
    
    public SendNotificationStep(OrderRepository orderRepository) {
        super("SEND_NOTIFICATION", 5);
        this.orderRepository = orderRepository;
    }
    
    @Override
    public SagaStepResult execute(SagaContext context) {
        try {
            UUID orderId = context.getData("orderId");
            String trackingNumber = context.getData("trackingNumber");
            String shipmentId = context.getData("shipmentId");
            
            logger.info("Sending notifications for order: {} trackingNumber: {}", orderId, trackingNumber);
            
            // Get order
            Order order = orderRepository.findById(orderId)
                    .orElseThrow(() -> new IllegalArgumentException("Order not found: " + orderId));
            
            // Send notifications
            Map<String, String> notificationResults = sendNotifications(order, trackingNumber, shipmentId);
            
            boolean allNotificationsSent = notificationResults.values().stream()
                    .allMatch(result -> "SUCCESS".equals(result));
            
            if (allNotificationsSent) {
                // Store notification details for compensation
                Map<String, Object> stepData = new HashMap<>();
                stepData.put("notifications", notificationResults);
                stepData.put("sentAt", System.currentTimeMillis());
                
                // Add to context
                context.addData("notificationsSent", true);
                context.addData("notificationResults", notificationResults);
                
                logger.info("Notifications sent successfully for order: {} results: {}", 
                        orderId, notificationResults);
                
                return SagaStepResult.success(stepData);
            } else {
                logger.warn("Some notifications failed for order: {} results: {}", 
                        orderId, notificationResults);
                return SagaStepResult.failure("Some notifications failed to send");
            }
            
        } catch (Exception e) {
            logger.error("Error during notification sending", e);
            return SagaStepResult.failure("Notification sending error: " + e.getMessage());
        }
    }
    
    @Override
    public SagaStepResult compensate(SagaContext context) {
        try {
            UUID orderId = context.getData("orderId");
            Map<String, String> notificationResults = context.getData("notificationResults");
            
            logger.info("Compensating notifications for order: {}", orderId);
            
            if (notificationResults != null && !notificationResults.isEmpty()) {
                // Send cancellation notifications
                sendCancellationNotifications(orderId, notificationResults);
                
                // Remove notification data from context
                context.addData("notificationsSent", false);
                context.addData("notificationResults", null);
                
                logger.info("Notification compensation successful for order: {}", orderId);
                
                return SagaStepResult.success();
            } else {
                logger.warn("No notification results found for compensation");
                return SagaStepResult.success();
            }
            
        } catch (Exception e) {
            logger.error("Error during notification compensation", e);
            return SagaStepResult.failure("Notification compensation error: " + e.getMessage());
        }
    }
    
    /**
     * Send various notifications for the order.
     * In real implementation, this would call the notification service.
     */
    private Map<String, String> sendNotifications(Order order, String trackingNumber, String shipmentId) {
        Map<String, String> results = new HashMap<>();
        
        // Simulate sending different types of notifications
        // In real implementation, call NotificationServiceClient here
        
        // Email notification
        boolean emailSuccess = sendEmailNotification(order, trackingNumber);
        results.put("email", emailSuccess ? "SUCCESS" : "FAILED");
        
        // SMS notification
        boolean smsSuccess = sendSmsNotification(order, trackingNumber);
        results.put("sms", smsSuccess ? "SUCCESS" : "FAILED");
        
        // Push notification
        boolean pushSuccess = sendPushNotification(order, trackingNumber);
        results.put("push", pushSuccess ? "SUCCESS" : "FAILED");
        
        return results;
    }
    
    /**
     * Send cancellation notifications.
     * In real implementation, this would call the notification service.
     */
    private void sendCancellationNotifications(UUID orderId, Map<String, String> originalResults) {
        // Simulate sending cancellation notifications
        // In real implementation, call NotificationServiceClient.sendCancellationNotification() here
        
        for (String channel : originalResults.keySet()) {
            logger.info("Sent cancellation notification via {}: for order: {}", channel, orderId);
        }
    }
    
    /**
     * Send email notification.
     */
    private boolean sendEmailNotification(Order order, String trackingNumber) {
        // Simulate email sending
        // For demo purposes, assume email always succeeds
        logger.info("Sending email notification for order: {} to customer: {}", 
                order.getId(), order.getCustomerId());
        return true;
    }
    
    /**
     * Send SMS notification.
     */
    private boolean sendSmsNotification(Order order, String trackingNumber) {
        // Simulate SMS sending
        // For demo purposes, assume SMS succeeds for orders with even customer ID hash
        boolean success = order.getCustomerId().toString().hashCode() % 2 == 0;
        logger.info("Sending SMS notification for order: {} to customer: {} success: {}", 
                order.getId(), order.getCustomerId(), success);
        return success;
    }
    
    /**
     * Send push notification.
     */
    private boolean sendPushNotification(Order order, String trackingNumber) {
        // Simulate push notification sending
        // For demo purposes, assume push notification always succeeds
        logger.info("Sending push notification for order: {} to customer: {}", 
                order.getId(), order.getCustomerId());
        return true;
    }
}

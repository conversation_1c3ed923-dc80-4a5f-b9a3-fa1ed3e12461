package com.nttdata.ndvn.order.domain.saga;

import com.nttdata.ndvn.order.domain.model.Order;
import com.nttdata.ndvn.order.domain.model.OrderStatus;
import com.nttdata.ndvn.order.domain.repository.OrderRepository;
import com.nttdata.ndvn.shared.infrastructure.saga.SagaContext;
import com.nttdata.ndvn.shared.infrastructure.saga.SagaStep;
import com.nttdata.ndvn.shared.infrastructure.saga.SagaStepResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Saga step for validating order in order workflow.
 * 
 * This step handles order validation and maintains compensation
 * logic for order status reversal in case of saga failure.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-28
 */
@Component
public class ValidateOrderStep extends SagaStep {
    
    private static final Logger logger = LoggerFactory.getLogger(ValidateOrderStep.class);
    
    private final OrderRepository orderRepository;
    
    public ValidateOrderStep(OrderRepository orderRepository) {
        super("VALIDATE_ORDER", 1);
        this.orderRepository = orderRepository;
    }
    
    @Override
    public SagaStepResult execute(SagaContext context) {
        try {
            UUID orderId = context.getData("orderId");
            
            logger.info("Validating order: {}", orderId);
            
            // Get order
            Order order = orderRepository.findById(orderId)
                    .orElseThrow(() -> new IllegalArgumentException("Order not found: " + orderId));
            
            // Validate order
            boolean validationSuccess = validateOrder(order);
            
            if (validationSuccess) {
                // Update order status
                order.updateStatus(OrderStatus.CONFIRMED);
                orderRepository.save(order);
                
                // Store validation details for compensation
                Map<String, Object> stepData = new HashMap<>();
                stepData.put("validatedAt", System.currentTimeMillis());
                stepData.put("originalStatus", OrderStatus.PENDING.name());
                
                // Add to context for next steps
                context.addData("orderValidated", true);
                context.addData("originalStatus", OrderStatus.PENDING.name());
                
                logger.info("Order validation successful for order: {}", orderId);
                
                return SagaStepResult.success(stepData);
            } else {
                logger.warn("Order validation failed for order: {}", orderId);
                return SagaStepResult.failure("Order validation failed");
            }
            
        } catch (Exception e) {
            logger.error("Error during order validation", e);
            return SagaStepResult.failure("Order validation error: " + e.getMessage());
        }
    }
    
    @Override
    public SagaStepResult compensate(SagaContext context) {
        try {
            UUID orderId = context.getData("orderId");
            String originalStatus = context.getData("originalStatus", OrderStatus.PENDING.name());
            
            logger.info("Compensating order validation for order: {}", orderId);
            
            if (orderId != null) {
                // Revert order status
                Order order = orderRepository.findById(orderId)
                        .orElseThrow(() -> new IllegalArgumentException("Order not found: " + orderId));
                
                order.updateStatus(OrderStatus.valueOf(originalStatus));
                orderRepository.save(order);
                
                // Remove validation data from context
                context.addData("orderValidated", false);
                context.addData("originalStatus", null);
                
                logger.info("Order validation compensation successful for order: {}", orderId);
                
                return SagaStepResult.success();
            } else {
                logger.warn("No order ID found for compensation");
                return SagaStepResult.success();
            }
            
        } catch (Exception e) {
            logger.error("Error during order validation compensation", e);
            return SagaStepResult.failure("Order validation compensation error: " + e.getMessage());
        }
    }
    
    /**
     * Validate order business rules.
     * In real implementation, this would perform comprehensive validation.
     */
    private boolean validateOrder(Order order) {
        // Basic validation rules
        if (order.getOrderItems() == null || order.getOrderItems().isEmpty()) {
            return false;
        }
        
        if (order.getTotalAmount() == null || order.getTotalAmount().compareTo(java.math.BigDecimal.ZERO) <= 0) {
            return false;
        }
        
        if (order.getCustomerId() == null) {
            return false;
        }
        
        // Additional business rules can be added here
        return true;
    }
}

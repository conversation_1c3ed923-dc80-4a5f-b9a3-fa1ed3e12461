package com.nttdata.ndvn.order.domain.service;

import com.nttdata.ndvn.order.domain.model.Order;
import com.nttdata.ndvn.order.domain.model.OrderItem;
import com.nttdata.ndvn.order.domain.model.Payment;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * Domain service for order validation business logic.
 * 
 * This service encapsulates complex validation rules that span multiple
 * entities or require domain expertise beyond simple field validation.
 */
@Service
public class OrderValidationService {
    
    /**
     * Validates an order for confirmation.
     * 
     * @param order the order to validate
     * @return list of validation errors (empty if valid)
     */
    public List<String> validateOrderForConfirmation(Order order) {
        List<String> errors = new ArrayList<>();
        
        // Check if order has items
        if (order.getItems() == null || order.getItems().isEmpty()) {
            errors.add("Order must contain at least one item");
        }
        
        // Validate order items
        if (order.getItems() != null) {
            for (int i = 0; i < order.getItems().size(); i++) {
                OrderItem item = order.getItems().get(i);
                List<String> itemErrors = validateOrderItem(item, i + 1);
                errors.addAll(itemErrors);
            }
        }
        
        // Validate order amounts
        errors.addAll(validateOrderAmounts(order));
        
        // Validate billing information if required
        if (order.getTotalAmount().compareTo(BigDecimal.ZERO) > 0) {
            errors.addAll(validateBillingInformation(order));
        }
        
        return errors;
    }
    
    /**
     * Validates an order item.
     * 
     * @param item the order item to validate
     * @param itemNumber the item number for error messages
     * @return list of validation errors
     */
    public List<String> validateOrderItem(OrderItem item, int itemNumber) {
        List<String> errors = new ArrayList<>();
        String prefix = "Item " + itemNumber + ": ";
        
        if (item.getQuantity() == null || item.getQuantity() <= 0) {
            errors.add(prefix + "Quantity must be greater than 0");
        }
        
        if (item.getUnitPrice() == null || item.getUnitPrice().compareTo(BigDecimal.ZERO) < 0) {
            errors.add(prefix + "Unit price must be non-negative");
        }
        
        if (item.getProductSku() == null || item.getProductSku().trim().isEmpty()) {
            errors.add(prefix + "Product SKU is required");
        }
        
        if (item.getProductName() == null || item.getProductName().trim().isEmpty()) {
            errors.add(prefix + "Product name is required");
        }
        
        // Validate discount amount
        if (item.getDiscountAmount() != null && item.getDiscountAmount().compareTo(BigDecimal.ZERO) < 0) {
            errors.add(prefix + "Discount amount cannot be negative");
        }
        
        // Validate that discount doesn't exceed subtotal
        if (item.getDiscountAmount() != null && item.getUnitPrice() != null && item.getQuantity() != null) {
            BigDecimal subtotal = item.getUnitPrice().multiply(BigDecimal.valueOf(item.getQuantity()));
            if (item.getDiscountAmount().compareTo(subtotal) > 0) {
                errors.add(prefix + "Discount amount cannot exceed item subtotal");
            }
        }
        
        return errors;
    }
    
    /**
     * Validates order amounts for consistency.
     * 
     * @param order the order to validate
     * @return list of validation errors
     */
    public List<String> validateOrderAmounts(Order order) {
        List<String> errors = new ArrayList<>();
        
        // Calculate expected subtotal from items
        BigDecimal expectedSubtotal = BigDecimal.ZERO;
        if (order.getItems() != null) {
            for (OrderItem item : order.getItems()) {
                if (item.getTotalAmount() != null) {
                    expectedSubtotal = expectedSubtotal.add(item.getTotalAmount());
                }
            }
        }
        
        // Validate subtotal matches items
        if (order.getSubtotal() != null && 
            order.getSubtotal().compareTo(expectedSubtotal) != 0) {
            errors.add("Order subtotal does not match sum of item totals");
        }
        
        // Validate total amount calculation
        BigDecimal expectedTotal = (order.getSubtotal() != null ? order.getSubtotal() : BigDecimal.ZERO)
            .add(order.getTaxAmount() != null ? order.getTaxAmount() : BigDecimal.ZERO)
            .add(order.getShippingAmount() != null ? order.getShippingAmount() : BigDecimal.ZERO)
            .subtract(order.getDiscountAmount() != null ? order.getDiscountAmount() : BigDecimal.ZERO);
        
        if (expectedTotal.compareTo(BigDecimal.ZERO) < 0) {
            expectedTotal = BigDecimal.ZERO;
        }
        
        if (order.getTotalAmount() != null && 
            order.getTotalAmount().compareTo(expectedTotal) != 0) {
            errors.add("Order total amount is incorrect");
        }
        
        // Validate amounts are non-negative
        if (order.getTaxAmount() != null && order.getTaxAmount().compareTo(BigDecimal.ZERO) < 0) {
            errors.add("Tax amount cannot be negative");
        }
        
        if (order.getShippingAmount() != null && order.getShippingAmount().compareTo(BigDecimal.ZERO) < 0) {
            errors.add("Shipping amount cannot be negative");
        }
        
        if (order.getDiscountAmount() != null && order.getDiscountAmount().compareTo(BigDecimal.ZERO) < 0) {
            errors.add("Discount amount cannot be negative");
        }
        
        return errors;
    }
    
    /**
     * Validates billing information.
     * 
     * @param order the order to validate
     * @return list of validation errors
     */
    public List<String> validateBillingInformation(Order order) {
        List<String> errors = new ArrayList<>();
        
        if (order.getBillingName() == null || order.getBillingName().trim().isEmpty()) {
            errors.add("Billing name is required");
        }
        
        if (order.getBillingAddressLine1() == null || order.getBillingAddressLine1().trim().isEmpty()) {
            errors.add("Billing address is required");
        }
        
        if (order.getBillingCity() == null || order.getBillingCity().trim().isEmpty()) {
            errors.add("Billing city is required");
        }
        
        if (order.getBillingPostalCode() == null || order.getBillingPostalCode().trim().isEmpty()) {
            errors.add("Billing postal code is required");
        }
        
        if (order.getBillingCountry() == null || order.getBillingCountry().trim().isEmpty()) {
            errors.add("Billing country is required");
        }
        
        return errors;
    }
    
    /**
     * Validates payment against order.
     * 
     * @param order the order
     * @param payment the payment to validate
     * @return list of validation errors
     */
    public List<String> validatePayment(Order order, Payment payment) {
        List<String> errors = new ArrayList<>();
        
        if (payment.getAmount() == null || payment.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            errors.add("Payment amount must be greater than 0");
        }
        
        if (payment.getAmount() != null && payment.getAmount().compareTo(order.getTotalAmount()) > 0) {
            errors.add("Payment amount cannot exceed order total");
        }
        
        if (payment.getCurrency() == null || !payment.getCurrency().equals(order.getCurrency())) {
            errors.add("Payment currency must match order currency");
        }
        
        if (payment.getPaymentMethod() == null) {
            errors.add("Payment method is required");
        }
        
        return errors;
    }
    
    /**
     * Validates if an order can be cancelled.
     * 
     * @param order the order to validate
     * @return list of validation errors
     */
    public List<String> validateOrderCancellation(Order order) {
        List<String> errors = new ArrayList<>();
        
        if (!order.getStatus().isCancellable()) {
            errors.add("Order cannot be cancelled in current status: " + order.getStatus().getDisplayName());
        }
        
        // Check if there are successful payments that would need refunding
        boolean hasSuccessfulPayments = order.getPayments().stream()
            .anyMatch(Payment::isSuccessful);
        
        if (hasSuccessfulPayments) {
            errors.add("Order has successful payments. Please process refunds before cancellation.");
        }
        
        return errors;
    }
    
    /**
     * Validates if an order can be modified.
     * 
     * @param order the order to validate
     * @return list of validation errors
     */
    public List<String> validateOrderModification(Order order) {
        List<String> errors = new ArrayList<>();
        
        if (!order.getStatus().isModifiable()) {
            errors.add("Order cannot be modified in current status: " + order.getStatus().getDisplayName());
        }
        
        return errors;
    }
}

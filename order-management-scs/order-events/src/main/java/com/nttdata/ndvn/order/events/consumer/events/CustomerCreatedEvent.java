package com.nttdata.ndvn.order.events.consumer.events;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.nttdata.ndvn.shared.events.BaseEvent;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Event consumed when a customer is created.
 * This event is published by the Customer Management SCS.
 */
public class CustomerCreatedEvent extends BaseEvent {
    
    @JsonProperty("customerId")
    private UUID customerId;

    @JsonProperty("customerNumber")
    private String customerNumber;

    @JsonProperty("firstName")
    private String firstName;

    @JsonProperty("lastName")
    private String lastName;

    @JsonProperty("email")
    private String email;

    @JsonProperty("phoneNumber")
    private String phoneNumber;

    @JsonProperty("status")
    private String status;

    @JsonProperty("createdAt")
    private LocalDateTime createdAt;

    @JsonProperty("customerType")
    private String customerType;

    // Default constructor
    public CustomerCreatedEvent() {
        super();
    }

    public CustomerCreatedEvent(UUID customerId, String customerNumber, String firstName,
                               String lastName, String email, String phoneNumber, String status,
                               LocalDateTime createdAt, String customerType) {
        super("CustomerCreated", "1.0", "customer-management-service");
        this.customerId = customerId;
        this.customerNumber = customerNumber;
        this.firstName = firstName;
        this.lastName = lastName;
        this.email = email;
        this.phoneNumber = phoneNumber;
        this.status = status;
        this.createdAt = createdAt;
        this.customerType = customerType;

        // Set aggregate context
        setAggregateContext(customerId.toString(), "Customer", 1L);
    }
    
    // Getters and setters
    public UUID getCustomerId() { return customerId; }
    public void setCustomerId(UUID customerId) { this.customerId = customerId; }

    public String getCustomerNumber() { return customerNumber; }
    public void setCustomerNumber(String customerNumber) { this.customerNumber = customerNumber; }

    public String getFirstName() { return firstName; }
    public void setFirstName(String firstName) { this.firstName = firstName; }

    public String getLastName() { return lastName; }
    public void setLastName(String lastName) { this.lastName = lastName; }

    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }

    public String getPhoneNumber() { return phoneNumber; }
    public void setPhoneNumber(String phoneNumber) { this.phoneNumber = phoneNumber; }

    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public String getCustomerType() { return customerType; }
    public void setCustomerType(String customerType) { this.customerType = customerType; }

    // Helper method for display name
    public String getDisplayName() {
        return firstName + " " + lastName;
    }
}

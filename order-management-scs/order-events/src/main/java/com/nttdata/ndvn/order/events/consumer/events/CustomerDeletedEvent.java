package com.nttdata.ndvn.order.events.consumer.events;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.nttdata.ndvn.shared.events.BaseEvent;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Event consumed when a customer is deleted.
 * This event is published by the Customer Management SCS.
 */
public class CustomerDeletedEvent extends BaseEvent {
    
    @JsonProperty("customerId")
    private UUID customerId;

    @JsonProperty("customerNumber")
    private String customerNumber;

    @JsonProperty("firstName")
    private String firstName;

    @JsonProperty("lastName")
    private String lastName;

    @JsonProperty("email")
    private String email;

    @JsonProperty("deletedAt")
    private LocalDateTime deletedAt;

    @JsonProperty("deletedBy")
    private String deletedBy;

    @JsonProperty("reason")
    private String reason;

    // Default constructor
    public CustomerDeletedEvent() {
        super();
    }

    public CustomerDeletedEvent(UUID customerId, String customerNumber, String firstName,
                               String lastName, String email, LocalDateTime deletedAt, String deletedBy,
                               String reason) {
        super("CustomerDeleted", "1.0", "customer-management-service");
        this.customerId = customerId;
        this.customerNumber = customerNumber;
        this.firstName = firstName;
        this.lastName = lastName;
        this.email = email;
        this.deletedAt = deletedAt;
        this.deletedBy = deletedBy;
        this.reason = reason;

        // Set aggregate context
        setAggregateContext(customerId.toString(), "Customer", 1L);
    }
    
    // Getters and setters
    public UUID getCustomerId() { return customerId; }
    public void setCustomerId(UUID customerId) { this.customerId = customerId; }

    public String getCustomerNumber() { return customerNumber; }
    public void setCustomerNumber(String customerNumber) { this.customerNumber = customerNumber; }

    public String getFirstName() { return firstName; }
    public void setFirstName(String firstName) { this.firstName = firstName; }

    public String getLastName() { return lastName; }
    public void setLastName(String lastName) { this.lastName = lastName; }

    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }

    public LocalDateTime getDeletedAt() { return deletedAt; }
    public void setDeletedAt(LocalDateTime deletedAt) { this.deletedAt = deletedAt; }

    public String getDeletedBy() { return deletedBy; }
    public void setDeletedBy(String deletedBy) { this.deletedBy = deletedBy; }

    public String getReason() { return reason; }
    public void setReason(String reason) { this.reason = reason; }
}

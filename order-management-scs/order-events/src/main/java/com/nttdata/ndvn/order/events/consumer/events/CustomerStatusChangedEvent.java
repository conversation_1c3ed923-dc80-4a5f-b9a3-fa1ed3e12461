package com.nttdata.ndvn.order.events.consumer.events;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.nttdata.ndvn.shared.events.BaseEvent;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Event consumed when a customer's status changes.
 * This event is published by the Customer Management SCS.
 */
public class CustomerStatusChangedEvent extends BaseEvent {
    
    @JsonProperty("customerId")
    private UUID customerId;

    @JsonProperty("customerNumber")
    private String customerNumber;

    @JsonProperty("previousStatus")
    private String previousStatus;

    @JsonProperty("newStatus")
    private String newStatus;

    @JsonProperty("reason")
    private String reason;

    @JsonProperty("changedAt")
    private LocalDateTime changedAt;

    @JsonProperty("changedBy")
    private String changedBy;

    // Default constructor
    public CustomerStatusChangedEvent() {
        super();
    }

    public CustomerStatusChangedEvent(UUID customerId, String customerNumber,
                                     String previousStatus, String newStatus, String reason,
                                     LocalDateTime changedAt, String changedBy) {
        super("CustomerStatusChanged", "1.0", "customer-management-service");
        this.customerId = customerId;
        this.customerNumber = customerNumber;
        this.previousStatus = previousStatus;
        this.newStatus = newStatus;
        this.reason = reason;
        this.changedAt = changedAt;
        this.changedBy = changedBy;

        // Set aggregate context
        setAggregateContext(customerId.toString(), "Customer", 1L);
    }
    
    // Getters and setters
    public UUID getCustomerId() { return customerId; }
    public void setCustomerId(UUID customerId) { this.customerId = customerId; }

    public String getCustomerNumber() { return customerNumber; }
    public void setCustomerNumber(String customerNumber) { this.customerNumber = customerNumber; }

    public String getPreviousStatus() { return previousStatus; }
    public void setPreviousStatus(String previousStatus) { this.previousStatus = previousStatus; }

    public String getNewStatus() { return newStatus; }
    public void setNewStatus(String newStatus) { this.newStatus = newStatus; }

    public String getReason() { return reason; }
    public void setReason(String reason) { this.reason = reason; }

    public LocalDateTime getChangedAt() { return changedAt; }
    public void setChangedAt(LocalDateTime changedAt) { this.changedAt = changedAt; }

    public String getChangedBy() { return changedBy; }
    public void setChangedBy(String changedBy) { this.changedBy = changedBy; }
}

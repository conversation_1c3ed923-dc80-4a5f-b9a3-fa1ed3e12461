package com.nttdata.ndvn.order.events.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * DTO for OrderCancelled events.
 */
public class OrderCancelledEventDto extends OrderEventDto {
    
    @JsonProperty("orderNumber")
    private String orderNumber;
    
    @JsonProperty("customerId")
    private UUID customerId;
    
    @JsonProperty("userId")
    private UUID userId;
    
    @JsonProperty("cancelledAt")
    private LocalDateTime cancelledAt;
    
    @JsonProperty("cancelledBy")
    private String cancelledBy;
    
    @JsonProperty("cancellationReason")
    private String cancellationReason;
    
    @JsonProperty("refundAmount")
    private BigDecimal refundAmount;
    
    @JsonProperty("currency")
    private String currency;
    
    @JsonProperty("refundMethod")
    private String refundMethod;
    
    @JsonProperty("refundStatus")
    private String refundStatus;
    
    @JsonProperty("originalOrderDate")
    private LocalDateTime originalOrderDate;
    
    @JsonProperty("orderStatus")
    private String orderStatus;
    
    // Constructors
    public OrderCancelledEventDto() {}
    
    public OrderCancelledEventDto(UUID eventId, UUID orderId, LocalDateTime occurredAt, String correlationId,
                                 String orderNumber, UUID customerId, UUID userId, LocalDateTime cancelledAt,
                                 String cancelledBy, String cancellationReason, BigDecimal refundAmount,
                                 String currency, String refundMethod, String refundStatus,
                                 LocalDateTime originalOrderDate, String orderStatus) {
        super(eventId, orderId, "OrderCancelled", occurredAt, correlationId);
        this.orderNumber = orderNumber;
        this.customerId = customerId;
        this.userId = userId;
        this.cancelledAt = cancelledAt;
        this.cancelledBy = cancelledBy;
        this.cancellationReason = cancellationReason;
        this.refundAmount = refundAmount;
        this.currency = currency;
        this.refundMethod = refundMethod;
        this.refundStatus = refundStatus;
        this.originalOrderDate = originalOrderDate;
        this.orderStatus = orderStatus;
    }
    
    // Getters and setters
    public String getOrderNumber() {
        return orderNumber;
    }
    
    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }
    
    public UUID getCustomerId() {
        return customerId;
    }
    
    public void setCustomerId(UUID customerId) {
        this.customerId = customerId;
    }
    
    public UUID getUserId() {
        return userId;
    }
    
    public void setUserId(UUID userId) {
        this.userId = userId;
    }
    
    public LocalDateTime getCancelledAt() {
        return cancelledAt;
    }
    
    public void setCancelledAt(LocalDateTime cancelledAt) {
        this.cancelledAt = cancelledAt;
    }
    
    public String getCancelledBy() {
        return cancelledBy;
    }
    
    public void setCancelledBy(String cancelledBy) {
        this.cancelledBy = cancelledBy;
    }
    
    public String getCancellationReason() {
        return cancellationReason;
    }
    
    public void setCancellationReason(String cancellationReason) {
        this.cancellationReason = cancellationReason;
    }
    
    public BigDecimal getRefundAmount() {
        return refundAmount;
    }
    
    public void setRefundAmount(BigDecimal refundAmount) {
        this.refundAmount = refundAmount;
    }
    
    public String getCurrency() {
        return currency;
    }
    
    public void setCurrency(String currency) {
        this.currency = currency;
    }
    
    public String getRefundMethod() {
        return refundMethod;
    }
    
    public void setRefundMethod(String refundMethod) {
        this.refundMethod = refundMethod;
    }
    
    public String getRefundStatus() {
        return refundStatus;
    }
    
    public void setRefundStatus(String refundStatus) {
        this.refundStatus = refundStatus;
    }
    
    public LocalDateTime getOriginalOrderDate() {
        return originalOrderDate;
    }
    
    public void setOriginalOrderDate(LocalDateTime originalOrderDate) {
        this.originalOrderDate = originalOrderDate;
    }
    
    public String getOrderStatus() {
        return orderStatus;
    }
    
    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }
}

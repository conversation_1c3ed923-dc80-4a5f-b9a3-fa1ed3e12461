package com.nttdata.ndvn.order.events.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * DTO for OrderDelivered events.
 */
public class OrderDeliveredEventDto extends OrderEventDto {
    
    @JsonProperty("shipmentId")
    private UUID shipmentId;
    
    @JsonProperty("orderNumber")
    private String orderNumber;
    
    @JsonProperty("customerId")
    private UUID customerId;
    
    @JsonProperty("trackingNumber")
    private String trackingNumber;
    
    @JsonProperty("carrier")
    private String carrier;
    
    @JsonProperty("recipientName")
    private String recipientName;
    
    @JsonProperty("deliveryAddress")
    private String deliveryAddress;
    
    @JsonProperty("deliveredAt")
    private LocalDateTime deliveredAt;
    
    @JsonProperty("deliveredBy")
    private String deliveredBy;
    
    @JsonProperty("signedBy")
    private String signedBy;
    
    @JsonProperty("deliveryNotes")
    private String deliveryNotes;
    
    // Constructors
    public OrderDeliveredEventDto() {}
    
    public OrderDeliveredEventDto(UUID eventId, UUID orderId, LocalDateTime occurredAt, String correlationId,
                                 UUID shipmentId, String orderNumber, UUID customerId, String trackingNumber,
                                 String carrier, String recipientName, String deliveryAddress,
                                 LocalDateTime deliveredAt, String deliveredBy, String signedBy, String deliveryNotes) {
        super(eventId, orderId, "OrderDelivered", occurredAt, correlationId);
        this.shipmentId = shipmentId;
        this.orderNumber = orderNumber;
        this.customerId = customerId;
        this.trackingNumber = trackingNumber;
        this.carrier = carrier;
        this.recipientName = recipientName;
        this.deliveryAddress = deliveryAddress;
        this.deliveredAt = deliveredAt;
        this.deliveredBy = deliveredBy;
        this.signedBy = signedBy;
        this.deliveryNotes = deliveryNotes;
    }
    
    // Getters and setters
    public UUID getShipmentId() {
        return shipmentId;
    }
    
    public void setShipmentId(UUID shipmentId) {
        this.shipmentId = shipmentId;
    }
    
    public String getOrderNumber() {
        return orderNumber;
    }
    
    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }
    
    public UUID getCustomerId() {
        return customerId;
    }
    
    public void setCustomerId(UUID customerId) {
        this.customerId = customerId;
    }
    
    public String getTrackingNumber() {
        return trackingNumber;
    }
    
    public void setTrackingNumber(String trackingNumber) {
        this.trackingNumber = trackingNumber;
    }
    
    public String getCarrier() {
        return carrier;
    }
    
    public void setCarrier(String carrier) {
        this.carrier = carrier;
    }
    
    public String getRecipientName() {
        return recipientName;
    }
    
    public void setRecipientName(String recipientName) {
        this.recipientName = recipientName;
    }
    
    public String getDeliveryAddress() {
        return deliveryAddress;
    }
    
    public void setDeliveryAddress(String deliveryAddress) {
        this.deliveryAddress = deliveryAddress;
    }
    
    public LocalDateTime getDeliveredAt() {
        return deliveredAt;
    }
    
    public void setDeliveredAt(LocalDateTime deliveredAt) {
        this.deliveredAt = deliveredAt;
    }
    
    public String getDeliveredBy() {
        return deliveredBy;
    }
    
    public void setDeliveredBy(String deliveredBy) {
        this.deliveredBy = deliveredBy;
    }
    
    public String getSignedBy() {
        return signedBy;
    }
    
    public void setSignedBy(String signedBy) {
        this.signedBy = signedBy;
    }
    
    public String getDeliveryNotes() {
        return deliveryNotes;
    }
    
    public void setDeliveryNotes(String deliveryNotes) {
        this.deliveryNotes = deliveryNotes;
    }
}

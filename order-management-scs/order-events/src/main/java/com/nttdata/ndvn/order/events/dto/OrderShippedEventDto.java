package com.nttdata.ndvn.order.events.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * DTO for OrderShipped events.
 */
public class OrderShippedEventDto extends OrderEventDto {
    
    @JsonProperty("shipmentId")
    private UUID shipmentId;
    
    @JsonProperty("orderNumber")
    private String orderNumber;
    
    @JsonProperty("customerId")
    private UUID customerId;
    
    @JsonProperty("trackingNumber")
    private String trackingNumber;
    
    @JsonProperty("carrier")
    private String carrier;
    
    @JsonProperty("serviceType")
    private String serviceType;
    
    @JsonProperty("recipientName")
    private String recipientName;
    
    @JsonProperty("recipientAddress")
    private String recipientAddress;
    
    @JsonProperty("estimatedDeliveryDate")
    private LocalDateTime estimatedDeliveryDate;
    
    // Constructors
    public OrderShippedEventDto() {}
    
    public OrderShippedEventDto(UUID eventId, UUID orderId, LocalDateTime occurredAt, String correlationId,
                               UUID shipmentId, String orderNumber, UUID customerId, String trackingNumber,
                               String carrier, String serviceType, String recipientName, String recipientAddress,
                               LocalDateTime estimatedDeliveryDate) {
        super(eventId, orderId, "OrderShipped", occurredAt, correlationId);
        this.shipmentId = shipmentId;
        this.orderNumber = orderNumber;
        this.customerId = customerId;
        this.trackingNumber = trackingNumber;
        this.carrier = carrier;
        this.serviceType = serviceType;
        this.recipientName = recipientName;
        this.recipientAddress = recipientAddress;
        this.estimatedDeliveryDate = estimatedDeliveryDate;
    }
    
    // Getters and setters
    public UUID getShipmentId() {
        return shipmentId;
    }
    
    public void setShipmentId(UUID shipmentId) {
        this.shipmentId = shipmentId;
    }
    
    public String getOrderNumber() {
        return orderNumber;
    }
    
    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }
    
    public UUID getCustomerId() {
        return customerId;
    }
    
    public void setCustomerId(UUID customerId) {
        this.customerId = customerId;
    }
    
    public String getTrackingNumber() {
        return trackingNumber;
    }
    
    public void setTrackingNumber(String trackingNumber) {
        this.trackingNumber = trackingNumber;
    }
    
    public String getCarrier() {
        return carrier;
    }
    
    public void setCarrier(String carrier) {
        this.carrier = carrier;
    }
    
    public String getServiceType() {
        return serviceType;
    }
    
    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }
    
    public String getRecipientName() {
        return recipientName;
    }
    
    public void setRecipientName(String recipientName) {
        this.recipientName = recipientName;
    }
    
    public String getRecipientAddress() {
        return recipientAddress;
    }
    
    public void setRecipientAddress(String recipientAddress) {
        this.recipientAddress = recipientAddress;
    }
    
    public LocalDateTime getEstimatedDeliveryDate() {
        return estimatedDeliveryDate;
    }
    
    public void setEstimatedDeliveryDate(LocalDateTime estimatedDeliveryDate) {
        this.estimatedDeliveryDate = estimatedDeliveryDate;
    }
}

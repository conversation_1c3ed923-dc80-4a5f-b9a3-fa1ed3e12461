package com.nttdata.ndvn.order.infrastructure.client;

import com.nttdata.ndvn.order.infrastructure.client.dto.PaymentRequest;
import com.nttdata.ndvn.order.infrastructure.client.dto.PaymentResponse;
import com.nttdata.ndvn.order.infrastructure.client.dto.RefundRequest;
import com.nttdata.ndvn.order.infrastructure.client.dto.RefundResponse;
import com.nttdata.ndvn.shared.infrastructure.client.BaseApiClient;
import com.nttdata.ndvn.shared.infrastructure.discovery.ServiceDiscoveryClient;
import com.nttdata.ndvn.shared.infrastructure.security.JwtTokenProvider;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import io.github.resilience4j.retry.annotation.Retry;
import io.github.resilience4j.timelimiter.annotation.TimeLimiter;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

import java.math.BigDecimal;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * Client for integrating with external payment processing services.
 *
 * This client handles communication with payment gateways and provides
 * resilient payment processing capabilities using the shared infrastructure framework.
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-28
 */
@Component
public class PaymentServiceClient extends BaseApiClient {

    private static final String SERVICE_NAME = "payment-service";

    public PaymentServiceClient(ServiceDiscoveryClient serviceDiscovery,
                               WebClient.Builder webClientBuilder,
                               JwtTokenProvider tokenProvider,
                               MeterRegistry meterRegistry) {
        super(SERVICE_NAME, serviceDiscovery, webClientBuilder, tokenProvider, meterRegistry);
    }

    @Override
    protected String getClientServiceName() {
        return "order-management-service";
    }
    
    /**
     * Processes a payment authorization.
     *
     * @param orderId the order ID
     * @param amount the payment amount
     * @param currency the currency
     * @param paymentMethod the payment method
     * @param paymentDetails additional payment details
     * @return the payment response
     */
    @CircuitBreaker(name = SERVICE_NAME, fallbackMethod = "authorizePaymentFallback")
    @Retry(name = SERVICE_NAME)
    @TimeLimiter(name = SERVICE_NAME)
    public CompletableFuture<PaymentResponse> authorizePayment(UUID orderId, BigDecimal amount, String currency,
                                                             String paymentMethod, String paymentDetails) {
        PaymentRequest request = new PaymentRequest();
        request.setOrderId(orderId);
        request.setAmount(amount);
        request.setCurrency(currency);
        request.setPaymentMethod(paymentMethod);
        request.setPaymentDetails(paymentDetails);
        request.setAction("AUTHORIZE");

        logger.info("Authorizing payment for order: {} amount: {} {}", orderId, amount, currency);

        return post("/api/v1/payments/authorize", request, PaymentResponse.class);
    }
    
    /**
     * Captures a previously authorized payment.
     *
     * @param transactionId the transaction ID from authorization
     * @param amount the amount to capture (can be less than authorized amount)
     * @return the payment response
     */
    @CircuitBreaker(name = SERVICE_NAME, fallbackMethod = "capturePaymentFallback")
    @Retry(name = SERVICE_NAME)
    @TimeLimiter(name = SERVICE_NAME)
    public CompletableFuture<PaymentResponse> capturePayment(String transactionId, BigDecimal amount) {
        PaymentRequest request = new PaymentRequest();
        request.setTransactionId(transactionId);
        request.setAmount(amount);
        request.setAction("CAPTURE");

        logger.info("Capturing payment for transaction: {} amount: {}", transactionId, amount);

        return post("/api/v1/payments/capture", request, PaymentResponse.class);
    }
    
    /**
     * Processes a direct payment (authorize and capture in one step).
     *
     * @param orderId the order ID
     * @param amount the payment amount
     * @param currency the currency
     * @param paymentMethod the payment method
     * @param paymentDetails additional payment details
     * @return the payment response
     */
    @CircuitBreaker(name = SERVICE_NAME, fallbackMethod = "processPaymentFallback")
    @Retry(name = SERVICE_NAME)
    @TimeLimiter(name = SERVICE_NAME)
    public CompletableFuture<PaymentResponse> processPayment(UUID orderId, BigDecimal amount, String currency,
                                                           String paymentMethod, String paymentDetails) {
        PaymentRequest request = new PaymentRequest();
        request.setOrderId(orderId);
        request.setAmount(amount);
        request.setCurrency(currency);
        request.setPaymentMethod(paymentMethod);
        request.setPaymentDetails(paymentDetails);
        request.setAction("SALE");

        logger.info("Processing payment for order: {} amount: {} {}", orderId, amount, currency);

        return post("/api/v1/payments/process", request, PaymentResponse.class);
    }
    
    /**
     * Processes a refund for a captured payment.
     *
     * @param transactionId the original transaction ID
     * @param amount the refund amount
     * @param reason the refund reason
     * @return the refund response
     */
    @CircuitBreaker(name = SERVICE_NAME, fallbackMethod = "refundPaymentFallback")
    @Retry(name = SERVICE_NAME)
    @TimeLimiter(name = SERVICE_NAME)
    public CompletableFuture<RefundResponse> refundPayment(String transactionId, BigDecimal amount, String reason) {
        RefundRequest request = new RefundRequest();
        request.setTransactionId(transactionId);
        request.setAmount(amount);
        request.setReason(reason);

        logger.info("Processing refund for transaction: {} amount: {}", transactionId, amount);

        return post("/api/v1/payments/refund", request, RefundResponse.class);
    }
    
    /**
     * Voids an authorized payment.
     *
     * @param transactionId the transaction ID to void
     * @return the payment response
     */
    @CircuitBreaker(name = SERVICE_NAME, fallbackMethod = "voidPaymentFallback")
    @Retry(name = SERVICE_NAME)
    @TimeLimiter(name = SERVICE_NAME)
    public CompletableFuture<PaymentResponse> voidPayment(String transactionId) {
        PaymentRequest request = new PaymentRequest();
        request.setTransactionId(transactionId);
        request.setAction("VOID");

        logger.info("Voiding payment for transaction: {}", transactionId);

        return post("/api/v1/payments/void", request, PaymentResponse.class);
    }

    // Fallback methods

    public CompletableFuture<PaymentResponse> authorizePaymentFallback(UUID orderId, BigDecimal amount, String currency,
                                                                     String paymentMethod, String paymentDetails, Exception ex) {
        logger.error("Fallback: Payment authorization failed for order: {}", orderId, ex);
        throw new PaymentServiceException("Payment authorization failed", ex);
    }

    public CompletableFuture<PaymentResponse> capturePaymentFallback(String transactionId, BigDecimal amount, Exception ex) {
        logger.error("Fallback: Payment capture failed for transaction: {}", transactionId, ex);
        throw new PaymentServiceException("Payment capture failed", ex);
    }

    public CompletableFuture<PaymentResponse> processPaymentFallback(UUID orderId, BigDecimal amount, String currency,
                                                                   String paymentMethod, String paymentDetails, Exception ex) {
        logger.error("Fallback: Payment processing failed for order: {}", orderId, ex);
        throw new PaymentServiceException("Payment processing failed", ex);
    }

    public CompletableFuture<RefundResponse> refundPaymentFallback(String transactionId, BigDecimal amount, String reason, Exception ex) {
        logger.error("Fallback: Refund processing failed for transaction: {}", transactionId, ex);
        throw new PaymentServiceException("Refund processing failed", ex);
    }

    public CompletableFuture<PaymentResponse> voidPaymentFallback(String transactionId, Exception ex) {
        logger.error("Fallback: Payment void failed for transaction: {}", transactionId, ex);
        throw new PaymentServiceException("Payment void failed", ex);
    }

    /**
     * Custom exception for payment service errors.
     */
    public static class PaymentServiceException extends RuntimeException {
        public PaymentServiceException(String message) {
            super(message);
        }

        public PaymentServiceException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}

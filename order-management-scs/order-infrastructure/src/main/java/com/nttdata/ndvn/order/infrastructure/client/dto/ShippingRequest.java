package com.nttdata.ndvn.order.infrastructure.client.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Request DTO for shipping service operations.
 * 
 * This class represents the data structure used when communicating
 * with external shipping services for creating shipments, calculating
 * rates, and scheduling pickups.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-28
 */
public class ShippingRequest {
    
    @JsonProperty("orderId")
    private UUID orderId;
    
    @JsonProperty("fromAddress")
    @Valid
    @NotNull
    private Address fromAddress;
    
    @JsonProperty("toAddress")
    @Valid
    @NotNull
    private Address toAddress;
    
    @JsonProperty("packageDetails")
    @Valid
    @NotNull
    private PackageDetails packageDetails;
    
    @JsonProperty("serviceType")
    @NotBlank
    private String serviceType = "STANDARD";
    
    @JsonProperty("requestedPickupDate")
    private LocalDateTime requestedPickupDate;
    
    @JsonProperty("specialInstructions")
    private String specialInstructions;

    @JsonProperty("carrier")
    private String carrier;

    @JsonProperty("action")
    private String action;

    // Constructors
    public ShippingRequest() {}
    
    public ShippingRequest(UUID orderId, Address fromAddress, Address toAddress, PackageDetails packageDetails) {
        this.orderId = orderId;
        this.fromAddress = fromAddress;
        this.toAddress = toAddress;
        this.packageDetails = packageDetails;
    }
    
    // Getters and Setters
    public UUID getOrderId() {
        return orderId;
    }
    
    public void setOrderId(UUID orderId) {
        this.orderId = orderId;
    }
    
    public Address getFromAddress() {
        return fromAddress;
    }
    
    public void setFromAddress(Address fromAddress) {
        this.fromAddress = fromAddress;
    }
    
    public Address getToAddress() {
        return toAddress;
    }
    
    public void setToAddress(Address toAddress) {
        this.toAddress = toAddress;
    }
    
    public PackageDetails getPackageDetails() {
        return packageDetails;
    }
    
    public void setPackageDetails(PackageDetails packageDetails) {
        this.packageDetails = packageDetails;
    }
    
    public String getServiceType() {
        return serviceType;
    }
    
    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }
    
    public LocalDateTime getRequestedPickupDate() {
        return requestedPickupDate;
    }
    
    public void setRequestedPickupDate(LocalDateTime requestedPickupDate) {
        this.requestedPickupDate = requestedPickupDate;
    }
    
    public String getSpecialInstructions() {
        return specialInstructions;
    }
    
    public void setSpecialInstructions(String specialInstructions) {
        this.specialInstructions = specialInstructions;
    }

    public String getCarrier() {
        return carrier;
    }

    public void setCarrier(String carrier) {
        this.carrier = carrier;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    /**
     * Address information for shipping.
     */
    public static class Address {
        @JsonProperty("name")
        @NotBlank
        private String name;
        
        @JsonProperty("company")
        private String company;
        
        @JsonProperty("addressLine1")
        @NotBlank
        private String addressLine1;
        
        @JsonProperty("addressLine2")
        private String addressLine2;
        
        @JsonProperty("city")
        @NotBlank
        private String city;
        
        @JsonProperty("state")
        @NotBlank
        private String state;
        
        @JsonProperty("postalCode")
        @NotBlank
        private String postalCode;
        
        @JsonProperty("country")
        @NotBlank
        private String country;
        
        @JsonProperty("phone")
        private String phone;
        
        @JsonProperty("email")
        private String email;
        
        // Constructors
        public Address() {}
        
        public Address(String name, String addressLine1, String city, String state, String postalCode, String country) {
            this.name = name;
            this.addressLine1 = addressLine1;
            this.city = city;
            this.state = state;
            this.postalCode = postalCode;
            this.country = country;
        }
        
        // Getters and Setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public String getCompany() { return company; }
        public void setCompany(String company) { this.company = company; }
        
        public String getAddressLine1() { return addressLine1; }
        public void setAddressLine1(String addressLine1) { this.addressLine1 = addressLine1; }
        
        public String getAddressLine2() { return addressLine2; }
        public void setAddressLine2(String addressLine2) { this.addressLine2 = addressLine2; }
        
        public String getCity() { return city; }
        public void setCity(String city) { this.city = city; }
        
        public String getState() { return state; }
        public void setState(String state) { this.state = state; }
        
        public String getPostalCode() { return postalCode; }
        public void setPostalCode(String postalCode) { this.postalCode = postalCode; }
        
        public String getCountry() { return country; }
        public void setCountry(String country) { this.country = country; }
        
        public String getPhone() { return phone; }
        public void setPhone(String phone) { this.phone = phone; }
        
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
    }
    
    /**
     * Package details for shipping.
     */
    public static class PackageDetails {
        @JsonProperty("weight")
        @NotNull
        @Positive
        private BigDecimal weight;
        
        @JsonProperty("weightUnit")
        @NotBlank
        private String weightUnit = "KG";
        
        @JsonProperty("length")
        @Positive
        private BigDecimal length;
        
        @JsonProperty("width")
        @Positive
        private BigDecimal width;
        
        @JsonProperty("height")
        @Positive
        private BigDecimal height;
        
        @JsonProperty("dimensionUnit")
        private String dimensionUnit = "CM";
        
        @JsonProperty("value")
        @NotNull
        @Positive
        private BigDecimal value;
        
        @JsonProperty("currency")
        @NotBlank
        private String currency = "USD";
        
        @JsonProperty("description")
        private String description;
        
        // Constructors
        public PackageDetails() {}
        
        public PackageDetails(BigDecimal weight, BigDecimal value, String description) {
            this.weight = weight;
            this.value = value;
            this.description = description;
        }
        
        // Getters and Setters
        public BigDecimal getWeight() { return weight; }
        public void setWeight(BigDecimal weight) { this.weight = weight; }
        
        public String getWeightUnit() { return weightUnit; }
        public void setWeightUnit(String weightUnit) { this.weightUnit = weightUnit; }
        
        public BigDecimal getLength() { return length; }
        public void setLength(BigDecimal length) { this.length = length; }
        
        public BigDecimal getWidth() { return width; }
        public void setWidth(BigDecimal width) { this.width = width; }
        
        public BigDecimal getHeight() { return height; }
        public void setHeight(BigDecimal height) { this.height = height; }
        
        public String getDimensionUnit() { return dimensionUnit; }
        public void setDimensionUnit(String dimensionUnit) { this.dimensionUnit = dimensionUnit; }
        
        public BigDecimal getValue() { return value; }
        public void setValue(BigDecimal value) { this.value = value; }
        
        public String getCurrency() { return currency; }
        public void setCurrency(String currency) { this.currency = currency; }
        
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
    }
}

package com.nttdata.ndvn.order.infrastructure.client.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * Response DTO for shipping service operations.
 * 
 * This class represents the response data structure received from
 * external shipping services for shipment creation, rate calculation,
 * and pickup scheduling operations.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-28
 */
public class ShippingResponse {
    
    @JsonProperty("success")
    private boolean success;
    
    @JsonProperty("shipmentId")
    private String shipmentId;
    
    @JsonProperty("trackingNumber")
    private String trackingNumber;
    
    @JsonProperty("carrier")
    private String carrier;
    
    @JsonProperty("serviceType")
    private String serviceType;
    
    @JsonProperty("estimatedDeliveryDate")
    private LocalDateTime estimatedDeliveryDate;
    
    @JsonProperty("shippingCost")
    private BigDecimal shippingCost;
    
    @JsonProperty("currency")
    private String currency;
    
    @JsonProperty("status")
    private String status;
    
    @JsonProperty("createdAt")
    private LocalDateTime createdAt;
    
    @JsonProperty("rates")
    private List<ShippingRate> rates;
    
    @JsonProperty("pickupConfirmation")
    private PickupConfirmation pickupConfirmation;
    
    @JsonProperty("errorMessage")
    private String errorMessage;
    
    @JsonProperty("errorCode")
    private String errorCode;
    
    // Constructors
    public ShippingResponse() {}
    
    public ShippingResponse(boolean success) {
        this.success = success;
        this.createdAt = LocalDateTime.now();
    }
    
    public static ShippingResponse success(String shipmentId, String trackingNumber, String carrier) {
        ShippingResponse response = new ShippingResponse(true);
        response.setShipmentId(shipmentId);
        response.setTrackingNumber(trackingNumber);
        response.setCarrier(carrier);
        response.setStatus("CREATED");
        return response;
    }
    
    public static ShippingResponse failure(String errorCode, String errorMessage) {
        ShippingResponse response = new ShippingResponse(false);
        response.setErrorCode(errorCode);
        response.setErrorMessage(errorMessage);
        return response;
    }
    
    // Getters and Setters
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getShipmentId() {
        return shipmentId;
    }
    
    public void setShipmentId(String shipmentId) {
        this.shipmentId = shipmentId;
    }
    
    public String getTrackingNumber() {
        return trackingNumber;
    }
    
    public void setTrackingNumber(String trackingNumber) {
        this.trackingNumber = trackingNumber;
    }
    
    public String getCarrier() {
        return carrier;
    }
    
    public void setCarrier(String carrier) {
        this.carrier = carrier;
    }
    
    public String getServiceType() {
        return serviceType;
    }
    
    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }
    
    public LocalDateTime getEstimatedDeliveryDate() {
        return estimatedDeliveryDate;
    }
    
    public void setEstimatedDeliveryDate(LocalDateTime estimatedDeliveryDate) {
        this.estimatedDeliveryDate = estimatedDeliveryDate;
    }
    
    public BigDecimal getShippingCost() {
        return shippingCost;
    }
    
    public void setShippingCost(BigDecimal shippingCost) {
        this.shippingCost = shippingCost;
    }
    
    public String getCurrency() {
        return currency;
    }
    
    public void setCurrency(String currency) {
        this.currency = currency;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public List<ShippingRate> getRates() {
        return rates;
    }
    
    public void setRates(List<ShippingRate> rates) {
        this.rates = rates;
    }
    
    public PickupConfirmation getPickupConfirmation() {
        return pickupConfirmation;
    }
    
    public void setPickupConfirmation(PickupConfirmation pickupConfirmation) {
        this.pickupConfirmation = pickupConfirmation;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
    
    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }
    
    /**
     * Shipping rate information.
     */
    public static class ShippingRate {
        @JsonProperty("serviceType")
        private String serviceType;
        
        @JsonProperty("serviceName")
        private String serviceName;
        
        @JsonProperty("cost")
        private BigDecimal cost;
        
        @JsonProperty("currency")
        private String currency;
        
        @JsonProperty("estimatedDeliveryDays")
        private Integer estimatedDeliveryDays;
        
        @JsonProperty("estimatedDeliveryDate")
        private LocalDateTime estimatedDeliveryDate;
        
        // Constructors
        public ShippingRate() {}
        
        public ShippingRate(String serviceType, String serviceName, BigDecimal cost, String currency, Integer estimatedDeliveryDays) {
            this.serviceType = serviceType;
            this.serviceName = serviceName;
            this.cost = cost;
            this.currency = currency;
            this.estimatedDeliveryDays = estimatedDeliveryDays;
        }
        
        // Getters and Setters
        public String getServiceType() { return serviceType; }
        public void setServiceType(String serviceType) { this.serviceType = serviceType; }
        
        public String getServiceName() { return serviceName; }
        public void setServiceName(String serviceName) { this.serviceName = serviceName; }
        
        public BigDecimal getCost() { return cost; }
        public void setCost(BigDecimal cost) { this.cost = cost; }
        
        public String getCurrency() { return currency; }
        public void setCurrency(String currency) { this.currency = currency; }
        
        public Integer getEstimatedDeliveryDays() { return estimatedDeliveryDays; }
        public void setEstimatedDeliveryDays(Integer estimatedDeliveryDays) { this.estimatedDeliveryDays = estimatedDeliveryDays; }
        
        public LocalDateTime getEstimatedDeliveryDate() { return estimatedDeliveryDate; }
        public void setEstimatedDeliveryDate(LocalDateTime estimatedDeliveryDate) { this.estimatedDeliveryDate = estimatedDeliveryDate; }
    }
    
    /**
     * Pickup confirmation details.
     */
    public static class PickupConfirmation {
        @JsonProperty("pickupId")
        private String pickupId;
        
        @JsonProperty("confirmationNumber")
        private String confirmationNumber;
        
        @JsonProperty("scheduledDate")
        private LocalDateTime scheduledDate;
        
        @JsonProperty("timeWindow")
        private String timeWindow;
        
        @JsonProperty("status")
        private String status;
        
        // Constructors
        public PickupConfirmation() {}
        
        public PickupConfirmation(String pickupId, String confirmationNumber, LocalDateTime scheduledDate) {
            this.pickupId = pickupId;
            this.confirmationNumber = confirmationNumber;
            this.scheduledDate = scheduledDate;
            this.status = "SCHEDULED";
        }
        
        // Getters and Setters
        public String getPickupId() { return pickupId; }
        public void setPickupId(String pickupId) { this.pickupId = pickupId; }
        
        public String getConfirmationNumber() { return confirmationNumber; }
        public void setConfirmationNumber(String confirmationNumber) { this.confirmationNumber = confirmationNumber; }
        
        public LocalDateTime getScheduledDate() { return scheduledDate; }
        public void setScheduledDate(LocalDateTime scheduledDate) { this.scheduledDate = scheduledDate; }
        
        public String getTimeWindow() { return timeWindow; }
        public void setTimeWindow(String timeWindow) { this.timeWindow = timeWindow; }
        
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
    }
}

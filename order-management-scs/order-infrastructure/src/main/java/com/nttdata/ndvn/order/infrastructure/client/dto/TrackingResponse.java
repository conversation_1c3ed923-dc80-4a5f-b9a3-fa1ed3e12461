package com.nttdata.ndvn.order.infrastructure.client.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Response DTO for tracking information from shipping services.
 * 
 * This class represents the tracking data structure received from
 * external shipping services when querying shipment status and
 * tracking information.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-28
 */
public class TrackingResponse {
    
    @JsonProperty("success")
    private boolean success;
    
    @JsonProperty("trackingNumber")
    private String trackingNumber;
    
    @JsonProperty("shipmentId")
    private String shipmentId;
    
    @JsonProperty("carrier")
    private String carrier;
    
    @JsonProperty("status")
    private String status;
    
    @JsonProperty("statusDescription")
    private String statusDescription;
    
    @JsonProperty("estimatedDeliveryDate")
    private LocalDateTime estimatedDeliveryDate;
    
    @JsonProperty("actualDeliveryDate")
    private LocalDateTime actualDeliveryDate;
    
    @JsonProperty("currentLocation")
    private TrackingLocation currentLocation;
    
    @JsonProperty("destinationLocation")
    private TrackingLocation destinationLocation;
    
    @JsonProperty("trackingEvents")
    private List<TrackingEvent> trackingEvents;
    
    @JsonProperty("lastUpdated")
    private LocalDateTime lastUpdated;
    
    @JsonProperty("errorMessage")
    private String errorMessage;
    
    @JsonProperty("errorCode")
    private String errorCode;
    
    // Constructors
    public TrackingResponse() {}
    
    public TrackingResponse(boolean success) {
        this.success = success;
        this.lastUpdated = LocalDateTime.now();
    }
    
    public static TrackingResponse success(String trackingNumber, String status, String statusDescription) {
        TrackingResponse response = new TrackingResponse(true);
        response.setTrackingNumber(trackingNumber);
        response.setStatus(status);
        response.setStatusDescription(statusDescription);
        return response;
    }
    
    public static TrackingResponse failure(String errorCode, String errorMessage) {
        TrackingResponse response = new TrackingResponse(false);
        response.setErrorCode(errorCode);
        response.setErrorMessage(errorMessage);
        return response;
    }
    
    // Getters and Setters
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getTrackingNumber() {
        return trackingNumber;
    }
    
    public void setTrackingNumber(String trackingNumber) {
        this.trackingNumber = trackingNumber;
    }
    
    public String getShipmentId() {
        return shipmentId;
    }
    
    public void setShipmentId(String shipmentId) {
        this.shipmentId = shipmentId;
    }
    
    public String getCarrier() {
        return carrier;
    }
    
    public void setCarrier(String carrier) {
        this.carrier = carrier;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getStatusDescription() {
        return statusDescription;
    }
    
    public void setStatusDescription(String statusDescription) {
        this.statusDescription = statusDescription;
    }
    
    public LocalDateTime getEstimatedDeliveryDate() {
        return estimatedDeliveryDate;
    }
    
    public void setEstimatedDeliveryDate(LocalDateTime estimatedDeliveryDate) {
        this.estimatedDeliveryDate = estimatedDeliveryDate;
    }
    
    public LocalDateTime getActualDeliveryDate() {
        return actualDeliveryDate;
    }
    
    public void setActualDeliveryDate(LocalDateTime actualDeliveryDate) {
        this.actualDeliveryDate = actualDeliveryDate;
    }
    
    public TrackingLocation getCurrentLocation() {
        return currentLocation;
    }
    
    public void setCurrentLocation(TrackingLocation currentLocation) {
        this.currentLocation = currentLocation;
    }
    
    public TrackingLocation getDestinationLocation() {
        return destinationLocation;
    }
    
    public void setDestinationLocation(TrackingLocation destinationLocation) {
        this.destinationLocation = destinationLocation;
    }
    
    public List<TrackingEvent> getTrackingEvents() {
        return trackingEvents;
    }
    
    public void setTrackingEvents(List<TrackingEvent> trackingEvents) {
        this.trackingEvents = trackingEvents;
    }
    
    public LocalDateTime getLastUpdated() {
        return lastUpdated;
    }
    
    public void setLastUpdated(LocalDateTime lastUpdated) {
        this.lastUpdated = lastUpdated;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
    
    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }
    
    /**
     * Location information for tracking.
     */
    public static class TrackingLocation {
        @JsonProperty("city")
        private String city;
        
        @JsonProperty("state")
        private String state;
        
        @JsonProperty("country")
        private String country;
        
        @JsonProperty("postalCode")
        private String postalCode;
        
        @JsonProperty("facilityName")
        private String facilityName;
        
        @JsonProperty("facilityType")
        private String facilityType;
        
        // Constructors
        public TrackingLocation() {}
        
        public TrackingLocation(String city, String state, String country) {
            this.city = city;
            this.state = state;
            this.country = country;
        }
        
        // Getters and Setters
        public String getCity() { return city; }
        public void setCity(String city) { this.city = city; }
        
        public String getState() { return state; }
        public void setState(String state) { this.state = state; }
        
        public String getCountry() { return country; }
        public void setCountry(String country) { this.country = country; }
        
        public String getPostalCode() { return postalCode; }
        public void setPostalCode(String postalCode) { this.postalCode = postalCode; }
        
        public String getFacilityName() { return facilityName; }
        public void setFacilityName(String facilityName) { this.facilityName = facilityName; }
        
        public String getFacilityType() { return facilityType; }
        public void setFacilityType(String facilityType) { this.facilityType = facilityType; }
    }
    
    /**
     * Individual tracking event.
     */
    public static class TrackingEvent {
        @JsonProperty("timestamp")
        private LocalDateTime timestamp;
        
        @JsonProperty("status")
        private String status;
        
        @JsonProperty("description")
        private String description;
        
        @JsonProperty("location")
        private TrackingLocation location;
        
        @JsonProperty("eventType")
        private String eventType;
        
        // Constructors
        public TrackingEvent() {}
        
        public TrackingEvent(LocalDateTime timestamp, String status, String description) {
            this.timestamp = timestamp;
            this.status = status;
            this.description = description;
        }
        
        // Getters and Setters
        public LocalDateTime getTimestamp() { return timestamp; }
        public void setTimestamp(LocalDateTime timestamp) { this.timestamp = timestamp; }
        
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        
        public TrackingLocation getLocation() { return location; }
        public void setLocation(TrackingLocation location) { this.location = location; }
        
        public String getEventType() { return eventType; }
        public void setEventType(String eventType) { this.eventType = eventType; }
    }
}

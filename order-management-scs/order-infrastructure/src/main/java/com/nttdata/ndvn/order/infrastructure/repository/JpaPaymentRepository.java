package com.nttdata.ndvn.order.infrastructure.repository;

import com.nttdata.ndvn.order.domain.model.Payment;
import com.nttdata.ndvn.order.domain.model.PaymentStatus;
import com.nttdata.ndvn.order.domain.repository.PaymentRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * JPA implementation of PaymentRepository.
 * 
 * This repository provides data access operations for Payment entities
 * using Spring Data JPA.
 */
@Repository
public interface JpaPaymentRepository extends JpaRepository<Payment, UUID>, PaymentRepository {
    
    @Override
    List<Payment> findByOrderId(UUID orderId);
    
    @Override
    Page<Payment> findByStatus(PaymentStatus status, Pageable pageable);
    
    @Override
    Optional<Payment> findByTransactionId(String transactionId);
    
    @Override
    Optional<Payment> findByGatewayTransactionId(String gatewayTransactionId);
    
    @Override
    Page<Payment> findByPaymentMethod(String paymentMethod, Pageable pageable);
    
    @Override
    Page<Payment> findByCreatedAtBetween(LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);
    
    @Override
    List<Payment> findByOrderIdAndStatus(UUID orderId, PaymentStatus status);
    
    @Override
    @Query("SELECT p FROM Payment p WHERE p.orderId = :orderId AND p.status = 'CAPTURED'")
    List<Payment> findSuccessfulPaymentsByOrderId(@Param("orderId") UUID orderId);
    
    @Override
    @Query("SELECT p FROM Payment p WHERE p.status = 'PENDING' AND p.createdAt < :cutoffTime")
    default List<Payment> findStalePendingPayments(int minutesAgo) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusMinutes(minutesAgo);
        return findStalePendingPaymentsByCutoffTime(cutoffTime);
    }

    @Query("SELECT p FROM Payment p WHERE p.status = 'PENDING' AND p.createdAt < :cutoffTime")
    List<Payment> findStalePendingPaymentsByCutoffTime(@Param("cutoffTime") LocalDateTime cutoffTime);
    
    @Override
    @Query("SELECT p FROM Payment p WHERE p.status = 'AUTHORIZED'")
    Page<Payment> findAuthorizedPayments(Pageable pageable);
    
    @Override
    long countByStatus(PaymentStatus status);
    
    @Override
    long countByOrderId(UUID orderId);
    
    // Additional JPA-specific queries
    
    @Query("SELECT p FROM Payment p WHERE p.paymentGateway = :gateway AND p.status = :status")
    Page<Payment> findByPaymentGatewayAndStatus(@Param("gateway") String gateway, 
                                               @Param("status") PaymentStatus status, 
                                               Pageable pageable);
    
    @Query("SELECT SUM(p.amount) FROM Payment p WHERE p.orderId = :orderId AND p.status = 'CAPTURED'")
    java.math.BigDecimal getTotalPaidAmountForOrder(@Param("orderId") UUID orderId);
    
    @Query("SELECT p FROM Payment p WHERE p.amount > :amount AND p.status = :status")
    Page<Payment> findByAmountGreaterThanAndStatus(@Param("amount") java.math.BigDecimal amount, 
                                                  @Param("status") PaymentStatus status, 
                                                  Pageable pageable);
    
    @Query("SELECT p FROM Payment p WHERE p.orderId = :orderId ORDER BY p.createdAt DESC")
    List<Payment> findByOrderIdOrderByCreatedAtDesc(@Param("orderId") UUID orderId);
    
    @Query("SELECT p FROM Payment p WHERE p.status IN ('FAILED', 'DECLINED') AND p.createdAt >= :startDate")
    List<Payment> findFailedPaymentsSince(@Param("startDate") LocalDateTime startDate);
    
    @Query("SELECT p.paymentMethod, COUNT(p) FROM Payment p WHERE p.status = 'CAPTURED' GROUP BY p.paymentMethod")
    List<Object[]> getPaymentCountByMethod();
    
    @Query("SELECT DATE(p.createdAt), SUM(p.amount) FROM Payment p " +
           "WHERE p.status = 'CAPTURED' AND p.createdAt >= :startDate " +
           "GROUP BY DATE(p.createdAt) " +
           "ORDER BY DATE(p.createdAt)")
    List<Object[]> getPaymentVolumeByDate(@Param("startDate") LocalDateTime startDate);
    
    @Query("SELECT p FROM Payment p WHERE p.refundedAmount > 0")
    Page<Payment> findRefundedPayments(Pageable pageable);
    
    @Query("SELECT p FROM Payment p WHERE p.status = 'AUTHORIZED' AND p.authorizedAt < :cutoffTime")
    List<Payment> findExpiredAuthorizations(@Param("cutoffTime") LocalDateTime cutoffTime);
    
    // Implementation of custom methods from PaymentRepository interface
}

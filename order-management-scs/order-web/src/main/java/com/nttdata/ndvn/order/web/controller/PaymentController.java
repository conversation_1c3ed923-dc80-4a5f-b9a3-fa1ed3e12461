package com.nttdata.ndvn.order.web.controller;

import com.nttdata.ndvn.order.application.dto.CreatePaymentRequest;
import com.nttdata.ndvn.order.application.dto.PaymentResponse;
import com.nttdata.ndvn.order.application.service.PaymentApplicationService;
import com.nttdata.ndvn.order.domain.model.PaymentStatus;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

/**
 * REST controller for payment processing operations.
 * 
 * This controller provides endpoints for payment processing,
 * including authorization, capture, refunds, and payment tracking.
 */
@RestController
@RequestMapping("/api/v1/payments")
@Tag(name = "Payment Management", description = "Payment processing and management operations")
public class PaymentController {
    
    private static final Logger logger = LoggerFactory.getLogger(PaymentController.class);
    
    private final PaymentApplicationService paymentApplicationService;
    
    @Autowired
    public PaymentController(PaymentApplicationService paymentApplicationService) {
        this.paymentApplicationService = paymentApplicationService;
    }
    
    /**
     * Processes a payment for an order.
     * 
     * @param request the payment request
     * @return the payment response
     */
    @PostMapping
    @Operation(summary = "Process payment", description = "Processes a payment for an order")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Payment processed successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid payment data"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Forbidden")
    })
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<PaymentResponse> processPayment(@Valid @RequestBody CreatePaymentRequest request) {
        logger.info("Processing payment for order: {} amount: {}", request.getOrderId(), request.getAmount());
        
        try {
            PaymentResponse response = paymentApplicationService.processPayment(request);
            logger.info("Payment processed successfully for order: {} status: {}", 
                request.getOrderId(), response.getStatus());
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
        } catch (PaymentApplicationService.PaymentValidationException e) {
            logger.warn("Payment validation failed: {}", e.getMessage());
            throw new BadRequestException(e.getMessage());
        } catch (PaymentApplicationService.OrderNotFoundException e) {
            logger.warn("Order not found for payment: {}", request.getOrderId());
            throw new NotFoundException(e.getMessage());
        }
    }
    
    /**
     * Captures a previously authorized payment.
     * 
     * @param paymentId the payment ID
     * @param request the capture request
     * @return the updated payment response
     */
    @PostMapping("/{paymentId}/capture")
    @Operation(summary = "Capture payment", description = "Captures a previously authorized payment")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Payment captured successfully"),
        @ApiResponse(responseCode = "400", description = "Payment cannot be captured"),
        @ApiResponse(responseCode = "404", description = "Payment not found"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Forbidden")
    })
    @PreAuthorize("hasRole('ADMIN') or hasRole('PAYMENT_PROCESSOR')")
    public ResponseEntity<PaymentResponse> capturePayment(
            @Parameter(description = "Payment ID", required = true) @PathVariable UUID paymentId,
            @RequestBody(required = false) CapturePaymentRequest request) {
        logger.info("Capturing payment: {}", paymentId);
        
        BigDecimal amount = request != null ? request.getAmount() : null;
        
        try {
            PaymentResponse response = paymentApplicationService.capturePayment(paymentId, amount);
            logger.info("Payment captured successfully: {}", paymentId);
            return ResponseEntity.ok(response);
        } catch (PaymentApplicationService.PaymentNotFoundException e) {
            logger.warn("Payment not found for capture: {}", paymentId);
            throw new NotFoundException(e.getMessage());
        } catch (PaymentApplicationService.PaymentValidationException e) {
            logger.warn("Payment capture validation failed: {}", e.getMessage());
            throw new BadRequestException(e.getMessage());
        }
    }
    
    /**
     * Refunds a payment.
     * 
     * @param paymentId the payment ID
     * @param request the refund request
     * @return the updated payment response
     */
    @PostMapping("/{paymentId}/refund")
    @Operation(summary = "Refund payment", description = "Processes a refund for a payment")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Payment refunded successfully"),
        @ApiResponse(responseCode = "400", description = "Payment cannot be refunded"),
        @ApiResponse(responseCode = "404", description = "Payment not found"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Forbidden")
    })
    @PreAuthorize("hasRole('ADMIN') or hasRole('PAYMENT_PROCESSOR')")
    public ResponseEntity<PaymentResponse> refundPayment(
            @Parameter(description = "Payment ID", required = true) @PathVariable UUID paymentId,
            @Valid @RequestBody RefundPaymentRequest request) {
        logger.info("Refunding payment: {} amount: {} reason: {}", 
            paymentId, request.getAmount(), request.getReason());
        
        try {
            PaymentResponse response = paymentApplicationService.refundPayment(
                paymentId, request.getAmount(), request.getReason());
            logger.info("Payment refunded successfully: {}", paymentId);
            return ResponseEntity.ok(response);
        } catch (PaymentApplicationService.PaymentNotFoundException e) {
            logger.warn("Payment not found for refund: {}", paymentId);
            throw new NotFoundException(e.getMessage());
        } catch (PaymentApplicationService.PaymentValidationException e) {
            logger.warn("Payment refund validation failed: {}", e.getMessage());
            throw new BadRequestException(e.getMessage());
        }
    }
    
    /**
     * Retrieves payments for an order.
     * 
     * @param orderId the order ID
     * @return list of payment responses
     */
    @GetMapping("/order/{orderId}")
    @Operation(summary = "Get order payments", description = "Retrieves all payments for a specific order")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Payments retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Forbidden")
    })
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<List<PaymentResponse>> getOrderPayments(
            @Parameter(description = "Order ID", required = true) @PathVariable UUID orderId) {
        logger.debug("Retrieving payments for order: {}", orderId);
        
        List<PaymentResponse> payments = paymentApplicationService.getOrderPayments(orderId);
        return ResponseEntity.ok(payments);
    }
    
    /**
     * Retrieves payments by status.
     * 
     * @param status the payment status
     * @param pageable pagination parameters
     * @return page of payment responses
     */
    @GetMapping("/status/{status}")
    @Operation(summary = "Get payments by status", description = "Retrieves payments filtered by status")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Payments retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Forbidden")
    })
    @PreAuthorize("hasRole('ADMIN') or hasRole('PAYMENT_PROCESSOR')")
    public ResponseEntity<Page<PaymentResponse>> getPaymentsByStatus(
            @Parameter(description = "Payment status", required = true) @PathVariable PaymentStatus status,
            @PageableDefault(size = 20) Pageable pageable) {
        logger.debug("Retrieving payments by status: {}", status);
        
        Page<PaymentResponse> payments = paymentApplicationService.getPaymentsByStatus(status, pageable);
        return ResponseEntity.ok(payments);
    }
    
    /**
     * DTO for capture payment request.
     */
    public static class CapturePaymentRequest {
        private BigDecimal amount;
        
        public BigDecimal getAmount() {
            return amount;
        }
        
        public void setAmount(BigDecimal amount) {
            this.amount = amount;
        }
    }
    
    /**
     * DTO for refund payment request.
     */
    public static class RefundPaymentRequest {
        @NotNull(message = "Refund amount is required")
        @DecimalMin(value = "0.01", message = "Refund amount must be positive")
        private BigDecimal amount;
        
        @NotBlank(message = "Refund reason is required")
        @Size(max = 500, message = "Refund reason must not exceed 500 characters")
        private String reason;
        
        public BigDecimal getAmount() {
            return amount;
        }
        
        public void setAmount(BigDecimal amount) {
            this.amount = amount;
        }
        
        public String getReason() {
            return reason;
        }
        
        public void setReason(String reason) {
            this.reason = reason;
        }
    }
    
    /**
     * Custom exception for bad request errors.
     */
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public static class BadRequestException extends RuntimeException {
        public BadRequestException(String message) {
            super(message);
        }
    }
    
    /**
     * Custom exception for not found errors.
     */
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public static class NotFoundException extends RuntimeException {
        public NotFoundException(String message) {
            super(message);
        }
    }
}

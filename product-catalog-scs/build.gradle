plugins {
    id 'java'
    id 'org.springframework.boot' version '3.4.1'
    id 'io.spring.dependency-management' version '1.1.7'
    id 'org.flywaydb.flyway' version '10.21.0'
}

// Configure main class for Spring Boot
springBoot {
    mainClass = 'com.nttdata.ndvn.product.ProductCatalogApplication'
}

group = 'com.nttdata.ndvn'
version = '1.0.0'

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    mavenLocal()
    mavenCentral()
    maven { url = 'https://packages.confluent.io/maven/' }
    maven { url = 'https://repo.spring.io/milestone' }
    maven { url = 'https://repo.spring.io/snapshot' }
    // Alternative Terasoluna repository
    maven {
        url = 'https://repo1.maven.org/maven2/'
        allowInsecureProtocol = false
    }
}

ext {
    terasolunaVersion = '5.10.0.RELEASE'
    springCloudVersion = '2024.0.0'
    testcontainersVersion = '1.20.4'
    mapstructVersion = '1.5.5.Final'
    kafkaVersion = '3.8.1'
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
        mavenBom "org.testcontainers:testcontainers-bom:${testcontainersVersion}"
        // Removed Terasoluna BOM due to repository accessibility issues
        // mavenBom "org.terasoluna.gfw:terasoluna-gfw-parent:${terasolunaVersion}"
    }
}

dependencies {
    // Replaced Terasoluna Framework with Spring Boot equivalents
    // implementation 'org.terasoluna.gfw:terasoluna-gfw-common-libraries'
    // implementation 'org.terasoluna.gfw:terasoluna-gfw-jodatime'
    // implementation 'org.terasoluna.gfw:terasoluna-gfw-web'
    // implementation 'org.terasoluna.gfw:terasoluna-gfw-web-jsp'
    // implementation 'org.terasoluna.gfw:terasoluna-gfw-security-web'

    // Spring Boot equivalents
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.apache.commons:commons-lang3'

    // Spring Boot Starters
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-cache'

    // Spring Cloud
    implementation 'org.springframework.cloud:spring-cloud-starter-consul-discovery'
    implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
    implementation 'org.springframework.cloud:spring-cloud-starter-loadbalancer'

    // Database
    implementation 'org.postgresql:postgresql'
    implementation 'org.flywaydb:flyway-core'
    implementation 'org.flywaydb:flyway-database-postgresql'

    // Kafka
    implementation "org.springframework.kafka:spring-kafka"
    implementation "org.apache.kafka:kafka-clients:${kafkaVersion}"
    implementation 'io.confluent:kafka-avro-serializer:7.7.1'

    // Elasticsearch
    implementation 'org.springframework.boot:spring-boot-starter-data-elasticsearch'

    // Mapping
    implementation "org.mapstruct:mapstruct:${mapstructVersion}"
    annotationProcessor "org.mapstruct:mapstruct-processor:${mapstructVersion}"

    // JSON Processing
    implementation 'com.fasterxml.jackson.core:jackson-databind'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310'

    // OpenAPI Documentation
    implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.7.0'

    // Monitoring
    implementation 'io.micrometer:micrometer-registry-prometheus'
    implementation 'io.micrometer:micrometer-tracing-bridge-brave'

    // Development
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    developmentOnly 'org.springframework.boot:spring-boot-devtools'

    // Testing
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.security:spring-security-test'
    testImplementation 'org.springframework.kafka:spring-kafka-test'
    testImplementation 'org.testcontainers:junit-jupiter'
    testImplementation 'org.testcontainers:postgresql'
    testImplementation 'org.testcontainers:kafka'
    testImplementation 'org.testcontainers:elasticsearch'
    testImplementation 'com.h2database:h2'
}

// Configure subprojects
configure(subprojects) {
    apply plugin: 'java'
    apply plugin: 'io.spring.dependency-management'

    java {
        toolchain {
            languageVersion = JavaLanguageVersion.of(21)
        }
    }

    repositories {
        mavenLocal()
        mavenCentral()
        maven { url = 'https://packages.confluent.io/maven/' }
        maven { url = 'https://repo.spring.io/milestone' }
        maven { url = 'https://repo.spring.io/snapshot' }
    }

    dependencyManagement {
        imports {
            mavenBom 'org.springframework.boot:spring-boot-dependencies:3.4.1'
            mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
            mavenBom "org.testcontainers:testcontainers-bom:${testcontainersVersion}"
            // Removed Terasoluna BOM due to repository accessibility issues
        }
    }

    dependencies {
        // Common dependencies for all modules
        compileOnly 'org.projectlombok:lombok'
        annotationProcessor 'org.projectlombok:lombok'

        testImplementation 'org.springframework.boot:spring-boot-starter-test'
        testImplementation 'org.junit.jupiter:junit-jupiter'
        testImplementation 'org.mockito:mockito-core'
        testImplementation 'org.assertj:assertj-core'
    }

    test {
        useJUnitPlatform()
        testLogging {
            events "passed", "skipped", "failed"
        }
    }
}

// Domain module configuration
configure(project(':product-domain')) {
    dependencies {
        // JPA for entities
        implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
        implementation 'org.springframework.boot:spring-boot-starter-validation'
        
        // Common utilities
        // implementation 'org.terasoluna.gfw:terasoluna-gfw-common'
        implementation 'org.apache.commons:commons-lang3'
    }
}

// Infrastructure module configuration
configure(project(':product-infrastructure')) {
    dependencies {
        implementation project(':product-domain')
        
        // Data access
        implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
        implementation 'org.springframework.boot:spring-boot-starter-data-redis'
        implementation 'org.springframework.boot:spring-boot-starter-data-elasticsearch'
        implementation 'org.postgresql:postgresql'
        
        // Caching
        implementation 'org.springframework.boot:spring-boot-starter-cache'
        
        // External integrations
        implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
        
        // File storage
        implementation 'org.springframework.boot:spring-boot-starter-web'
    }
}

// Application module configuration
configure(project(':product-application')) {
    dependencies {
        implementation project(':product-domain')
        implementation project(':product-infrastructure')

        // Shared Infrastructure for external service clients
        implementation 'com.nttdata.ndvn:shared-infrastructure:1.0.0-SNAPSHOT'

        // Application services
        implementation 'org.springframework:spring-tx'
        implementation 'org.springframework.boot:spring-boot-starter-validation'
        implementation 'org.springframework.data:spring-data-commons'

        // Mapping
        implementation "org.mapstruct:mapstruct:${mapstructVersion}"
        annotationProcessor "org.mapstruct:mapstruct-processor:${mapstructVersion}"

        // JSON processing
        implementation 'com.fasterxml.jackson.core:jackson-databind'
        implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310'
    }
}

// Events module configuration
configure(project(':product-events')) {
    dependencies {
        implementation project(':product-domain')

        // Shared Events Framework
        implementation 'com.nttdata.ndvn:shared-events:1.0.0-SNAPSHOT'

        // Kafka
        implementation 'org.springframework.kafka:spring-kafka'
        implementation "org.apache.kafka:kafka-clients:${kafkaVersion}"
        implementation 'io.confluent:kafka-avro-serializer:7.7.1'

        // JSON processing
        implementation 'com.fasterxml.jackson.core:jackson-databind'
        implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310'

        // Testing
        testImplementation 'org.springframework.kafka:spring-kafka-test'
        testImplementation 'org.testcontainers:kafka'
    }
}

// Web module configuration
configure(project(':product-web')) {
    apply plugin: 'org.springframework.boot'

    dependencies {
        implementation project(':product-domain')
        implementation project(':product-infrastructure')
        implementation project(':product-application')
        implementation project(':product-events')

        // Web
        implementation 'org.springframework.boot:spring-boot-starter-web'
        implementation 'org.springframework.boot:spring-boot-starter-security'
        implementation 'org.springframework.boot:spring-boot-starter-actuator'

        // Replaced Terasoluna Web with Spring Boot equivalents
        // implementation 'org.terasoluna.gfw:terasoluna-gfw-web'
        // implementation 'org.terasoluna.gfw:terasoluna-gfw-security-web'

        // Service Discovery
        implementation 'org.springframework.cloud:spring-cloud-starter-consul-discovery'
        
        // Documentation
        implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.7.0'
        
        // Monitoring
        implementation 'io.micrometer:micrometer-registry-prometheus'
        implementation 'io.micrometer:micrometer-tracing-bridge-brave'
        
        // Database migration
        implementation 'org.flywaydb:flyway-core'
        implementation 'org.flywaydb:flyway-database-postgresql'
        
        // Testing
        testImplementation 'org.springframework.security:spring-security-test'
        testImplementation 'org.testcontainers:postgresql'
        testImplementation 'com.h2database:h2'
    }
}

tasks.named('test') {
    useJUnitPlatform()
}

// Flyway configuration
flyway {
    url = '***************************************************'
    user = 'product_user'
    password = 'product_password'
    schemas = ['public']
    locations = ['classpath:db/migration']
}

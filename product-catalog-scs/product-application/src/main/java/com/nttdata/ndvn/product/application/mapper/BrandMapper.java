package com.nttdata.ndvn.product.application.mapper;

import com.nttdata.ndvn.product.application.dto.BrandDto;
import com.nttdata.ndvn.product.domain.model.Brand;
import org.mapstruct.*;

import java.util.List;

/**
 * MapStruct mapper for Brand entity and DTO.
 */
@Mapper(componentModel = "spring", 
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface BrandMapper {
    
    @Mapping(target = "productCount", ignore = true)
    BrandDto toDto(Brand brand);
    
    @Named("toEntity")
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    Brand toEntity(BrandDto brandDto);

    List<BrandDto> toDtoList(List<Brand> brands);

    @IterableMapping(qualifiedByName = "toEntity")
    List<Brand> toEntityList(List<BrandDto> brandDtos);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    void updateEntityFromDto(BrandDto brandDto, @MappingTarget Brand brand);
    
    @Named("toNewEntity")
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    Brand toNewEntity(BrandDto brandDto);
}

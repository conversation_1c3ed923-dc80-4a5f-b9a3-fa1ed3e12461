package com.nttdata.ndvn.product.application.mapper;

import com.nttdata.ndvn.product.application.dto.CategoryDto;
import com.nttdata.ndvn.product.domain.model.Category;
import org.mapstruct.*;

import java.util.List;

/**
 * MapStruct mapper for Category entity and DTO.
 */
@Mapper(componentModel = "spring", 
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface CategoryMapper {
    
    @Mapping(target = "parentName", ignore = true)
    @Mapping(target = "children", ignore = true)
    @Mapping(target = "ancestors", ignore = true)
    @Mapping(target = "hasChildren", ignore = true)
    @Mapping(target = "hasProducts", ignore = true)
    @Mapping(target = "productCount", ignore = true)
    CategoryDto toDto(Category category);
    
    @Named("toEntity")
    @Mapping(target = "parent", ignore = true)
    @Mapping(target = "children", ignore = true)
    @Mapping(target = "descendants", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    Category toEntity(CategoryDto categoryDto);

    List<CategoryDto> toDtoList(List<Category> categories);

    @IterableMapping(qualifiedByName = "toEntity")
    List<Category> toEntityList(List<CategoryDto> categoryDtos);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "parent", ignore = true)
    @Mapping(target = "children", ignore = true)
    @Mapping(target = "descendants", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    void updateEntityFromDto(CategoryDto categoryDto, @MappingTarget Category category);

    @Named("toNewEntity")
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "parent", ignore = true)
    @Mapping(target = "children", ignore = true)
    @Mapping(target = "descendants", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    Category toNewEntity(CategoryDto categoryDto);
}

package com.nttdata.ndvn.product.application.mapper;

import com.nttdata.ndvn.product.application.dto.InventoryDto;
import com.nttdata.ndvn.product.domain.model.Inventory;
import org.mapstruct.*;

import java.util.List;

/**
 * MapStruct mapper for Inventory entity and DTO.
 */
@Mapper(componentModel = "spring", 
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface InventoryMapper {
    
    @Mapping(target = "inStock", ignore = true)
    @Mapping(target = "lowStock", ignore = true)
    @Mapping(target = "outOfStock", ignore = true)
    @Mapping(target = "needsReorder", ignore = true)
    @Mapping(target = "committedQuantity", ignore = true)
    @Mapping(target = "stockUtilization", ignore = true)
    InventoryDto toDto(Inventory inventory);
    
    @Named("toEntity")
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    Inventory toEntity(InventoryDto inventoryDto);

    List<InventoryDto> toDtoList(List<Inventory> inventories);

    @IterableMapping(qualifiedByName = "toEntity")
    List<Inventory> toEntityList(List<InventoryDto> inventoryDtos);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    void updateEntityFromDto(InventoryDto inventoryDto, @MappingTarget Inventory inventory);
    
    @Named("toNewEntity")
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    Inventory toNewEntity(InventoryDto inventoryDto);
}

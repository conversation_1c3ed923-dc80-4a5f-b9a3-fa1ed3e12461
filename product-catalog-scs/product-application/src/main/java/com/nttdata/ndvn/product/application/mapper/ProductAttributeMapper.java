package com.nttdata.ndvn.product.application.mapper;

import com.nttdata.ndvn.product.application.dto.ProductAttributeDto;
import com.nttdata.ndvn.product.domain.model.ProductAttribute;
import org.mapstruct.*;

import java.util.List;

/**
 * MapStruct mapper for ProductAttribute entity and DTO.
 */
@Mapper(componentModel = "spring", 
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface ProductAttributeMapper {
    
    @Mapping(target = "formattedValue", ignore = true)
    @Mapping(target = "validValue", ignore = true)
    ProductAttributeDto toDto(ProductAttribute attribute);
    
    @Named("toEntity")
    @Mapping(target = "product", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    ProductAttribute toEntity(ProductAttributeDto attributeDto);

    List<ProductAttributeDto> toDtoList(List<ProductAttribute> attributes);

    @IterableMapping(qualifiedByName = "toEntity")
    List<ProductAttribute> toEntityList(List<ProductAttributeDto> attributeDtos);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "product", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    void updateEntityFromDto(ProductAttributeDto attributeDto, @MappingTarget ProductAttribute attribute);
    
    @Named("toNewEntity")
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "product", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    ProductAttribute toNewEntity(ProductAttributeDto attributeDto);
}

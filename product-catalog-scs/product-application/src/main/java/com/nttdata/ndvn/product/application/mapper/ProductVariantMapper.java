package com.nttdata.ndvn.product.application.mapper;

import com.nttdata.ndvn.product.application.dto.ProductVariantDto;
import com.nttdata.ndvn.product.domain.model.ProductVariant;
import org.mapstruct.*;

import java.util.List;

/**
 * MapStruct mapper for ProductVariant entity and DTO.
 */
@Mapper(componentModel = "spring", 
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface ProductVariantMapper {
    
    @Mapping(target = "effectivePrice", ignore = true)
    @Mapping(target = "available", ignore = true)
    @Mapping(target = "availableStock", ignore = true)
    ProductVariantDto toDto(ProductVariant variant);
    
    @Named("toEntity")
    @Mapping(target = "product", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    ProductVariant toEntity(ProductVariantDto variantDto);

    List<ProductVariantDto> toDtoList(List<ProductVariant> variants);

    @IterableMapping(qualifiedByName = "toEntity")
    List<ProductVariant> toEntityList(List<ProductVariantDto> variantDtos);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "product", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    void updateEntityFromDto(ProductVariantDto variantDto, @MappingTarget ProductVariant variant);
    
    @Named("toNewEntity")
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "product", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    ProductVariant toNewEntity(ProductVariantDto variantDto);
}

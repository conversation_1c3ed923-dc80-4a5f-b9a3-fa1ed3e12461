package com.nttdata.ndvn.product.application.service;

import com.nttdata.ndvn.product.application.dto.BrandDto;
import com.nttdata.ndvn.product.application.mapper.BrandMapper;
import com.nttdata.ndvn.product.domain.model.Brand;
import com.nttdata.ndvn.product.domain.repository.BrandRepository;
import com.nttdata.ndvn.product.domain.repository.ProductRepository;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Application service for Brand management.
 */
@Service
@Transactional
public class BrandApplicationService {
    
    private final BrandRepository brandRepository;
    private final ProductRepository productRepository;
    private final BrandMapper brandMapper;
    
    public BrandApplicationService(BrandRepository brandRepository,
                                 ProductRepository productRepository,
                                 BrandMapper brandMapper) {
        this.brandRepository = brandRepository;
        this.productRepository = productRepository;
        this.brandMapper = brandMapper;
    }
    
    /**
     * Creates a new brand.
     */
    @CacheEvict(value = {"brands", "brands_by_slug"}, allEntries = true)
    public BrandDto createBrand(BrandDto brandDto) {
        // Generate unique slug if not provided
        if (brandDto.getSlug() == null || brandDto.getSlug().trim().isEmpty()) {
            String slug = generateUniqueSlug(brandDto.getName());
            brandDto.setSlug(slug);
        }
        
        // Validate unique constraints
        if (brandRepository.existsBySlug(brandDto.getSlug())) {
            throw new IllegalArgumentException("Brand slug already exists: " + brandDto.getSlug());
        }
        
        if (brandRepository.existsByName(brandDto.getName())) {
            throw new IllegalArgumentException("Brand name already exists: " + brandDto.getName());
        }
        
        Brand brand = brandMapper.toNewEntity(brandDto);
        Brand savedBrand = brandRepository.save(brand);
        
        return brandMapper.toDto(savedBrand);
    }
    
    /**
     * Updates an existing brand.
     */
    @CacheEvict(value = {"brands", "brands_by_slug"}, allEntries = true)
    public BrandDto updateBrand(UUID brandId, BrandDto brandDto) {
        Brand existingBrand = brandRepository.findById(brandId)
                .orElseThrow(() -> new IllegalArgumentException("Brand not found: " + brandId));
        
        // Check unique constraints if values changed
        if (!existingBrand.getSlug().equals(brandDto.getSlug()) && 
            brandRepository.existsBySlug(brandDto.getSlug())) {
            throw new IllegalArgumentException("Brand slug already exists: " + brandDto.getSlug());
        }
        
        if (!existingBrand.getName().equals(brandDto.getName()) && 
            brandRepository.existsByName(brandDto.getName())) {
            throw new IllegalArgumentException("Brand name already exists: " + brandDto.getName());
        }
        
        // Update brand fields
        brandMapper.updateEntityFromDto(brandDto, existingBrand);
        Brand savedBrand = brandRepository.save(existingBrand);
        
        return brandMapper.toDto(savedBrand);
    }
    
    /**
     * Finds a brand by ID.
     */
    @Cacheable(value = "brands", key = "#brandId")
    @Transactional(readOnly = true)
    public Optional<BrandDto> findBrandById(UUID brandId) {
        return brandRepository.findById(brandId)
                .map(this::enrichBrandDto);
    }
    
    /**
     * Finds a brand by slug.
     */
    @Cacheable(value = "brands_by_slug", key = "#slug")
    @Transactional(readOnly = true)
    public Optional<BrandDto> findBrandBySlug(String slug) {
        return brandRepository.findBySlug(slug)
                .map(this::enrichBrandDto);
    }
    
    /**
     * Finds a brand by name.
     */
    @Transactional(readOnly = true)
    public Optional<BrandDto> findBrandByName(String name) {
        return brandRepository.findByName(name)
                .map(this::enrichBrandDto);
    }
    
    /**
     * Finds all brands with pagination.
     */
    @Transactional(readOnly = true)
    public Page<BrandDto> findAllBrands(Pageable pageable) {
        return brandRepository.findAll(pageable)
                .map(brandMapper::toDto);
    }
    
    /**
     * Finds active brands.
     */
    @Transactional(readOnly = true)
    public Page<BrandDto> findActiveBrands(Pageable pageable) {
        return brandRepository.findByActive(true, pageable)
                .map(brandMapper::toDto);
    }
    
    /**
     * Finds brands ordered by sort order.
     */
    @Transactional(readOnly = true)
    public List<BrandDto> findBrandsOrderedBySortOrder() {
        List<Brand> brands = brandRepository.findAllOrderBySortOrderAsc();
        return brandMapper.toDtoList(brands);
    }
    
    /**
     * Searches brands by name or description.
     */
    @Transactional(readOnly = true)
    public Page<BrandDto> searchBrands(String searchTerm, Pageable pageable) {
        return brandRepository.searchBrands(searchTerm, pageable)
                .map(brandMapper::toDto);
    }
    
    /**
     * Finds brands with logos.
     */
    @Transactional(readOnly = true)
    public Page<BrandDto> findBrandsWithLogos(Pageable pageable) {
        return brandRepository.findByLogoUrlIsNotNull(pageable)
                .map(brandMapper::toDto);
    }
    
    /**
     * Finds brands without logos.
     */
    @Transactional(readOnly = true)
    public Page<BrandDto> findBrandsWithoutLogos(Pageable pageable) {
        return brandRepository.findByLogoUrlIsNull(pageable)
                .map(brandMapper::toDto);
    }
    
    /**
     * Activates a brand.
     */
    @CacheEvict(value = {"brands", "brands_by_slug"}, allEntries = true)
    public BrandDto activateBrand(UUID brandId) {
        Brand brand = brandRepository.findById(brandId)
                .orElseThrow(() -> new IllegalArgumentException("Brand not found: " + brandId));
        
        brand.activate();
        Brand savedBrand = brandRepository.save(brand);
        
        return brandMapper.toDto(savedBrand);
    }
    
    /**
     * Deactivates a brand.
     */
    @CacheEvict(value = {"brands", "brands_by_slug"}, allEntries = true)
    public BrandDto deactivateBrand(UUID brandId) {
        Brand brand = brandRepository.findById(brandId)
                .orElseThrow(() -> new IllegalArgumentException("Brand not found: " + brandId));
        
        brand.deactivate();
        Brand savedBrand = brandRepository.save(brand);
        
        return brandMapper.toDto(savedBrand);
    }
    
    /**
     * Updates brand assets (logo, website).
     */
    @CacheEvict(value = {"brands", "brands_by_slug"}, allEntries = true)
    public BrandDto updateBrandAssets(UUID brandId, String logoUrl, String websiteUrl) {
        Brand brand = brandRepository.findById(brandId)
                .orElseThrow(() -> new IllegalArgumentException("Brand not found: " + brandId));
        
        brand.updateAssets(logoUrl, websiteUrl);
        Brand savedBrand = brandRepository.save(brand);
        
        return brandMapper.toDto(savedBrand);
    }
    
    /**
     * Updates brand sort order.
     */
    @CacheEvict(value = {"brands", "brands_by_slug"}, allEntries = true)
    public BrandDto updateBrandSortOrder(UUID brandId, Integer sortOrder) {
        Brand brand = brandRepository.findById(brandId)
                .orElseThrow(() -> new IllegalArgumentException("Brand not found: " + brandId));
        
        brand.updateSortOrder(sortOrder);
        Brand savedBrand = brandRepository.save(brand);
        
        return brandMapper.toDto(savedBrand);
    }
    
    /**
     * Updates brand SEO metadata.
     */
    @CacheEvict(value = {"brands", "brands_by_slug"}, allEntries = true)
    public BrandDto updateBrandSeoMetadata(UUID brandId, String metaTitle, 
                                          String metaDescription, String metaKeywords) {
        Brand brand = brandRepository.findById(brandId)
                .orElseThrow(() -> new IllegalArgumentException("Brand not found: " + brandId));
        
        brand.updateSeoMetadata(metaTitle, metaDescription, metaKeywords);
        Brand savedBrand = brandRepository.save(brand);
        
        return brandMapper.toDto(savedBrand);
    }
    
    /**
     * Deletes a brand.
     */
    @CacheEvict(value = {"brands", "brands_by_slug"}, allEntries = true)
    public void deleteBrand(UUID brandId) {
        Brand brand = brandRepository.findById(brandId)
                .orElseThrow(() -> new IllegalArgumentException("Brand not found: " + brandId));
        
        // Check if brand has products
        long productCount = productRepository.countByBrandId(brandId);
        if (productCount > 0) {
            throw new IllegalStateException("Cannot delete brand - it has " + productCount + " products");
        }
        
        brandRepository.delete(brand);
    }
    
    /**
     * Gets popular brands.
     */
    @Transactional(readOnly = true)
    public List<BrandDto> getPopularBrands(int limit) {
        List<Brand> brands = brandRepository.findPopularBrands(limit);
        return brands.stream()
                .map(this::enrichBrandDto)
                .toList();
    }
    
    /**
     * Gets brand statistics.
     */
    @Transactional(readOnly = true)
    public BrandStatistics getBrandStatistics() {
        long totalBrands = brandRepository.count();
        long activeBrands = brandRepository.countByActive(true);
        long brandsWithProducts = brandRepository.countBrandsWithProducts();
        
        return new BrandStatistics(totalBrands, activeBrands, brandsWithProducts);
    }
    
    /**
     * Enriches brand DTO with computed fields.
     */
    private BrandDto enrichBrandDto(Brand brand) {
        BrandDto dto = brandMapper.toDto(brand);
        
        // Set product count
        long productCount = productRepository.countByBrandId(brand.getId());
        dto.setProductCount(productCount);
        
        return dto;
    }
    
    /**
     * Generates a unique slug from brand name.
     */
    private String generateUniqueSlug(String name) {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("Name is required to generate slug");
        }
        
        String baseSlug = name.toLowerCase()
                .replaceAll("[^a-z0-9\\s-]", "")
                .replaceAll("\\s+", "-")
                .replaceAll("-+", "-")
                .replaceAll("^-|-$", "");
        
        String slug = baseSlug;
        int counter = 1;
        
        while (brandRepository.existsBySlug(slug)) {
            slug = baseSlug + "-" + counter;
            counter++;
        }
        
        return slug;
    }
    
    /**
     * Brand statistics record.
     */
    public record BrandStatistics(long totalBrands, long activeBrands, long brandsWithProducts) {}
}

package com.nttdata.ndvn.product.application.service;

import com.nttdata.ndvn.product.application.dto.CategoryDto;
import com.nttdata.ndvn.product.application.mapper.CategoryMapper;
import com.nttdata.ndvn.product.domain.model.Category;
import com.nttdata.ndvn.product.domain.repository.CategoryRepository;
import com.nttdata.ndvn.product.domain.service.CategoryDomainService;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Application service for Category management.
 */
@Service
@Transactional
public class CategoryApplicationService {
    
    private final CategoryRepository categoryRepository;
    private final CategoryDomainService categoryDomainService;
    private final CategoryMapper categoryMapper;
    
    public CategoryApplicationService(CategoryRepository categoryRepository,
                                    CategoryDomainService categoryDomainService,
                                    CategoryMapper categoryMapper) {
        this.categoryRepository = categoryRepository;
        this.categoryDomainService = categoryDomainService;
        this.categoryMapper = categoryMapper;
    }
    
    /**
     * Creates a new category.
     */
    @CacheEvict(value = {"categories", "category_tree", "categories_by_slug"}, allEntries = true)
    public CategoryDto createCategory(CategoryDto categoryDto) {
        // Generate unique slug if not provided
        if (categoryDto.getSlug() == null || categoryDto.getSlug().trim().isEmpty()) {
            String slug = categoryDomainService.generateUniqueSlug(categoryDto.getName());
            categoryDto.setSlug(slug);
        }
        
        Category category = categoryMapper.toNewEntity(categoryDto);
        
        // Validate category for creation
        categoryDomainService.validateCategoryForCreation(category);
        
        // Save category
        Category savedCategory = categoryRepository.save(category);
        
        // Update hierarchy if needed
        if (savedCategory.getParentId() != null) {
            categoryDomainService.updateCategoryHierarchy(savedCategory);
        }
        
        return categoryMapper.toDto(savedCategory);
    }
    
    /**
     * Updates an existing category.
     */
    @CacheEvict(value = {"categories", "category_tree", "categories_by_slug"}, allEntries = true)
    public CategoryDto updateCategory(UUID categoryId, CategoryDto categoryDto) {
        Category existingCategory = categoryRepository.findById(categoryId)
                .orElseThrow(() -> new IllegalArgumentException("Category not found: " + categoryId));
        
        // Update category fields
        categoryMapper.updateEntityFromDto(categoryDto, existingCategory);
        
        // Validate category for update
        categoryDomainService.validateCategoryForUpdate(existingCategory);
        
        // Save updated category
        Category savedCategory = categoryRepository.save(existingCategory);
        
        // Update hierarchy for this category and its children
        categoryDomainService.updateCategoryHierarchy(savedCategory);
        
        return categoryMapper.toDto(savedCategory);
    }
    
    /**
     * Finds a category by ID.
     */
    @Cacheable(value = "categories", key = "#categoryId")
    @Transactional(readOnly = true)
    public Optional<CategoryDto> findCategoryById(UUID categoryId) {
        return categoryRepository.findById(categoryId)
                .map(this::enrichCategoryDto);
    }
    
    /**
     * Finds a category by slug.
     */
    @Cacheable(value = "categories_by_slug", key = "#slug")
    @Transactional(readOnly = true)
    public Optional<CategoryDto> findCategoryBySlug(String slug) {
        return categoryRepository.findBySlug(slug)
                .map(this::enrichCategoryDto);
    }
    
    /**
     * Finds all categories with pagination.
     */
    @Transactional(readOnly = true)
    public Page<CategoryDto> findAllCategories(Pageable pageable) {
        return categoryRepository.findAll(pageable)
                .map(categoryMapper::toDto);
    }
    
    /**
     * Finds active categories.
     */
    @Transactional(readOnly = true)
    public Page<CategoryDto> findActiveCategories(Pageable pageable) {
        return categoryRepository.findByActive(true, pageable)
                .map(categoryMapper::toDto);
    }
    
    /**
     * Finds root categories.
     */
    @Transactional(readOnly = true)
    public List<CategoryDto> findRootCategories() {
        List<Category> categories = categoryRepository.findRootCategories();
        return categories.stream()
                .map(this::enrichCategoryDto)
                .toList();
    }
    
    /**
     * Finds categories by parent.
     */
    @Transactional(readOnly = true)
    public List<CategoryDto> findCategoriesByParent(UUID parentId) {
        List<Category> categories = categoryRepository.findByParentIdOrderBySortOrderAsc(parentId);
        return categories.stream()
                .map(this::enrichCategoryDto)
                .toList();
    }
    
    /**
     * Builds complete category tree.
     */
    @Cacheable(value = "category_tree")
    @Transactional(readOnly = true)
    public List<CategoryDto> buildCategoryTree() {
        List<Category> rootCategories = categoryRepository.buildCategoryTree();
        return rootCategories.stream()
                .map(this::mapCategoryTreeToDto)
                .toList();
    }
    
    /**
     * Builds category tree from specific parent.
     */
    @Transactional(readOnly = true)
    public List<CategoryDto> buildCategoryTree(UUID parentId) {
        List<Category> categories = categoryRepository.buildCategoryTree(parentId);
        return categories.stream()
                .map(this::mapCategoryTreeToDto)
                .toList();
    }
    
    /**
     * Gets category breadcrumb.
     */
    @Transactional(readOnly = true)
    public List<CategoryDto> getCategoryBreadcrumb(UUID categoryId) {
        List<Category> breadcrumb = categoryRepository.getCategoryBreadcrumb(categoryId);
        return categoryMapper.toDtoList(breadcrumb);
    }
    
    /**
     * Searches categories.
     */
    @Transactional(readOnly = true)
    public Page<CategoryDto> searchCategories(String searchTerm, Pageable pageable) {
        return categoryRepository.searchCategories(searchTerm, pageable)
                .map(categoryMapper::toDto);
    }
    
    /**
     * Moves a category to a new parent.
     */
    @CacheEvict(value = {"categories", "category_tree", "categories_by_slug"}, allEntries = true)
    public CategoryDto moveCategory(UUID categoryId, UUID newParentId) {
        Category category = categoryRepository.findById(categoryId)
                .orElseThrow(() -> new IllegalArgumentException("Category not found: " + categoryId));
        
        Category newParent = null;
        if (newParentId != null) {
            newParent = categoryRepository.findById(newParentId)
                    .orElseThrow(() -> new IllegalArgumentException("Parent category not found: " + newParentId));
        }
        
        // Set new parent and update hierarchy
        category.setParent(newParent);
        Category savedCategory = categoryRepository.save(category);
        
        return categoryMapper.toDto(savedCategory);
    }
    
    /**
     * Reorders categories within the same parent.
     */
    @CacheEvict(value = {"categories", "category_tree"}, allEntries = true)
    public void reorderCategories(UUID parentId, List<UUID> categoryIds) {
        categoryRepository.reorderCategories(parentId, categoryIds);
    }
    
    /**
     * Deletes a category.
     */
    @CacheEvict(value = {"categories", "category_tree", "categories_by_slug"}, allEntries = true)
    public void deleteCategory(UUID categoryId) {
        Category category = categoryRepository.findById(categoryId)
                .orElseThrow(() -> new IllegalArgumentException("Category not found: " + categoryId));
        
        // Check if category can be deleted
        if (!categoryDomainService.canDeleteCategory(category)) {
            throw new IllegalStateException("Category cannot be deleted - it has children or products");
        }
        
        categoryRepository.delete(category);
    }
    
    /**
     * Gets popular categories.
     */
    @Transactional(readOnly = true)
    public List<CategoryDto> getPopularCategories(int limit) {
        List<Category> categories = categoryRepository.findPopularCategories(limit);
        return categoryMapper.toDtoList(categories);
    }
    
    /**
     * Gets categories for navigation.
     */
    @Transactional(readOnly = true)
    public List<CategoryDto> getCategoriesForNavigation(Integer maxLevel) {
        List<Category> categories = categoryRepository.findCategoriesForNavigation(maxLevel);
        return categoryMapper.toDtoList(categories);
    }
    
    /**
     * Gets category statistics.
     */
    @Transactional(readOnly = true)
    public CategoryStatistics getCategoryStatistics() {
        long totalCategories = categoryRepository.count();
        long activeCategories = categoryRepository.countByActive(true);
        long rootCategories = categoryRepository.countRootCategories();
        long leafCategories = categoryRepository.countLeafCategories();
        
        return new CategoryStatistics(totalCategories, activeCategories, rootCategories, leafCategories);
    }
    
    /**
     * Enriches category DTO with computed fields.
     */
    private CategoryDto enrichCategoryDto(Category category) {
        CategoryDto dto = categoryMapper.toDto(category);
        
        // Set computed fields
        dto.setHasChildren(categoryRepository.hasChildren(category.getId()));
        dto.setHasProducts(categoryRepository.hasProducts(category.getId()));
        dto.setProductCount(categoryRepository.countByParentId(category.getId()));
        
        // Set parent name if exists
        if (category.getParentId() != null) {
            categoryRepository.findById(category.getParentId())
                    .ifPresent(parent -> dto.setParentName(parent.getName()));
        }
        
        return dto;
    }
    
    /**
     * Maps category tree to DTO with children.
     */
    private CategoryDto mapCategoryTreeToDto(Category category) {
        CategoryDto dto = enrichCategoryDto(category);
        
        // Map children recursively
        if (category.getChildren() != null && !category.getChildren().isEmpty()) {
            List<CategoryDto> childrenDtos = category.getChildren().stream()
                    .map(this::mapCategoryTreeToDto)
                    .toList();
            dto.setChildren(childrenDtos);
        }
        
        return dto;
    }
    
    /**
     * Category statistics record.
     */
    public record CategoryStatistics(long totalCategories, long activeCategories, 
                                   long rootCategories, long leafCategories) {}
}

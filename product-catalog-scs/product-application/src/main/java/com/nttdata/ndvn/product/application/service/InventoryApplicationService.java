package com.nttdata.ndvn.product.application.service;

import com.nttdata.ndvn.product.application.dto.InventoryDto;
import com.nttdata.ndvn.product.application.mapper.InventoryMapper;
import com.nttdata.ndvn.product.domain.model.Inventory;
import com.nttdata.ndvn.product.domain.repository.InventoryRepository;
import com.nttdata.ndvn.product.domain.service.InventoryDomainService;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Application service for Inventory management.
 */
@Service
@Transactional
public class InventoryApplicationService {
    
    private final InventoryRepository inventoryRepository;
    private final InventoryDomainService inventoryDomainService;
    private final InventoryMapper inventoryMapper;
    
    public InventoryApplicationService(InventoryRepository inventoryRepository,
                                     InventoryDomainService inventoryDomainService,
                                     InventoryMapper inventoryMapper) {
        this.inventoryRepository = inventoryRepository;
        this.inventoryDomainService = inventoryDomainService;
        this.inventoryMapper = inventoryMapper;
    }
    
    /**
     * Creates new inventory record.
     */
    @CacheEvict(value = {"inventory", "inventory_summary"}, allEntries = true)
    public InventoryDto createInventory(InventoryDto inventoryDto) {
        Inventory inventory = inventoryMapper.toNewEntity(inventoryDto);
        
        // Validate inventory for creation
        inventoryDomainService.validateInventoryForCreation(inventory);
        
        // Save inventory
        Inventory savedInventory = inventoryRepository.save(inventory);
        
        return enrichInventoryDto(savedInventory);
    }
    
    /**
     * Updates existing inventory.
     */
    @CacheEvict(value = {"inventory", "inventory_summary"}, allEntries = true)
    public InventoryDto updateInventory(UUID inventoryId, InventoryDto inventoryDto) {
        Inventory existingInventory = inventoryRepository.findById(inventoryId)
                .orElseThrow(() -> new IllegalArgumentException("Inventory not found: " + inventoryId));
        
        // Update inventory fields
        inventoryMapper.updateEntityFromDto(inventoryDto, existingInventory);
        
        // Validate inventory for update
        inventoryDomainService.validateInventoryForUpdate(existingInventory);
        
        // Save updated inventory
        Inventory savedInventory = inventoryRepository.save(existingInventory);
        
        return enrichInventoryDto(savedInventory);
    }
    
    /**
     * Finds inventory by ID.
     */
    @Cacheable(value = "inventory", key = "#inventoryId")
    @Transactional(readOnly = true)
    public Optional<InventoryDto> findInventoryById(UUID inventoryId) {
        return inventoryRepository.findById(inventoryId)
                .map(this::enrichInventoryDto);
    }
    
    /**
     * Finds inventory by product and location.
     */
    @Transactional(readOnly = true)
    public Optional<InventoryDto> findInventoryByProductAndLocation(UUID productId, String locationCode) {
        return inventoryRepository.findByProductIdAndLocationCode(productId, locationCode)
                .map(this::enrichInventoryDto);
    }
    
    /**
     * Finds inventory by variant and location.
     */
    @Transactional(readOnly = true)
    public Optional<InventoryDto> findInventoryByVariantAndLocation(UUID variantId, String locationCode) {
        return inventoryRepository.findByVariantIdAndLocationCode(variantId, locationCode)
                .map(this::enrichInventoryDto);
    }
    
    /**
     * Finds all inventory for a product.
     */
    @Transactional(readOnly = true)
    public List<InventoryDto> findInventoryByProduct(UUID productId) {
        List<Inventory> inventories = inventoryRepository.findByProductId(productId);
        return inventories.stream()
                .map(this::enrichInventoryDto)
                .toList();
    }
    
    /**
     * Finds all inventory for a variant.
     */
    @Transactional(readOnly = true)
    public List<InventoryDto> findInventoryByVariant(UUID variantId) {
        List<Inventory> inventories = inventoryRepository.findByVariantId(variantId);
        return inventories.stream()
                .map(this::enrichInventoryDto)
                .toList();
    }
    
    /**
     * Finds inventory by location.
     */
    @Transactional(readOnly = true)
    public Page<InventoryDto> findInventoryByLocation(String locationCode, Pageable pageable) {
        return inventoryRepository.findByLocationCode(locationCode, pageable)
                .map(this::enrichInventoryDto);
    }
    
    /**
     * Finds low stock inventory.
     */
    @Transactional(readOnly = true)
    public List<InventoryDto> findLowStockInventory() {
        List<Inventory> inventories = inventoryRepository.findLowStockInventory();
        return inventories.stream()
                .map(this::enrichInventoryDto)
                .toList();
    }
    
    /**
     * Finds out of stock inventory.
     */
    @Transactional(readOnly = true)
    public List<InventoryDto> findOutOfStockInventory() {
        List<Inventory> inventories = inventoryRepository.findOutOfStockInventory();
        return inventories.stream()
                .map(this::enrichInventoryDto)
                .toList();
    }
    
    /**
     * Finds inventory needing reorder.
     */
    @Transactional(readOnly = true)
    public List<InventoryDto> findInventoryNeedingReorder() {
        List<Inventory> inventories = inventoryRepository.findInventoryNeedingReorder();
        return inventories.stream()
                .map(this::enrichInventoryDto)
                .toList();
    }
    
    /**
     * Reserves stock for an order.
     */
    @CacheEvict(value = {"inventory", "inventory_summary"}, allEntries = true)
    public InventoryDto reserveStock(UUID inventoryId, Integer quantity) {
        Inventory inventory = inventoryRepository.findById(inventoryId)
                .orElseThrow(() -> new IllegalArgumentException("Inventory not found: " + inventoryId));
        
        // Validate stock movement
        inventoryDomainService.validateStockMovement(inventory, quantity, "RESERVE");
        
        // Reserve stock
        inventory.reserveStock(quantity);
        Inventory savedInventory = inventoryRepository.save(inventory);
        
        return enrichInventoryDto(savedInventory);
    }
    
    /**
     * Releases reserved stock.
     */
    @CacheEvict(value = {"inventory", "inventory_summary"}, allEntries = true)
    public InventoryDto releaseReservedStock(UUID inventoryId, Integer quantity) {
        Inventory inventory = inventoryRepository.findById(inventoryId)
                .orElseThrow(() -> new IllegalArgumentException("Inventory not found: " + inventoryId));
        
        // Validate stock movement
        inventoryDomainService.validateStockMovement(inventory, quantity, "RELEASE");
        
        // Release reserved stock
        inventory.releaseReservedStock(quantity);
        Inventory savedInventory = inventoryRepository.save(inventory);
        
        return enrichInventoryDto(savedInventory);
    }
    
    /**
     * Confirms sale and removes stock.
     */
    @CacheEvict(value = {"inventory", "inventory_summary"}, allEntries = true)
    public InventoryDto confirmSale(UUID inventoryId, Integer quantity) {
        Inventory inventory = inventoryRepository.findById(inventoryId)
                .orElseThrow(() -> new IllegalArgumentException("Inventory not found: " + inventoryId));
        
        // Validate stock movement
        inventoryDomainService.validateStockMovement(inventory, quantity, "CONFIRM_SALE");
        
        // Confirm sale
        inventory.confirmSale(quantity);
        Inventory savedInventory = inventoryRepository.save(inventory);
        
        return enrichInventoryDto(savedInventory);
    }
    
    /**
     * Adds stock to inventory.
     */
    @CacheEvict(value = {"inventory", "inventory_summary"}, allEntries = true)
    public InventoryDto addStock(UUID inventoryId, Integer quantity) {
        Inventory inventory = inventoryRepository.findById(inventoryId)
                .orElseThrow(() -> new IllegalArgumentException("Inventory not found: " + inventoryId));
        
        // Add stock
        inventory.addStock(quantity);
        Inventory savedInventory = inventoryRepository.save(inventory);
        
        return enrichInventoryDto(savedInventory);
    }
    
    /**
     * Marks stock as damaged.
     */
    @CacheEvict(value = {"inventory", "inventory_summary"}, allEntries = true)
    public InventoryDto markAsDamaged(UUID inventoryId, Integer quantity) {
        Inventory inventory = inventoryRepository.findById(inventoryId)
                .orElseThrow(() -> new IllegalArgumentException("Inventory not found: " + inventoryId));
        
        // Validate stock movement
        inventoryDomainService.validateStockMovement(inventory, quantity, "MARK_DAMAGED");
        
        // Mark as damaged
        inventory.markAsDamaged(quantity);
        Inventory savedInventory = inventoryRepository.save(inventory);
        
        return enrichInventoryDto(savedInventory);
    }
    
    /**
     * Removes damaged stock.
     */
    @CacheEvict(value = {"inventory", "inventory_summary"}, allEntries = true)
    public InventoryDto removeDamagedStock(UUID inventoryId, Integer quantity) {
        Inventory inventory = inventoryRepository.findById(inventoryId)
                .orElseThrow(() -> new IllegalArgumentException("Inventory not found: " + inventoryId));
        
        // Validate stock movement
        inventoryDomainService.validateStockMovement(inventory, quantity, "REMOVE_DAMAGED");
        
        // Remove damaged stock
        inventory.removeDamagedStock(quantity);
        Inventory savedInventory = inventoryRepository.save(inventory);
        
        return enrichInventoryDto(savedInventory);
    }
    
    /**
     * Updates stock check timestamp.
     */
    @CacheEvict(value = {"inventory", "inventory_summary"}, allEntries = true)
    public InventoryDto updateStockCheck(UUID inventoryId) {
        Inventory inventory = inventoryRepository.findById(inventoryId)
                .orElseThrow(() -> new IllegalArgumentException("Inventory not found: " + inventoryId));
        
        inventory.updateLastStockCheck();
        Inventory savedInventory = inventoryRepository.save(inventory);
        
        return enrichInventoryDto(savedInventory);
    }
    
    /**
     * Gets total available stock for a product.
     */
    @Transactional(readOnly = true)
    public Integer getTotalAvailableStock(UUID productId) {
        return inventoryDomainService.calculateTotalAvailableStock(productId);
    }
    
    /**
     * Gets total stock for a product.
     */
    @Transactional(readOnly = true)
    public Integer getTotalStock(UUID productId) {
        return inventoryDomainService.calculateTotalStock(productId);
    }
    
    /**
     * Checks if product is in stock.
     */
    @Transactional(readOnly = true)
    public boolean isProductInStock(UUID productId) {
        return inventoryDomainService.isProductInStock(productId);
    }
    
    /**
     * Gets inventory summary by location.
     */
    @Cacheable(value = "inventory_summary", key = "'by_location'")
    @Transactional(readOnly = true)
    public List<LocationInventorySummary> getInventorySummaryByLocation() {
        List<Object[]> results = inventoryRepository.findInventorySummaryByLocation();
        return results.stream()
                .map(row -> new LocationInventorySummary(
                        (String) row[0], // locationCode
                        (String) row[1], // locationName
                        ((Number) row[2]).longValue(), // inventoryCount
                        ((Number) row[3]).intValue(), // totalQuantity
                        ((Number) row[4]).intValue()  // availableQuantity
                ))
                .toList();
    }
    
    /**
     * Gets all location codes.
     */
    @Transactional(readOnly = true)
    public List<String> getAllLocationCodes() {
        return inventoryRepository.findAllLocationCodes();
    }
    
    /**
     * Gets inventory statistics.
     */
    @Transactional(readOnly = true)
    public InventoryStatistics getInventoryStatistics() {
        long totalInventoryRecords = inventoryRepository.count();
        long activeInventoryRecords = inventoryRepository.countByActive(true);
        long lowStockCount = inventoryRepository.countLowStockInventory();
        long outOfStockCount = inventoryRepository.countOutOfStockInventory();
        
        return new InventoryStatistics(totalInventoryRecords, activeInventoryRecords, 
                                     lowStockCount, outOfStockCount);
    }
    
    /**
     * Deletes inventory record.
     */
    @CacheEvict(value = {"inventory", "inventory_summary"}, allEntries = true)
    public void deleteInventory(UUID inventoryId) {
        Inventory inventory = inventoryRepository.findById(inventoryId)
                .orElseThrow(() -> new IllegalArgumentException("Inventory not found: " + inventoryId));
        
        // Check if inventory has stock
        if (inventory.getTotalQuantity() > 0) {
            throw new IllegalStateException("Cannot delete inventory with existing stock");
        }
        
        inventoryRepository.delete(inventory);
    }
    
    /**
     * Enriches inventory DTO with computed fields.
     */
    private InventoryDto enrichInventoryDto(Inventory inventory) {
        InventoryDto dto = inventoryMapper.toDto(inventory);
        
        // Set computed fields
        dto.setInStock(inventory.isInStock());
        dto.setLowStock(inventory.isLowStock());
        dto.setOutOfStock(inventory.isOutOfStock());
        dto.setNeedsReorder(inventory.needsReorder());
        dto.setCommittedQuantity(inventory.getCommittedQuantity());
        dto.setStockUtilization(inventory.getStockUtilization());
        
        return dto;
    }
    
    /**
     * Location inventory summary record.
     */
    public record LocationInventorySummary(String locationCode, String locationName, 
                                         long inventoryCount, int totalQuantity, int availableQuantity) {}
    
    /**
     * Inventory statistics record.
     */
    public record InventoryStatistics(long totalInventoryRecords, long activeInventoryRecords,
                                    long lowStockCount, long outOfStockCount) {}
}

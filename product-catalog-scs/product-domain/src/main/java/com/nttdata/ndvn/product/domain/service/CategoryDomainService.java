package com.nttdata.ndvn.product.domain.service;

import com.nttdata.ndvn.product.domain.model.Category;
import com.nttdata.ndvn.product.domain.repository.CategoryRepository;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

/**
 * Domain service for Category-related business logic.
 * 
 * This service encapsulates complex category hierarchy management and business rules.
 */
@Service
public class CategoryDomainService {
    
    private final CategoryRepository categoryRepository;
    
    public CategoryDomainService(CategoryRepository categoryRepository) {
        this.categoryRepository = categoryRepository;
    }
    
    /**
     * Validates category data for creation.
     */
    public void validateCategoryForCreation(Category category) {
        if (category.getName() == null || category.getName().trim().isEmpty()) {
            throw new IllegalArgumentException("Category name is required");
        }
        
        if (category.getSlug() == null || category.getSlug().trim().isEmpty()) {
            throw new IllegalArgumentException("Category slug is required");
        }
        
        if (categoryRepository.existsBySlug(category.getSlug())) {
            throw new IllegalArgumentException("Slug already exists: " + category.getSlug());
        }
        
        validateSlugFormat(category.getSlug());
        validateCategoryHierarchy(category);
    }
    
    /**
     * Validates category data for update.
     */
    public void validateCategoryForUpdate(Category category) {
        if (category.getId() == null) {
            throw new IllegalArgumentException("Category ID is required for update");
        }
        
        if (!categoryRepository.existsById(category.getId())) {
            throw new IllegalArgumentException("Category not found: " + category.getId());
        }
        
        if (category.getName() == null || category.getName().trim().isEmpty()) {
            throw new IllegalArgumentException("Category name is required");
        }
        
        if (category.getSlug() == null || category.getSlug().trim().isEmpty()) {
            throw new IllegalArgumentException("Category slug is required");
        }
        
        validateSlugFormat(category.getSlug());
        validateCategoryHierarchy(category);
    }
    
    /**
     * Validates slug format.
     */
    public void validateSlugFormat(String slug) {
        if (slug == null || slug.trim().isEmpty()) {
            throw new IllegalArgumentException("Slug is required");
        }
        
        if (!slug.matches("^[a-z0-9-]+$")) {
            throw new IllegalArgumentException("Slug must contain only lowercase letters, numbers, and hyphens");
        }
        
        if (slug.startsWith("-") || slug.endsWith("-")) {
            throw new IllegalArgumentException("Slug cannot start or end with a hyphen");
        }
        
        if (slug.contains("--")) {
            throw new IllegalArgumentException("Slug cannot contain consecutive hyphens");
        }
    }
    
    /**
     * Validates category hierarchy rules.
     */
    public void validateCategoryHierarchy(Category category) {
        if (category.getParentId() != null) {
            // Check if parent exists
            if (!categoryRepository.existsById(category.getParentId())) {
                throw new IllegalArgumentException("Parent category not found: " + category.getParentId());
            }
            
            // Check for circular reference
            if (category.getId() != null && category.getParentId().equals(category.getId())) {
                throw new IllegalArgumentException("Category cannot be its own parent");
            }
            
            // Check if setting this parent would create a circular reference
            if (category.getId() != null && wouldCreateCircularReference(category.getId(), category.getParentId())) {
                throw new IllegalArgumentException("Cannot set descendant as parent - would create circular reference");
            }
        }
    }
    
    /**
     * Checks if setting a parent would create a circular reference.
     */
    private boolean wouldCreateCircularReference(UUID categoryId, UUID parentId) {
        if (parentId == null) {
            return false;
        }
        
        Category parent = categoryRepository.findById(parentId).orElse(null);
        if (parent == null) {
            return false;
        }
        
        // Check if the proposed parent is a descendant of the current category
        return isDescendantOf(parent, categoryId);
    }
    
    /**
     * Checks if a category is a descendant of another category.
     */
    private boolean isDescendantOf(Category category, UUID ancestorId) {
        if (category == null || category.getParentId() == null) {
            return false;
        }
        
        if (category.getParentId().equals(ancestorId)) {
            return true;
        }
        
        Category parent = categoryRepository.findById(category.getParentId()).orElse(null);
        return isDescendantOf(parent, ancestorId);
    }
    
    /**
     * Checks if a category can be deleted.
     */
    public boolean canDeleteCategory(Category category) {
        // Cannot delete if it has children
        if (categoryRepository.hasChildren(category.getId())) {
            return false;
        }
        
        // Cannot delete if it has products
        if (categoryRepository.hasProducts(category.getId())) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Calculates the maximum depth of the category tree.
     */
    public Integer calculateMaxDepth() {
        List<Category> allCategories = categoryRepository.findAll(Pageable.unpaged()).getContent();
        return allCategories.stream()
                           .mapToInt(Category::getLevel)
                           .max()
                           .orElse(0);
    }
    
    /**
     * Calculates the depth of a specific category branch.
     */
    public Integer calculateBranchDepth(UUID categoryId) {
        Category category = categoryRepository.findById(categoryId).orElse(null);
        if (category == null) {
            return 0;
        }
        
        List<Category> descendants = categoryRepository.findDescendants(categoryId);
        return descendants.stream()
                         .mapToInt(Category::getLevel)
                         .max()
                         .orElse(category.getLevel()) - category.getLevel();
    }
    
    /**
     * Generates a unique slug from category name.
     */
    public String generateUniqueSlug(String name) {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("Name is required to generate slug");
        }
        
        String baseSlug = generateSlugFromName(name);
        String slug = baseSlug;
        int counter = 1;
        
        while (categoryRepository.existsBySlug(slug)) {
            slug = baseSlug + "-" + counter;
            counter++;
        }
        
        return slug;
    }
    
    /**
     * Generates slug from name.
     */
    public String generateSlugFromName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return "";
        }
        
        return name.toLowerCase()
                  .replaceAll("[^a-z0-9\\s-]", "")
                  .replaceAll("\\s+", "-")
                  .replaceAll("-+", "-")
                  .replaceAll("^-|-$", "");
    }
    
    /**
     * Builds the full category path.
     */
    public String buildCategoryPath(Category category) {
        if (category == null) {
            return "";
        }
        
        if (category.getParentId() == null) {
            return category.getSlug();
        }
        
        Category parent = categoryRepository.findById(category.getParentId()).orElse(null);
        if (parent == null) {
            return category.getSlug();
        }
        
        return buildCategoryPath(parent) + "/" + category.getSlug();
    }
    
    /**
     * Updates category hierarchy information recursively.
     */
    public void updateCategoryHierarchy(Category category) {
        // Update level and path
        if (category.getParentId() == null) {
            category.setLevel(0);
            category.setPath(category.getSlug());
        } else {
            Category parent = categoryRepository.findById(category.getParentId()).orElse(null);
            if (parent != null) {
                category.setLevel(parent.getLevel() + 1);
                category.setPath(parent.getPath() + "/" + category.getSlug());
            }
        }
        
        // Save the updated category
        categoryRepository.save(category);
        
        // Update all children recursively
        List<Category> children = categoryRepository.findByParentId(category.getId());
        for (Category child : children) {
            updateCategoryHierarchy(child);
        }
    }
    
    /**
     * Validates category tree consistency.
     */
    public boolean validateCategoryTreeConsistency() {
        List<Category> allCategories = categoryRepository.findAll(Pageable.unpaged()).getContent();
        
        for (Category category : allCategories) {
            // Check if parent exists (if parentId is not null)
            if (category.getParentId() != null) {
                if (!categoryRepository.existsById(category.getParentId())) {
                    return false; // Parent doesn't exist
                }
            }
            
            // Check if level is correct
            if (category.getParentId() == null && category.getLevel() != 0) {
                return false; // Root category should have level 0
            }
            
            if (category.getParentId() != null) {
                Category parent = categoryRepository.findById(category.getParentId()).orElse(null);
                if (parent != null && category.getLevel() != parent.getLevel() + 1) {
                    return false; // Level should be parent level + 1
                }
            }
            
            // Check if path is correct
            String expectedPath = buildCategoryPath(category);
            if (!expectedPath.equals(category.getPath())) {
                return false; // Path is incorrect
            }
        }
        
        return true;
    }
    
    /**
     * Finds categories that need hierarchy updates.
     */
    public List<Category> findCategoriesNeedingHierarchyUpdate() {
        return categoryRepository.findAll(Pageable.unpaged()).getContent().stream()
                .filter(category -> {
                    String expectedPath = buildCategoryPath(category);
                    return !expectedPath.equals(category.getPath());
                })
                .toList();
    }
    
    /**
     * Reorders categories within the same parent.
     */
    public void reorderCategories(UUID parentId, List<UUID> categoryIds) {
        for (int i = 0; i < categoryIds.size(); i++) {
            Category category = categoryRepository.findById(categoryIds.get(i)).orElse(null);
            if (category != null && 
                ((parentId == null && category.getParentId() == null) || 
                 (parentId != null && parentId.equals(category.getParentId())))) {
                category.setSortOrder(i);
                categoryRepository.save(category);
            }
        }
    }
    
    /**
     * Checks if a category name is unique within its parent.
     */
    public boolean isCategoryNameUniqueInParent(String name, UUID parentId, UUID excludeCategoryId) {
        List<Category> siblings = categoryRepository.findByParentId(parentId);
        
        return siblings.stream()
                      .filter(category -> !category.getId().equals(excludeCategoryId))
                      .noneMatch(category -> category.getName().equalsIgnoreCase(name));
    }
}

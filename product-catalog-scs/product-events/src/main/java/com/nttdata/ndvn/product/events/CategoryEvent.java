package com.nttdata.ndvn.product.events;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Base class for category-related domain events.
 */
public abstract class CategoryEvent {
    
    private final UUID categoryId;
    private final String categoryName;
    private final String categorySlug;
    private final LocalDateTime occurredAt;
    private final String eventType;
    
    protected CategoryEvent(UUID categoryId, String categoryName, String categorySlug, String eventType) {
        this.categoryId = categoryId;
        this.categoryName = categoryName;
        this.categorySlug = categorySlug;
        this.eventType = eventType;
        this.occurredAt = LocalDateTime.now();
    }
    
    // Getters
    public UUID getCategoryId() { return categoryId; }
    public String getCategoryName() { return categoryName; }
    public String getCategorySlug() { return categorySlug; }
    public LocalDateTime getOccurredAt() { return occurredAt; }
    public String getEventType() { return eventType; }
    
    /**
     * Category created event.
     */
    public static class CategoryCreated extends CategoryEvent {
        private final UUID parentId;
        private final Integer level;
        private final String path;
        
        public CategoryCreated(UUID categoryId, String categoryName, String categorySlug,
                             UUID parentId, Integer level, String path) {
            super(categoryId, categoryName, categorySlug, "CATEGORY_CREATED");
            this.parentId = parentId;
            this.level = level;
            this.path = path;
        }
        
        public UUID getParentId() { return parentId; }
        public Integer getLevel() { return level; }
        public String getPath() { return path; }
    }
    
    /**
     * Category updated event.
     */
    public static class CategoryUpdated extends CategoryEvent {
        private final UUID parentId;
        private final Integer level;
        private final String path;
        private final boolean active;
        
        public CategoryUpdated(UUID categoryId, String categoryName, String categorySlug,
                             UUID parentId, Integer level, String path, boolean active) {
            super(categoryId, categoryName, categorySlug, "CATEGORY_UPDATED");
            this.parentId = parentId;
            this.level = level;
            this.path = path;
            this.active = active;
        }
        
        public UUID getParentId() { return parentId; }
        public Integer getLevel() { return level; }
        public String getPath() { return path; }
        public boolean isActive() { return active; }
    }
    
    /**
     * Category moved event.
     */
    public static class CategoryMoved extends CategoryEvent {
        private final UUID oldParentId;
        private final UUID newParentId;
        private final String oldPath;
        private final String newPath;
        
        public CategoryMoved(UUID categoryId, String categoryName, String categorySlug,
                           UUID oldParentId, UUID newParentId, String oldPath, String newPath) {
            super(categoryId, categoryName, categorySlug, "CATEGORY_MOVED");
            this.oldParentId = oldParentId;
            this.newParentId = newParentId;
            this.oldPath = oldPath;
            this.newPath = newPath;
        }
        
        public UUID getOldParentId() { return oldParentId; }
        public UUID getNewParentId() { return newParentId; }
        public String getOldPath() { return oldPath; }
        public String getNewPath() { return newPath; }
    }
    
    /**
     * Category deleted event.
     */
    public static class CategoryDeleted extends CategoryEvent {
        private final UUID parentId;
        private final String path;
        
        public CategoryDeleted(UUID categoryId, String categoryName, String categorySlug,
                             UUID parentId, String path) {
            super(categoryId, categoryName, categorySlug, "CATEGORY_DELETED");
            this.parentId = parentId;
            this.path = path;
        }
        
        public UUID getParentId() { return parentId; }
        public String getPath() { return path; }
    }
}

package com.nttdata.ndvn.product.events;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Base class for inventory-related domain events.
 */
public abstract class InventoryEvent {
    
    private final UUID inventoryId;
    private final UUID productId;
    private final UUID variantId;
    private final String locationCode;
    private final LocalDateTime occurredAt;
    private final String eventType;
    
    protected InventoryEvent(UUID inventoryId, UUID productId, UUID variantId, 
                           String locationCode, String eventType) {
        this.inventoryId = inventoryId;
        this.productId = productId;
        this.variantId = variantId;
        this.locationCode = locationCode;
        this.eventType = eventType;
        this.occurredAt = LocalDateTime.now();
    }
    
    // Getters
    public UUID getInventoryId() { return inventoryId; }
    public UUID getProductId() { return productId; }
    public UUID getVariantId() { return variantId; }
    public String getLocationCode() { return locationCode; }
    public LocalDateTime getOccurredAt() { return occurredAt; }
    public String getEventType() { return eventType; }
    
    /**
     * Stock reserved event.
     */
    public static class StockReserved extends InventoryEvent {
        private final Integer quantity;
        private final Integer remainingAvailable;
        private final String reservationReason;
        
        public StockReserved(UUID inventoryId, UUID productId, UUID variantId, String locationCode,
                           Integer quantity, Integer remainingAvailable, String reservationReason) {
            super(inventoryId, productId, variantId, locationCode, "STOCK_RESERVED");
            this.quantity = quantity;
            this.remainingAvailable = remainingAvailable;
            this.reservationReason = reservationReason;
        }
        
        public Integer getQuantity() { return quantity; }
        public Integer getRemainingAvailable() { return remainingAvailable; }
        public String getReservationReason() { return reservationReason; }
    }
    
    /**
     * Stock released event.
     */
    public static class StockReleased extends InventoryEvent {
        private final Integer quantity;
        private final Integer newAvailable;
        private final String releaseReason;
        
        public StockReleased(UUID inventoryId, UUID productId, UUID variantId, String locationCode,
                           Integer quantity, Integer newAvailable, String releaseReason) {
            super(inventoryId, productId, variantId, locationCode, "STOCK_RELEASED");
            this.quantity = quantity;
            this.newAvailable = newAvailable;
            this.releaseReason = releaseReason;
        }
        
        public Integer getQuantity() { return quantity; }
        public Integer getNewAvailable() { return newAvailable; }
        public String getReleaseReason() { return releaseReason; }
    }
    
    /**
     * Stock sold event.
     */
    public static class StockSold extends InventoryEvent {
        private final Integer quantity;
        private final Integer remainingTotal;
        private final String orderId;
        
        public StockSold(UUID inventoryId, UUID productId, UUID variantId, String locationCode,
                        Integer quantity, Integer remainingTotal, String orderId) {
            super(inventoryId, productId, variantId, locationCode, "STOCK_SOLD");
            this.quantity = quantity;
            this.remainingTotal = remainingTotal;
            this.orderId = orderId;
        }
        
        public Integer getQuantity() { return quantity; }
        public Integer getRemainingTotal() { return remainingTotal; }
        public String getOrderId() { return orderId; }
    }
    
    /**
     * Stock added event.
     */
    public static class StockAdded extends InventoryEvent {
        private final Integer quantity;
        private final Integer newTotal;
        private final String reason;
        
        public StockAdded(UUID inventoryId, UUID productId, UUID variantId, String locationCode,
                         Integer quantity, Integer newTotal, String reason) {
            super(inventoryId, productId, variantId, locationCode, "STOCK_ADDED");
            this.quantity = quantity;
            this.newTotal = newTotal;
            this.reason = reason;
        }
        
        public Integer getQuantity() { return quantity; }
        public Integer getNewTotal() { return newTotal; }
        public String getReason() { return reason; }
    }
    
    /**
     * Low stock alert event.
     */
    public static class LowStockAlert extends InventoryEvent {
        private final Integer currentQuantity;
        private final Integer reorderPoint;
        private final Integer reorderQuantity;
        
        public LowStockAlert(UUID inventoryId, UUID productId, UUID variantId, String locationCode,
                           Integer currentQuantity, Integer reorderPoint, Integer reorderQuantity) {
            super(inventoryId, productId, variantId, locationCode, "LOW_STOCK_ALERT");
            this.currentQuantity = currentQuantity;
            this.reorderPoint = reorderPoint;
            this.reorderQuantity = reorderQuantity;
        }
        
        public Integer getCurrentQuantity() { return currentQuantity; }
        public Integer getReorderPoint() { return reorderPoint; }
        public Integer getReorderQuantity() { return reorderQuantity; }
    }
    
    /**
     * Out of stock event.
     */
    public static class OutOfStock extends InventoryEvent {
        private final LocalDateTime stockDepletedAt;
        
        public OutOfStock(UUID inventoryId, UUID productId, UUID variantId, String locationCode) {
            super(inventoryId, productId, variantId, locationCode, "OUT_OF_STOCK");
            this.stockDepletedAt = LocalDateTime.now();
        }
        
        public LocalDateTime getStockDepletedAt() { return stockDepletedAt; }
    }
    
    /**
     * Stock damaged event.
     */
    public static class StockDamaged extends InventoryEvent {
        private final Integer quantity;
        private final Integer remainingAvailable;
        private final String damageReason;
        
        public StockDamaged(UUID inventoryId, UUID productId, UUID variantId, String locationCode,
                          Integer quantity, Integer remainingAvailable, String damageReason) {
            super(inventoryId, productId, variantId, locationCode, "STOCK_DAMAGED");
            this.quantity = quantity;
            this.remainingAvailable = remainingAvailable;
            this.damageReason = damageReason;
        }
        
        public Integer getQuantity() { return quantity; }
        public Integer getRemainingAvailable() { return remainingAvailable; }
        public String getDamageReason() { return damageReason; }
    }
}

package com.nttdata.ndvn.product.events;

import com.nttdata.ndvn.product.domain.model.ProductStatus;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Base class for product-related domain events.
 */
public abstract class ProductEvent {
    
    private final UUID productId;
    private final String productSku;
    private final String productName;
    private final LocalDateTime occurredAt;
    private final String eventType;
    
    protected ProductEvent(UUID productId, String productSku, String productName, String eventType) {
        this.productId = productId;
        this.productSku = productSku;
        this.productName = productName;
        this.eventType = eventType;
        this.occurredAt = LocalDateTime.now();
    }
    
    // Getters
    public UUID getProductId() { return productId; }
    public String getProductSku() { return productSku; }
    public String getProductName() { return productName; }
    public LocalDateTime getOccurredAt() { return occurredAt; }
    public String getEventType() { return eventType; }
    
    /**
     * Product created event.
     */
    public static class ProductCreated extends ProductEvent {
        private final UUID categoryId;
        private final UUID brandId;
        private final BigDecimal basePrice;
        private final ProductStatus status;
        
        public ProductCreated(UUID productId, String productSku, String productName,
                            UUID categoryId, UUID brandId, BigDecimal basePrice, ProductStatus status) {
            super(productId, productSku, productName, "PRODUCT_CREATED");
            this.categoryId = categoryId;
            this.brandId = brandId;
            this.basePrice = basePrice;
            this.status = status;
        }
        
        public UUID getCategoryId() { return categoryId; }
        public UUID getBrandId() { return brandId; }
        public BigDecimal getBasePrice() { return basePrice; }
        public ProductStatus getStatus() { return status; }
    }
    
    /**
     * Product updated event.
     */
    public static class ProductUpdated extends ProductEvent {
        private final UUID categoryId;
        private final UUID brandId;
        private final BigDecimal basePrice;
        private final ProductStatus status;
        
        public ProductUpdated(UUID productId, String productSku, String productName,
                            UUID categoryId, UUID brandId, BigDecimal basePrice, ProductStatus status) {
            super(productId, productSku, productName, "PRODUCT_UPDATED");
            this.categoryId = categoryId;
            this.brandId = brandId;
            this.basePrice = basePrice;
            this.status = status;
        }
        
        public UUID getCategoryId() { return categoryId; }
        public UUID getBrandId() { return brandId; }
        public BigDecimal getBasePrice() { return basePrice; }
        public ProductStatus getStatus() { return status; }
    }
    
    /**
     * Product status changed event.
     */
    public static class ProductStatusChanged extends ProductEvent {
        private final ProductStatus oldStatus;
        private final ProductStatus newStatus;
        
        public ProductStatusChanged(UUID productId, String productSku, String productName,
                                  ProductStatus oldStatus, ProductStatus newStatus) {
            super(productId, productSku, productName, "PRODUCT_STATUS_CHANGED");
            this.oldStatus = oldStatus;
            this.newStatus = newStatus;
        }
        
        public ProductStatus getOldStatus() { return oldStatus; }
        public ProductStatus getNewStatus() { return newStatus; }
    }
    
    /**
     * Product pricing updated event.
     */
    public static class ProductPricingUpdated extends ProductEvent {
        private final BigDecimal oldBasePrice;
        private final BigDecimal newBasePrice;
        private final BigDecimal oldSalePrice;
        private final BigDecimal newSalePrice;
        
        public ProductPricingUpdated(UUID productId, String productSku, String productName,
                                   BigDecimal oldBasePrice, BigDecimal newBasePrice,
                                   BigDecimal oldSalePrice, BigDecimal newSalePrice) {
            super(productId, productSku, productName, "PRODUCT_PRICING_UPDATED");
            this.oldBasePrice = oldBasePrice;
            this.newBasePrice = newBasePrice;
            this.oldSalePrice = oldSalePrice;
            this.newSalePrice = newSalePrice;
        }
        
        public BigDecimal getOldBasePrice() { return oldBasePrice; }
        public BigDecimal getNewBasePrice() { return newBasePrice; }
        public BigDecimal getOldSalePrice() { return oldSalePrice; }
        public BigDecimal getNewSalePrice() { return newSalePrice; }
    }
    
    /**
     * Product deleted event.
     */
    public static class ProductDeleted extends ProductEvent {
        private final ProductStatus lastStatus;
        
        public ProductDeleted(UUID productId, String productSku, String productName, ProductStatus lastStatus) {
            super(productId, productSku, productName, "PRODUCT_DELETED");
            this.lastStatus = lastStatus;
        }
        
        public ProductStatus getLastStatus() { return lastStatus; }
    }
}

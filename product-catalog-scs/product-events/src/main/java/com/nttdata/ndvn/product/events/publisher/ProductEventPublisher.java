package com.nttdata.ndvn.product.events.publisher;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nttdata.ndvn.product.events.CategoryEvent;
import com.nttdata.ndvn.product.events.InventoryEvent;
import com.nttdata.ndvn.product.events.ProductEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

/**
 * Publisher for product catalog domain events.
 * 
 * This component publishes domain events to Kafka topics for consumption
 * by other services and systems.
 */
@Component
public class ProductEventPublisher {
    
    private static final Logger logger = LoggerFactory.getLogger(ProductEventPublisher.class);
    
    // Kafka topics
    private static final String PRODUCT_EVENTS_TOPIC = "product-catalog.product.events";
    private static final String INVENTORY_EVENTS_TOPIC = "product-catalog.inventory.events";
    private static final String CATEGORY_EVENTS_TOPIC = "product-catalog.category.events";
    
    private final KafkaTemplate<String, String> kafkaTemplate;
    private final ObjectMapper objectMapper;
    
    public ProductEventPublisher(KafkaTemplate<String, String> kafkaTemplate, ObjectMapper objectMapper) {
        this.kafkaTemplate = kafkaTemplate;
        this.objectMapper = objectMapper;
    }
    
    /**
     * Publishes a product event.
     */
    public void publishProductEvent(ProductEvent event) {
        try {
            String eventJson = objectMapper.writeValueAsString(event);
            String key = event.getProductId().toString();
            
            kafkaTemplate.send(PRODUCT_EVENTS_TOPIC, key, eventJson)
                    .whenComplete((result, ex) -> {
                        if (ex == null) {
                            logger.info("Published product event: {} for product: {}", 
                                      event.getEventType(), event.getProductId());
                        } else {
                            logger.error("Failed to publish product event: {} for product: {}", 
                                       event.getEventType(), event.getProductId(), ex);
                        }
                    });
                    
        } catch (JsonProcessingException e) {
            logger.error("Failed to serialize product event: {}", event.getEventType(), e);
        }
    }
    
    /**
     * Publishes an inventory event.
     */
    public void publishInventoryEvent(InventoryEvent event) {
        try {
            String eventJson = objectMapper.writeValueAsString(event);
            String key = event.getProductId().toString() + ":" + event.getLocationCode();
            
            kafkaTemplate.send(INVENTORY_EVENTS_TOPIC, key, eventJson)
                    .whenComplete((result, ex) -> {
                        if (ex == null) {
                            logger.info("Published inventory event: {} for product: {} at location: {}", 
                                      event.getEventType(), event.getProductId(), event.getLocationCode());
                        } else {
                            logger.error("Failed to publish inventory event: {} for product: {} at location: {}", 
                                       event.getEventType(), event.getProductId(), event.getLocationCode(), ex);
                        }
                    });
                    
        } catch (JsonProcessingException e) {
            logger.error("Failed to serialize inventory event: {}", event.getEventType(), e);
        }
    }
    
    /**
     * Publishes a category event.
     */
    public void publishCategoryEvent(CategoryEvent event) {
        try {
            String eventJson = objectMapper.writeValueAsString(event);
            String key = event.getCategoryId().toString();
            
            kafkaTemplate.send(CATEGORY_EVENTS_TOPIC, key, eventJson)
                    .whenComplete((result, ex) -> {
                        if (ex == null) {
                            logger.info("Published category event: {} for category: {}", 
                                      event.getEventType(), event.getCategoryId());
                        } else {
                            logger.error("Failed to publish category event: {} for category: {}", 
                                       event.getEventType(), event.getCategoryId(), ex);
                        }
                    });
                    
        } catch (JsonProcessingException e) {
            logger.error("Failed to serialize category event: {}", event.getEventType(), e);
        }
    }
    
    /**
     * Publishes product created event.
     */
    public void publishProductCreated(ProductEvent.ProductCreated event) {
        publishProductEvent(event);
    }
    
    /**
     * Publishes product updated event.
     */
    public void publishProductUpdated(ProductEvent.ProductUpdated event) {
        publishProductEvent(event);
    }
    
    /**
     * Publishes product status changed event.
     */
    public void publishProductStatusChanged(ProductEvent.ProductStatusChanged event) {
        publishProductEvent(event);
    }
    
    /**
     * Publishes product pricing updated event.
     */
    public void publishProductPricingUpdated(ProductEvent.ProductPricingUpdated event) {
        publishProductEvent(event);
    }
    
    /**
     * Publishes product deleted event.
     */
    public void publishProductDeleted(ProductEvent.ProductDeleted event) {
        publishProductEvent(event);
    }
    
    /**
     * Publishes stock reserved event.
     */
    public void publishStockReserved(InventoryEvent.StockReserved event) {
        publishInventoryEvent(event);
    }
    
    /**
     * Publishes stock released event.
     */
    public void publishStockReleased(InventoryEvent.StockReleased event) {
        publishInventoryEvent(event);
    }
    
    /**
     * Publishes stock sold event.
     */
    public void publishStockSold(InventoryEvent.StockSold event) {
        publishInventoryEvent(event);
    }
    
    /**
     * Publishes stock added event.
     */
    public void publishStockAdded(InventoryEvent.StockAdded event) {
        publishInventoryEvent(event);
    }
    
    /**
     * Publishes low stock alert event.
     */
    public void publishLowStockAlert(InventoryEvent.LowStockAlert event) {
        publishInventoryEvent(event);
    }
    
    /**
     * Publishes out of stock event.
     */
    public void publishOutOfStock(InventoryEvent.OutOfStock event) {
        publishInventoryEvent(event);
    }
    
    /**
     * Publishes stock damaged event.
     */
    public void publishStockDamaged(InventoryEvent.StockDamaged event) {
        publishInventoryEvent(event);
    }
    
    /**
     * Publishes category created event.
     */
    public void publishCategoryCreated(CategoryEvent.CategoryCreated event) {
        publishCategoryEvent(event);
    }
    
    /**
     * Publishes category updated event.
     */
    public void publishCategoryUpdated(CategoryEvent.CategoryUpdated event) {
        publishCategoryEvent(event);
    }
    
    /**
     * Publishes category moved event.
     */
    public void publishCategoryMoved(CategoryEvent.CategoryMoved event) {
        publishCategoryEvent(event);
    }
    
    /**
     * Publishes category deleted event.
     */
    public void publishCategoryDeleted(CategoryEvent.CategoryDeleted event) {
        publishCategoryEvent(event);
    }
}

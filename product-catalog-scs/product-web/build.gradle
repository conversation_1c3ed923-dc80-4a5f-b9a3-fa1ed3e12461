plugins {
    id 'org.springframework.boot'
    id 'java'
}

description = 'Product Catalog Web Layer - REST API and Spring Boot configuration'

dependencies {
    implementation project(':product-domain')
    implementation project(':product-infrastructure')
    implementation project(':product-application')
    implementation project(':product-events')

    // Web
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    
    // Replaced Terasoluna Web with Spring Boot equivalents
    // implementation 'org.terasoluna.gfw:terasoluna-gfw-web'
    // implementation 'org.terasoluna.gfw:terasoluna-gfw-security-web'
    
    // Service Discovery
    implementation 'org.springframework.cloud:spring-cloud-starter-consul-discovery'
    
    // Documentation
    implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.7.0'
    
    // Monitoring
    implementation 'io.micrometer:micrometer-registry-prometheus'
    implementation 'io.micrometer:micrometer-tracing-bridge-brave'
    
    // Database migration
    implementation 'org.flywaydb:flyway-core'
    implementation 'org.flywaydb:flyway-database-postgresql'
    
    // Testing
    testImplementation 'org.springframework.security:spring-security-test'
    testImplementation 'org.testcontainers:postgresql'
    testImplementation 'com.h2database:h2'
}

package com.nttdata.ndvn.product;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Main application class for Product Catalog SCS.
 * 
 * This Self-Contained System (SCS) provides comprehensive product catalog
 * management capabilities including products, categories, brands, variants,
 * attributes, images, and inventory management.
 * 
 * Key Features:
 * - Product management with variants and attributes
 * - Hierarchical category management
 * - Brand management
 * - Multi-location inventory tracking
 * - Advanced search with Elasticsearch
 * - Redis caching for performance
 * - Event-driven architecture with Kafka
 * - File storage for product images
 * - RESTful APIs with OpenAPI documentation
 * 
 * Architecture:
 * - Domain-Driven Design (DDD) with clean architecture
 * - Multi-module structure (domain, infrastructure, application, events, web)
 * - Terasoluna framework patterns
 * - Event sourcing for domain events
 * - CQRS for read/write separation
 */
@SpringBootApplication(scanBasePackages = "com.nttdata.ndvn.product")
@EnableJpaRepositories(basePackages = "com.nttdata.ndvn.product.infrastructure.repository")
@EnableTransactionManagement
@EnableCaching
@EnableKafka
@EnableAsync
public class ProductCatalogApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(ProductCatalogApplication.class, args);
    }
}

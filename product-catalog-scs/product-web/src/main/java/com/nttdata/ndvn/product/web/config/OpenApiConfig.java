package com.nttdata.ndvn.product.web.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * OpenAPI configuration for Product Catalog SCS.
 * 
 * This configuration sets up Swagger/OpenAPI documentation for the
 * product catalog APIs.
 */
@Configuration
public class OpenApiConfig {
    
    @Value("${app.api.version:1.0.0}")
    private String apiVersion;
    
    @Value("${app.api.title:Product Catalog API}")
    private String apiTitle;
    
    @Value("${app.api.description:Comprehensive product catalog management system}")
    private String apiDescription;
    
    @Value("${server.servlet.context-path:}")
    private String contextPath;
    
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(apiInfo())
                .servers(serverList())
                .addSecurityItem(securityRequirement())
                .components(securityComponents());
    }
    
    private Info apiInfo() {
        return new Info()
                .title(apiTitle)
                .description(apiDescription)
                .version(apiVersion)
                .contact(new Contact()
                        .name("NTT Data Vietnam")
                        .email("<EMAIL>")
                        .url("https://nttdata.com"))
                .license(new License()
                        .name("Proprietary")
                        .url("https://nttdata.com/license"));
    }
    
    private List<Server> serverList() {
        return List.of(
                new Server()
                        .url("http://localhost:8080" + contextPath)
                        .description("Local Development Server"),
                new Server()
                        .url("https://api-dev.yourdomain.com" + contextPath)
                        .description("Development Server"),
                new Server()
                        .url("https://api-staging.yourdomain.com" + contextPath)
                        .description("Staging Server"),
                new Server()
                        .url("https://api.yourdomain.com" + contextPath)
                        .description("Production Server")
        );
    }
    
    private SecurityRequirement securityRequirement() {
        return new SecurityRequirement().addList("Bearer Authentication");
    }
    
    private Components securityComponents() {
        return new Components()
                .addSecuritySchemes("Bearer Authentication", 
                        new SecurityScheme()
                                .type(SecurityScheme.Type.HTTP)
                                .scheme("bearer")
                                .bearerFormat("JWT")
                                .description("JWT token for authentication"));
    }
}

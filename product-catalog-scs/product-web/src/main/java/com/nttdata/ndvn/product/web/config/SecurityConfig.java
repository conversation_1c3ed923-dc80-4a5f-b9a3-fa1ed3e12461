package com.nttdata.ndvn.product.web.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;
import java.util.List;

/**
 * Security configuration for Product Catalog SCS.
 * 
 * This configuration sets up JWT-based authentication and authorization
 * for the product catalog APIs.
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
public class SecurityConfig {
    
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            .csrf(AbstractHttpConfigurer::disable)
            .cors(cors -> cors.configurationSource(corsConfigurationSource()))
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .authorizeHttpRequests(authz -> authz
                // Public endpoints
                .requestMatchers(HttpMethod.GET, "/api/v1/products/**").permitAll()
                .requestMatchers(HttpMethod.GET, "/api/v1/categories/**").permitAll()
                .requestMatchers(HttpMethod.GET, "/api/v1/brands/**").permitAll()
                .requestMatchers(HttpMethod.GET, "/api/v1/inventory/product/*/in-stock").permitAll()
                .requestMatchers(HttpMethod.GET, "/api/v1/inventory/product/*/available-stock").permitAll()
                
                // Health and monitoring endpoints
                .requestMatchers("/actuator/health", "/actuator/info").permitAll()
                
                // API documentation endpoints
                .requestMatchers("/v3/api-docs/**", "/swagger-ui/**", "/swagger-ui.html").permitAll()
                
                // File serving endpoints
                .requestMatchers(HttpMethod.GET, "/files/**").permitAll()
                
                // Admin endpoints
                .requestMatchers(HttpMethod.DELETE, "/api/v1/**").hasRole("ADMIN")
                .requestMatchers("/actuator/**").hasRole("ADMIN")
                
                // Product management endpoints
                .requestMatchers(HttpMethod.POST, "/api/v1/products/**").hasAnyRole("ADMIN", "PRODUCT_MANAGER")
                .requestMatchers(HttpMethod.PUT, "/api/v1/products/**").hasAnyRole("ADMIN", "PRODUCT_MANAGER")
                .requestMatchers(HttpMethod.PATCH, "/api/v1/products/**").hasAnyRole("ADMIN", "PRODUCT_MANAGER")
                
                // Category management endpoints
                .requestMatchers(HttpMethod.POST, "/api/v1/categories/**").hasAnyRole("ADMIN", "PRODUCT_MANAGER")
                .requestMatchers(HttpMethod.PUT, "/api/v1/categories/**").hasAnyRole("ADMIN", "PRODUCT_MANAGER")
                .requestMatchers(HttpMethod.PATCH, "/api/v1/categories/**").hasAnyRole("ADMIN", "PRODUCT_MANAGER")
                
                // Brand management endpoints
                .requestMatchers(HttpMethod.POST, "/api/v1/brands/**").hasAnyRole("ADMIN", "PRODUCT_MANAGER")
                .requestMatchers(HttpMethod.PUT, "/api/v1/brands/**").hasAnyRole("ADMIN", "PRODUCT_MANAGER")
                .requestMatchers(HttpMethod.PATCH, "/api/v1/brands/**").hasAnyRole("ADMIN", "PRODUCT_MANAGER")
                
                // Inventory management endpoints
                .requestMatchers(HttpMethod.POST, "/api/v1/inventory/**").hasAnyRole("ADMIN", "INVENTORY_MANAGER")
                .requestMatchers(HttpMethod.PUT, "/api/v1/inventory/**").hasAnyRole("ADMIN", "INVENTORY_MANAGER")
                .requestMatchers(HttpMethod.PATCH, "/api/v1/inventory/**").hasAnyRole("ADMIN", "INVENTORY_MANAGER")
                
                // Order-related inventory operations
                .requestMatchers(HttpMethod.POST, "/api/v1/inventory/*/reserve").hasAnyRole("ADMIN", "INVENTORY_MANAGER", "ORDER_MANAGER")
                .requestMatchers(HttpMethod.POST, "/api/v1/inventory/*/release").hasAnyRole("ADMIN", "INVENTORY_MANAGER", "ORDER_MANAGER")
                .requestMatchers(HttpMethod.POST, "/api/v1/inventory/*/confirm-sale").hasAnyRole("ADMIN", "INVENTORY_MANAGER", "ORDER_MANAGER")
                
                // All other requests require authentication
                .anyRequest().authenticated()
            );
            
        return http.build();
    }
    
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        
        // Allow specific origins (configure based on your frontend domains)
        configuration.setAllowedOriginPatterns(List.of(
            "http://localhost:3000",  // React dev server
            "http://localhost:4200",  // Angular dev server
            "http://localhost:8080",  // Local development
            "https://*.yourdomain.com" // Production domains
        ));
        
        configuration.setAllowedMethods(Arrays.asList(
            "GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"
        ));
        
        configuration.setAllowedHeaders(Arrays.asList(
            "Authorization", "Content-Type", "X-Requested-With", "Accept", 
            "Origin", "Access-Control-Request-Method", "Access-Control-Request-Headers"
        ));
        
        configuration.setExposedHeaders(Arrays.asList(
            "Access-Control-Allow-Origin", "Access-Control-Allow-Credentials",
            "X-Total-Count", "X-Page-Number", "X-Page-Size"
        ));
        
        configuration.setAllowCredentials(true);
        configuration.setMaxAge(3600L);
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        
        return source;
    }
}

package com.nttdata.ndvn.product.web.controller;

import com.nttdata.ndvn.product.application.dto.CategoryDto;
import com.nttdata.ndvn.product.application.service.CategoryApplicationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

/**
 * REST controller for Category management.
 */
@RestController
@RequestMapping("/api/v1/categories")
@Tag(name = "Categories", description = "Category management operations")
public class CategoryController {
    
    private final CategoryApplicationService categoryApplicationService;
    
    public CategoryController(CategoryApplicationService categoryApplicationService) {
        this.categoryApplicationService = categoryApplicationService;
    }
    
    @PostMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('PRODUCT_MANAGER')")
    @Operation(summary = "Create a new category")
    public ResponseEntity<CategoryDto> createCategory(@Valid @RequestBody CategoryDto categoryDto) {
        CategoryDto createdCategory = categoryApplicationService.createCategory(categoryDto);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdCategory);
    }
    
    @PutMapping("/{categoryId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('PRODUCT_MANAGER')")
    @Operation(summary = "Update a category")
    public ResponseEntity<CategoryDto> updateCategory(
            @PathVariable UUID categoryId,
            @Valid @RequestBody CategoryDto categoryDto) {
        CategoryDto updatedCategory = categoryApplicationService.updateCategory(categoryId, categoryDto);
        return ResponseEntity.ok(updatedCategory);
    }
    
    @GetMapping("/{categoryId}")
    @Operation(summary = "Get category by ID")
    public ResponseEntity<CategoryDto> getCategoryById(@PathVariable UUID categoryId) {
        return categoryApplicationService.findCategoryById(categoryId)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
    
    @GetMapping("/slug/{slug}")
    @Operation(summary = "Get category by slug")
    public ResponseEntity<CategoryDto> getCategoryBySlug(@PathVariable String slug) {
        return categoryApplicationService.findCategoryBySlug(slug)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
    
    @GetMapping
    @Operation(summary = "Get all categories")
    public ResponseEntity<Page<CategoryDto>> getAllCategories(@PageableDefault(size = 20) Pageable pageable) {
        Page<CategoryDto> categories = categoryApplicationService.findAllCategories(pageable);
        return ResponseEntity.ok(categories);
    }
    
    @GetMapping("/active")
    @Operation(summary = "Get active categories")
    public ResponseEntity<Page<CategoryDto>> getActiveCategories(@PageableDefault(size = 20) Pageable pageable) {
        Page<CategoryDto> categories = categoryApplicationService.findActiveCategories(pageable);
        return ResponseEntity.ok(categories);
    }
    
    @GetMapping("/root")
    @Operation(summary = "Get root categories")
    public ResponseEntity<List<CategoryDto>> getRootCategories() {
        List<CategoryDto> categories = categoryApplicationService.findRootCategories();
        return ResponseEntity.ok(categories);
    }
    
    @GetMapping("/{parentId}/children")
    @Operation(summary = "Get categories by parent")
    public ResponseEntity<List<CategoryDto>> getCategoriesByParent(@PathVariable UUID parentId) {
        List<CategoryDto> categories = categoryApplicationService.findCategoriesByParent(parentId);
        return ResponseEntity.ok(categories);
    }
    
    @GetMapping("/tree")
    @Operation(summary = "Get category tree")
    public ResponseEntity<List<CategoryDto>> getCategoryTree() {
        List<CategoryDto> tree = categoryApplicationService.buildCategoryTree();
        return ResponseEntity.ok(tree);
    }
    
    @GetMapping("/{categoryId}/breadcrumb")
    @Operation(summary = "Get category breadcrumb")
    public ResponseEntity<List<CategoryDto>> getCategoryBreadcrumb(@PathVariable UUID categoryId) {
        List<CategoryDto> breadcrumb = categoryApplicationService.getCategoryBreadcrumb(categoryId);
        return ResponseEntity.ok(breadcrumb);
    }
    
    @GetMapping("/search")
    @Operation(summary = "Search categories")
    public ResponseEntity<Page<CategoryDto>> searchCategories(
            @RequestParam String q,
            @PageableDefault(size = 20) Pageable pageable) {
        Page<CategoryDto> categories = categoryApplicationService.searchCategories(q, pageable);
        return ResponseEntity.ok(categories);
    }
    
    @PatchMapping("/{categoryId}/move")
    @PreAuthorize("hasRole('ADMIN') or hasRole('PRODUCT_MANAGER')")
    @Operation(summary = "Move category to new parent")
    public ResponseEntity<CategoryDto> moveCategory(
            @PathVariable UUID categoryId,
            @RequestParam(required = false) UUID newParentId) {
        CategoryDto movedCategory = categoryApplicationService.moveCategory(categoryId, newParentId);
        return ResponseEntity.ok(movedCategory);
    }
    
    @PostMapping("/{parentId}/reorder")
    @PreAuthorize("hasRole('ADMIN') or hasRole('PRODUCT_MANAGER')")
    @Operation(summary = "Reorder categories")
    public ResponseEntity<Void> reorderCategories(
            @PathVariable UUID parentId,
            @RequestBody List<UUID> categoryIds) {
        categoryApplicationService.reorderCategories(parentId, categoryIds);
        return ResponseEntity.ok().build();
    }
    
    @GetMapping("/popular")
    @Operation(summary = "Get popular categories")
    public ResponseEntity<List<CategoryDto>> getPopularCategories(
            @RequestParam(defaultValue = "10") int limit) {
        List<CategoryDto> categories = categoryApplicationService.getPopularCategories(limit);
        return ResponseEntity.ok(categories);
    }
    
    @GetMapping("/navigation")
    @Operation(summary = "Get categories for navigation")
    public ResponseEntity<List<CategoryDto>> getCategoriesForNavigation(
            @RequestParam(defaultValue = "3") Integer maxLevel) {
        List<CategoryDto> categories = categoryApplicationService.getCategoriesForNavigation(maxLevel);
        return ResponseEntity.ok(categories);
    }
    
    @GetMapping("/statistics")
    @PreAuthorize("hasRole('ADMIN') or hasRole('PRODUCT_MANAGER')")
    @Operation(summary = "Get category statistics")
    public ResponseEntity<CategoryApplicationService.CategoryStatistics> getCategoryStatistics() {
        CategoryApplicationService.CategoryStatistics statistics = categoryApplicationService.getCategoryStatistics();
        return ResponseEntity.ok(statistics);
    }
    
    @DeleteMapping("/{categoryId}")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Delete category")
    public ResponseEntity<Void> deleteCategory(@PathVariable UUID categoryId) {
        categoryApplicationService.deleteCategory(categoryId);
        return ResponseEntity.noContent().build();
    }
}

package com.nttdata.ndvn.product.web.controller;

import com.nttdata.ndvn.product.application.dto.InventoryDto;
import com.nttdata.ndvn.product.application.service.InventoryApplicationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

/**
 * REST controller for Inventory management.
 */
@RestController
@RequestMapping("/api/v1/inventory")
@Tag(name = "Inventory", description = "Inventory management operations")
public class InventoryController {
    
    private final InventoryApplicationService inventoryApplicationService;
    
    public InventoryController(InventoryApplicationService inventoryApplicationService) {
        this.inventoryApplicationService = inventoryApplicationService;
    }
    
    @PostMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('INVENTORY_MANAGER')")
    @Operation(summary = "Create inventory record")
    public ResponseEntity<InventoryDto> createInventory(@Valid @RequestBody InventoryDto inventoryDto) {
        InventoryDto createdInventory = inventoryApplicationService.createInventory(inventoryDto);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdInventory);
    }
    
    @PutMapping("/{inventoryId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('INVENTORY_MANAGER')")
    @Operation(summary = "Update inventory record")
    public ResponseEntity<InventoryDto> updateInventory(
            @PathVariable UUID inventoryId,
            @Valid @RequestBody InventoryDto inventoryDto) {
        InventoryDto updatedInventory = inventoryApplicationService.updateInventory(inventoryId, inventoryDto);
        return ResponseEntity.ok(updatedInventory);
    }
    
    @GetMapping("/{inventoryId}")
    @Operation(summary = "Get inventory by ID")
    public ResponseEntity<InventoryDto> getInventoryById(@PathVariable UUID inventoryId) {
        return inventoryApplicationService.findInventoryById(inventoryId)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
    
    @GetMapping("/product/{productId}")
    @Operation(summary = "Get inventory by product")
    public ResponseEntity<List<InventoryDto>> getInventoryByProduct(@PathVariable UUID productId) {
        List<InventoryDto> inventory = inventoryApplicationService.findInventoryByProduct(productId);
        return ResponseEntity.ok(inventory);
    }
    
    @GetMapping("/product/{productId}/location/{locationCode}")
    @Operation(summary = "Get inventory by product and location")
    public ResponseEntity<InventoryDto> getInventoryByProductAndLocation(
            @PathVariable UUID productId,
            @PathVariable String locationCode) {
        return inventoryApplicationService.findInventoryByProductAndLocation(productId, locationCode)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
    
    @GetMapping("/location/{locationCode}")
    @Operation(summary = "Get inventory by location")
    public ResponseEntity<Page<InventoryDto>> getInventoryByLocation(
            @PathVariable String locationCode,
            @PageableDefault(size = 20) Pageable pageable) {
        Page<InventoryDto> inventory = inventoryApplicationService.findInventoryByLocation(locationCode, pageable);
        return ResponseEntity.ok(inventory);
    }
    
    @GetMapping("/low-stock")
    @Operation(summary = "Get low stock inventory")
    public ResponseEntity<List<InventoryDto>> getLowStockInventory() {
        List<InventoryDto> inventory = inventoryApplicationService.findLowStockInventory();
        return ResponseEntity.ok(inventory);
    }
    
    @GetMapping("/out-of-stock")
    @Operation(summary = "Get out of stock inventory")
    public ResponseEntity<List<InventoryDto>> getOutOfStockInventory() {
        List<InventoryDto> inventory = inventoryApplicationService.findOutOfStockInventory();
        return ResponseEntity.ok(inventory);
    }
    
    @GetMapping("/needs-reorder")
    @Operation(summary = "Get inventory needing reorder")
    public ResponseEntity<List<InventoryDto>> getInventoryNeedingReorder() {
        List<InventoryDto> inventory = inventoryApplicationService.findInventoryNeedingReorder();
        return ResponseEntity.ok(inventory);
    }
    
    @PostMapping("/{inventoryId}/reserve")
    @PreAuthorize("hasRole('ADMIN') or hasRole('INVENTORY_MANAGER') or hasRole('ORDER_MANAGER')")
    @Operation(summary = "Reserve stock")
    public ResponseEntity<InventoryDto> reserveStock(
            @PathVariable UUID inventoryId,
            @RequestParam Integer quantity) {
        InventoryDto updatedInventory = inventoryApplicationService.reserveStock(inventoryId, quantity);
        return ResponseEntity.ok(updatedInventory);
    }
    
    @PostMapping("/{inventoryId}/release")
    @PreAuthorize("hasRole('ADMIN') or hasRole('INVENTORY_MANAGER') or hasRole('ORDER_MANAGER')")
    @Operation(summary = "Release reserved stock")
    public ResponseEntity<InventoryDto> releaseReservedStock(
            @PathVariable UUID inventoryId,
            @RequestParam Integer quantity) {
        InventoryDto updatedInventory = inventoryApplicationService.releaseReservedStock(inventoryId, quantity);
        return ResponseEntity.ok(updatedInventory);
    }
    
    @PostMapping("/{inventoryId}/confirm-sale")
    @PreAuthorize("hasRole('ADMIN') or hasRole('INVENTORY_MANAGER') or hasRole('ORDER_MANAGER')")
    @Operation(summary = "Confirm sale")
    public ResponseEntity<InventoryDto> confirmSale(
            @PathVariable UUID inventoryId,
            @RequestParam Integer quantity) {
        InventoryDto updatedInventory = inventoryApplicationService.confirmSale(inventoryId, quantity);
        return ResponseEntity.ok(updatedInventory);
    }
    
    @PostMapping("/{inventoryId}/add-stock")
    @PreAuthorize("hasRole('ADMIN') or hasRole('INVENTORY_MANAGER')")
    @Operation(summary = "Add stock")
    public ResponseEntity<InventoryDto> addStock(
            @PathVariable UUID inventoryId,
            @RequestParam Integer quantity) {
        InventoryDto updatedInventory = inventoryApplicationService.addStock(inventoryId, quantity);
        return ResponseEntity.ok(updatedInventory);
    }
    
    @PostMapping("/{inventoryId}/mark-damaged")
    @PreAuthorize("hasRole('ADMIN') or hasRole('INVENTORY_MANAGER')")
    @Operation(summary = "Mark stock as damaged")
    public ResponseEntity<InventoryDto> markAsDamaged(
            @PathVariable UUID inventoryId,
            @RequestParam Integer quantity) {
        InventoryDto updatedInventory = inventoryApplicationService.markAsDamaged(inventoryId, quantity);
        return ResponseEntity.ok(updatedInventory);
    }
    
    @PostMapping("/{inventoryId}/remove-damaged")
    @PreAuthorize("hasRole('ADMIN') or hasRole('INVENTORY_MANAGER')")
    @Operation(summary = "Remove damaged stock")
    public ResponseEntity<InventoryDto> removeDamagedStock(
            @PathVariable UUID inventoryId,
            @RequestParam Integer quantity) {
        InventoryDto updatedInventory = inventoryApplicationService.removeDamagedStock(inventoryId, quantity);
        return ResponseEntity.ok(updatedInventory);
    }
    
    @PostMapping("/{inventoryId}/stock-check")
    @PreAuthorize("hasRole('ADMIN') or hasRole('INVENTORY_MANAGER')")
    @Operation(summary = "Update stock check")
    public ResponseEntity<InventoryDto> updateStockCheck(@PathVariable UUID inventoryId) {
        InventoryDto updatedInventory = inventoryApplicationService.updateStockCheck(inventoryId);
        return ResponseEntity.ok(updatedInventory);
    }
    
    @GetMapping("/product/{productId}/total-stock")
    @Operation(summary = "Get total stock for product")
    public ResponseEntity<Integer> getTotalStock(@PathVariable UUID productId) {
        Integer totalStock = inventoryApplicationService.getTotalStock(productId);
        return ResponseEntity.ok(totalStock);
    }
    
    @GetMapping("/product/{productId}/available-stock")
    @Operation(summary = "Get available stock for product")
    public ResponseEntity<Integer> getAvailableStock(@PathVariable UUID productId) {
        Integer availableStock = inventoryApplicationService.getTotalAvailableStock(productId);
        return ResponseEntity.ok(availableStock);
    }
    
    @GetMapping("/product/{productId}/in-stock")
    @Operation(summary = "Check if product is in stock")
    public ResponseEntity<Boolean> isProductInStock(@PathVariable UUID productId) {
        boolean inStock = inventoryApplicationService.isProductInStock(productId);
        return ResponseEntity.ok(inStock);
    }
    
    @GetMapping("/summary/by-location")
    @Operation(summary = "Get inventory summary by location")
    public ResponseEntity<List<InventoryApplicationService.LocationInventorySummary>> getInventorySummaryByLocation() {
        List<InventoryApplicationService.LocationInventorySummary> summary = 
                inventoryApplicationService.getInventorySummaryByLocation();
        return ResponseEntity.ok(summary);
    }
    
    @GetMapping("/locations")
    @Operation(summary = "Get all location codes")
    public ResponseEntity<List<String>> getAllLocationCodes() {
        List<String> locations = inventoryApplicationService.getAllLocationCodes();
        return ResponseEntity.ok(locations);
    }
    
    @GetMapping("/statistics")
    @PreAuthorize("hasRole('ADMIN') or hasRole('INVENTORY_MANAGER')")
    @Operation(summary = "Get inventory statistics")
    public ResponseEntity<InventoryApplicationService.InventoryStatistics> getInventoryStatistics() {
        InventoryApplicationService.InventoryStatistics statistics = inventoryApplicationService.getInventoryStatistics();
        return ResponseEntity.ok(statistics);
    }
    
    @DeleteMapping("/{inventoryId}")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Delete inventory record")
    public ResponseEntity<Void> deleteInventory(@PathVariable UUID inventoryId) {
        inventoryApplicationService.deleteInventory(inventoryId);
        return ResponseEntity.noContent().build();
    }
}

package com.nttdata.ndvn.product.web.controller;

import com.nttdata.ndvn.product.application.dto.ProductDto;
import com.nttdata.ndvn.product.application.service.ProductApplicationService;
import com.nttdata.ndvn.product.domain.model.ProductStatus;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

/**
 * REST controller for Product management.
 * 
 * This controller provides endpoints for product CRUD operations,
 * search, and related functionality.
 */
@RestController
@RequestMapping("/api/v1/products")
@Tag(name = "Products", description = "Product management operations")
public class ProductController {
    
    private final ProductApplicationService productApplicationService;
    
    public ProductController(ProductApplicationService productApplicationService) {
        this.productApplicationService = productApplicationService;
    }
    
    /**
     * Creates a new product.
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('PRODUCT_MANAGER')")
    @Operation(summary = "Create a new product", description = "Creates a new product with the provided details")
    public ResponseEntity<ProductDto> createProduct(@Valid @RequestBody ProductDto productDto) {
        ProductDto createdProduct = productApplicationService.createProduct(productDto);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdProduct);
    }
    
    /**
     * Updates an existing product.
     */
    @PutMapping("/{productId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('PRODUCT_MANAGER')")
    @Operation(summary = "Update a product", description = "Updates an existing product with the provided details")
    public ResponseEntity<ProductDto> updateProduct(
            @Parameter(description = "Product ID") @PathVariable UUID productId,
            @Valid @RequestBody ProductDto productDto) {
        ProductDto updatedProduct = productApplicationService.updateProduct(productId, productDto);
        return ResponseEntity.ok(updatedProduct);
    }
    
    /**
     * Gets a product by ID.
     */
    @GetMapping("/{productId}")
    @Operation(summary = "Get product by ID", description = "Retrieves a product by its unique identifier")
    public ResponseEntity<ProductDto> getProductById(
            @Parameter(description = "Product ID") @PathVariable UUID productId) {
        return productApplicationService.findProductById(productId)
                .map(product -> ResponseEntity.ok(product))
                .orElse(ResponseEntity.notFound().build());
    }
    
    /**
     * Gets a product by SKU.
     */
    @GetMapping("/sku/{sku}")
    @Operation(summary = "Get product by SKU", description = "Retrieves a product by its SKU")
    public ResponseEntity<ProductDto> getProductBySku(
            @Parameter(description = "Product SKU") @PathVariable String sku) {
        return productApplicationService.findProductBySku(sku)
                .map(product -> ResponseEntity.ok(product))
                .orElse(ResponseEntity.notFound().build());
    }
    
    /**
     * Gets all products with pagination.
     */
    @GetMapping
    @Operation(summary = "Get all products", description = "Retrieves all products with pagination")
    public ResponseEntity<Page<ProductDto>> getAllProducts(
            @PageableDefault(size = 20) Pageable pageable) {
        Page<ProductDto> products = productApplicationService.findAllProducts(pageable);
        return ResponseEntity.ok(products);
    }
    
    /**
     * Gets products by status.
     */
    @GetMapping("/status/{status}")
    @Operation(summary = "Get products by status", description = "Retrieves products filtered by status")
    public ResponseEntity<Page<ProductDto>> getProductsByStatus(
            @Parameter(description = "Product status") @PathVariable ProductStatus status,
            @PageableDefault(size = 20) Pageable pageable) {
        Page<ProductDto> products = productApplicationService.findProductsByStatus(status, pageable);
        return ResponseEntity.ok(products);
    }
    
    /**
     * Gets products by category.
     */
    @GetMapping("/category/{categoryId}")
    @Operation(summary = "Get products by category", description = "Retrieves products in a specific category")
    public ResponseEntity<Page<ProductDto>> getProductsByCategory(
            @Parameter(description = "Category ID") @PathVariable UUID categoryId,
            @PageableDefault(size = 20) Pageable pageable) {
        Page<ProductDto> products = productApplicationService.findProductsByCategory(categoryId, pageable);
        return ResponseEntity.ok(products);
    }
    
    /**
     * Gets products by brand.
     */
    @GetMapping("/brand/{brandId}")
    @Operation(summary = "Get products by brand", description = "Retrieves products from a specific brand")
    public ResponseEntity<Page<ProductDto>> getProductsByBrand(
            @Parameter(description = "Brand ID") @PathVariable UUID brandId,
            @PageableDefault(size = 20) Pageable pageable) {
        Page<ProductDto> products = productApplicationService.findProductsByBrand(brandId, pageable);
        return ResponseEntity.ok(products);
    }
    
    /**
     * Gets featured products.
     */
    @GetMapping("/featured")
    @Operation(summary = "Get featured products", description = "Retrieves featured products")
    public ResponseEntity<Page<ProductDto>> getFeaturedProducts(
            @PageableDefault(size = 20) Pageable pageable) {
        Page<ProductDto> products = productApplicationService.findFeaturedProducts(pageable);
        return ResponseEntity.ok(products);
    }
    
    /**
     * Gets products on sale.
     */
    @GetMapping("/on-sale")
    @Operation(summary = "Get products on sale", description = "Retrieves products that are currently on sale")
    public ResponseEntity<Page<ProductDto>> getProductsOnSale(
            @PageableDefault(size = 20) Pageable pageable) {
        Page<ProductDto> products = productApplicationService.findProductsOnSale(pageable);
        return ResponseEntity.ok(products);
    }
    
    /**
     * Searches products.
     */
    @GetMapping("/search")
    @Operation(summary = "Search products", description = "Searches products by text query")
    public ResponseEntity<Page<ProductDto>> searchProducts(
            @Parameter(description = "Search query") @RequestParam String q,
            @PageableDefault(size = 20) Pageable pageable) {
        Page<ProductDto> products = productApplicationService.searchProducts(q, pageable);
        return ResponseEntity.ok(products);
    }
    
    /**
     * Advanced product search.
     */
    @GetMapping("/search/advanced")
    @Operation(summary = "Advanced product search", description = "Advanced search with multiple criteria")
    public ResponseEntity<Page<ProductDto>> advancedSearch(
            @RequestParam(required = false) String q,
            @RequestParam(required = false) List<UUID> categoryIds,
            @RequestParam(required = false) List<UUID> brandIds,
            @RequestParam(required = false) List<ProductStatus> statuses,
            @RequestParam(required = false) BigDecimal minPrice,
            @RequestParam(required = false) BigDecimal maxPrice,
            @RequestParam(required = false) Boolean featured,
            @RequestParam(required = false) Boolean digital,
            @RequestParam(required = false) Boolean onSale,
            @PageableDefault(size = 20) Pageable pageable) {
        
        Page<ProductDto> products = productApplicationService.searchProductsByCriteria(
                q, categoryIds, brandIds, statuses, minPrice, maxPrice, 
                featured, digital, onSale, pageable);
        return ResponseEntity.ok(products);
    }
    
    /**
     * Changes product status.
     */
    @PatchMapping("/{productId}/status")
    @PreAuthorize("hasRole('ADMIN') or hasRole('PRODUCT_MANAGER')")
    @Operation(summary = "Change product status", description = "Changes the status of a product")
    public ResponseEntity<ProductDto> changeProductStatus(
            @Parameter(description = "Product ID") @PathVariable UUID productId,
            @Parameter(description = "New status") @RequestParam ProductStatus status) {
        ProductDto updatedProduct = productApplicationService.changeProductStatus(productId, status);
        return ResponseEntity.ok(updatedProduct);
    }
    
    /**
     * Activates a product.
     */
    @PostMapping("/{productId}/activate")
    @PreAuthorize("hasRole('ADMIN') or hasRole('PRODUCT_MANAGER')")
    @Operation(summary = "Activate product", description = "Activates a product")
    public ResponseEntity<ProductDto> activateProduct(
            @Parameter(description = "Product ID") @PathVariable UUID productId) {
        ProductDto updatedProduct = productApplicationService.activateProduct(productId);
        return ResponseEntity.ok(updatedProduct);
    }
    
    /**
     * Deactivates a product.
     */
    @PostMapping("/{productId}/deactivate")
    @PreAuthorize("hasRole('ADMIN') or hasRole('PRODUCT_MANAGER')")
    @Operation(summary = "Deactivate product", description = "Deactivates a product")
    public ResponseEntity<ProductDto> deactivateProduct(
            @Parameter(description = "Product ID") @PathVariable UUID productId) {
        ProductDto updatedProduct = productApplicationService.deactivateProduct(productId);
        return ResponseEntity.ok(updatedProduct);
    }
    
    /**
     * Updates product pricing.
     */
    @PatchMapping("/{productId}/pricing")
    @PreAuthorize("hasRole('ADMIN') or hasRole('PRODUCT_MANAGER')")
    @Operation(summary = "Update product pricing", description = "Updates the pricing of a product")
    public ResponseEntity<ProductDto> updateProductPricing(
            @Parameter(description = "Product ID") @PathVariable UUID productId,
            @Parameter(description = "Base price") @RequestParam BigDecimal basePrice,
            @Parameter(description = "Sale price") @RequestParam(required = false) BigDecimal salePrice) {
        ProductDto updatedProduct = productApplicationService.updateProductPricing(productId, basePrice, salePrice);
        return ResponseEntity.ok(updatedProduct);
    }
    
    /**
     * Gets product recommendations.
     */
    @GetMapping("/{productId}/recommendations")
    @Operation(summary = "Get product recommendations", description = "Gets recommended products for a given product")
    public ResponseEntity<List<ProductDto>> getProductRecommendations(
            @Parameter(description = "Product ID") @PathVariable UUID productId,
            @Parameter(description = "Number of recommendations") @RequestParam(defaultValue = "10") int limit) {
        List<ProductDto> recommendations = productApplicationService.getProductRecommendations(productId, limit);
        return ResponseEntity.ok(recommendations);
    }
    
    /**
     * Gets popular products.
     */
    @GetMapping("/popular")
    @Operation(summary = "Get popular products", description = "Gets the most popular products")
    public ResponseEntity<List<ProductDto>> getPopularProducts(
            @Parameter(description = "Number of products") @RequestParam(defaultValue = "10") int limit) {
        List<ProductDto> products = productApplicationService.getPopularProducts(limit);
        return ResponseEntity.ok(products);
    }
    
    /**
     * Gets newest products.
     */
    @GetMapping("/newest")
    @Operation(summary = "Get newest products", description = "Gets the newest products")
    public ResponseEntity<List<ProductDto>> getNewestProducts(
            @Parameter(description = "Number of products") @RequestParam(defaultValue = "10") int limit) {
        List<ProductDto> products = productApplicationService.getNewestProducts(limit);
        return ResponseEntity.ok(products);
    }
    
    /**
     * Gets product statistics.
     */
    @GetMapping("/statistics")
    @PreAuthorize("hasRole('ADMIN') or hasRole('PRODUCT_MANAGER')")
    @Operation(summary = "Get product statistics", description = "Gets product statistics")
    public ResponseEntity<ProductApplicationService.ProductStatistics> getProductStatistics() {
        ProductApplicationService.ProductStatistics statistics = productApplicationService.getProductStatistics();
        return ResponseEntity.ok(statistics);
    }
    
    /**
     * Deletes a product.
     */
    @DeleteMapping("/{productId}")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Delete product", description = "Deletes a product")
    public ResponseEntity<Void> deleteProduct(
            @Parameter(description = "Product ID") @PathVariable UUID productId) {
        productApplicationService.deleteProduct(productId);
        return ResponseEntity.noContent().build();
    }
}

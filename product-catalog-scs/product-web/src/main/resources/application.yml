# Product Catalog SCS Configuration
spring:
  application:
    name: product-catalog-scs
  
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:local}
  
  # Database Configuration
  datasource:
    url: ${DATABASE_URL:************************************************}
    username: ${DATABASE_USERNAME:product_catalog_user}
    password: ${DATABASE_PASSWORD:product_catalog_pass}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: ${DATABASE_POOL_SIZE:20}
      minimum-idle: ${DATABASE_POOL_MIN_IDLE:5}
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  # JPA Configuration
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: ${JPA_SHOW_SQL:false}
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
  
  # Flyway Configuration
  flyway:
    enabled: true
    baseline-on-migrate: true
    locations: classpath:db/migration
    schemas: public
  
  # Redis Configuration
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: ${REDIS_DATABASE:0}
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1ms
  
  # Elasticsearch Configuration
  elasticsearch:
    uris: ${ELASTICSEARCH_URIS:http://localhost:9200}
    username: ${ELASTICSEARCH_USERNAME:}
    password: ${ELASTICSEARCH_PASSWORD:}
    connection-timeout: 10s
    socket-timeout: 30s
  
  # Kafka Configuration
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:localhost:9092}
    producer:
      client-id: product-catalog-producer
      acks: all
      retries: 3
      batch-size: 16384
      linger-ms: 5
      buffer-memory: 33554432
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
  
  # Jackson Configuration
  jackson:
    default-property-inclusion: non_null
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
  
  # Servlet Configuration
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

# Server Configuration
server:
  port: ${SERVER_PORT:8083}
  servlet:
    context-path: ${CONTEXT_PATH:}
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024

# Management Configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when_authorized
  metrics:
    export:
      prometheus:
        enabled: true

# Logging Configuration
logging:
  level:
    com.nttdata.ndvn.product: ${LOG_LEVEL:INFO}
    org.springframework.security: ${SECURITY_LOG_LEVEL:WARN}
    org.springframework.web: ${WEB_LOG_LEVEL:WARN}
    org.hibernate.SQL: ${SQL_LOG_LEVEL:WARN}
    org.hibernate.type.descriptor.sql.BasicBinder: ${SQL_PARAM_LOG_LEVEL:WARN}
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Application Configuration
app:
  api:
    version: 1.0.0
    title: Product Catalog API
    description: Comprehensive product catalog management system with products, categories, brands, and inventory
  
  file:
    upload-dir: ${FILE_UPLOAD_DIR:./uploads}
    base-url: ${FILE_BASE_URL:http://localhost:8083/files}
    max-size: ${FILE_MAX_SIZE:10MB}
  
  cache:
    default-ttl: ${CACHE_DEFAULT_TTL:PT30M}
    product-ttl: ${CACHE_PRODUCT_TTL:PT1H}
    category-ttl: ${CACHE_CATEGORY_TTL:PT2H}
    inventory-ttl: ${CACHE_INVENTORY_TTL:PT5M}
  
  search:
    elasticsearch:
      index-prefix: ${SEARCH_INDEX_PREFIX:product-catalog}
      refresh-interval: ${SEARCH_REFRESH_INTERVAL:30s}
      number-of-shards: ${SEARCH_SHARDS:1}
      number-of-replicas: ${SEARCH_REPLICAS:0}

---
# Local Development Profile
spring:
  config:
    activate:
      on-profile: local
  
  jpa:
    show-sql: true
    properties:
      hibernate:
        format_sql: true

logging:
  level:
    com.nttdata.ndvn.product: DEBUG
    org.springframework.web: DEBUG

---
# Development Profile
spring:
  config:
    activate:
      on-profile: dev

logging:
  level:
    com.nttdata.ndvn.product: DEBUG

---
# Staging Profile
spring:
  config:
    activate:
      on-profile: staging

logging:
  level:
    com.nttdata.ndvn.product: INFO

---
# Production Profile
spring:
  config:
    activate:
      on-profile: prod

logging:
  level:
    com.nttdata.ndvn.product: WARN
    org.springframework.security: ERROR
    org.springframework.web: ERROR

# Shared Events Framework

## Overview

The Shared Events Framework provides a standardized approach to event publishing and consumption across all NDVN SCS services. It ensures consistency, traceability, and reliability in event-driven communication.

## 🚀 Features

### Event Publishing
- ✅ Standardized event structure with BaseEvent
- ✅ Automatic correlation ID management
- ✅ Event validation and schema enforcement
- ✅ Metrics collection and monitoring
- ✅ Error handling and retry mechanisms
- ✅ Kafka integration with optimized configuration

### Correlation and Tracing
- ✅ Automatic correlation ID propagation
- ✅ Causation tracking for event chains
- ✅ MDC integration for logging
- ✅ Distributed tracing support
- ✅ Thread-safe context management

### Monitoring and Observability
- ✅ Prometheus metrics integration
- ✅ Event publishing success/failure rates
- ✅ Processing duration tracking
- ✅ Event lag monitoring
- ✅ Serialization error tracking

## 📋 Usage

### 1. Add Dependency

Add the shared-events dependency to your service:

```gradle
dependencies {
    implementation project(':shared-events')
}
```

### 2. Create Event Classes

Extend `BaseEvent` for your domain events:

```java
public class UserCreatedEvent extends BaseEvent {
    private UUID userId;
    private String username;
    private String email;
    
    public UserCreatedEvent(UUID userId, String username, String email) {
        super("UserCreated", "1.0", "user-management-service");
        this.userId = userId;
        this.username = username;
        this.email = email;
        
        // Set aggregate context
        setAggregateContext(userId.toString(), "User", 1L);
    }
    
    // Getters and setters...
}
```

### 3. Create Event Publisher

Extend `BaseEventPublisher` for your service:

```java
@Component
public class UserEventPublisher extends BaseEventPublisher {
    
    private static final String USER_EVENTS_TOPIC = "user.events";
    
    public UserEventPublisher(KafkaTemplate<String, String> kafkaTemplate,
                             ObjectMapper objectMapper,
                             MeterRegistry meterRegistry) {
        super(kafkaTemplate, objectMapper, meterRegistry);
    }
    
    public CompletableFuture<SendResult<String, String>> publishUserCreated(User user) {
        UserCreatedEvent event = new UserCreatedEvent(
            user.getId(), user.getUsername(), user.getEmail()
        );
        
        return publishEvent(USER_EVENTS_TOPIC, user.getId().toString(), event);
    }
    
    @Override
    protected String getServiceName() {
        return "user-management-service";
    }
}
```

### 4. Use in Application Service

Inject and use the event publisher:

```java
@Service
@Transactional
public class UserApplicationService {
    
    private final UserEventPublisher eventPublisher;
    
    public UserDto createUser(CreateUserRequest request) {
        // Create user logic...
        User user = userRepository.save(newUser);
        
        // Publish event
        eventPublisher.publishUserCreated(user)
            .whenComplete((result, ex) -> {
                if (ex != null) {
                    logger.error("Failed to publish UserCreatedEvent", ex);
                }
            });
        
        return userMapper.toDto(user);
    }
}
```

## 🔧 Configuration

### Application Properties

```yaml
spring:
  kafka:
    bootstrap-servers: localhost:9092
    producer:
      acks: all
      retries: 3
      enable-idempotence: true
      compression-type: snappy
      batch-size: 16384
      linger-ms: 5
```

### Auto-Configuration

The framework provides auto-configuration for:
- Kafka producer factory
- Event object mapper
- Meter registry integration

## 📊 Event Structure

All events extend `BaseEvent` and include:

```json
{
  "eventId": "550e8400-e29b-41d4-a716-446655440000",
  "eventType": "UserCreated",
  "eventVersion": "1.0",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "source": "user-management-service",
  "correlationId": "550e8400-e29b-41d4-a716-446655440001",
  "causationId": "550e8400-e29b-41d4-a716-446655440002",
  "aggregateId": "550e8400-e29b-41d4-a716-446655440003",
  "aggregateType": "User",
  "aggregateVersion": 1,
  "metadata": {
    "traceId": "abc123",
    "spanId": "def456",
    "publishedBy": "UserEventPublisher",
    "publishedAt": 1705312200000
  },
  // Event-specific data...
  "userId": "550e8400-e29b-41d4-a716-446655440003",
  "username": "john.doe",
  "email": "<EMAIL>"
}
```

## 🔍 Correlation Context

### Automatic Management

The framework automatically manages correlation context:

```java
// Correlation ID is automatically set from MDC or generated
CorrelationContext.setCorrelationId("my-correlation-id");

// Events published within this context will inherit the correlation ID
eventPublisher.publishUserCreated(user);
```

### Manual Management

```java
// Execute with specific correlation ID
CorrelationContext.executeWithCorrelationId("correlation-123", () -> {
    eventPublisher.publishUserCreated(user);
});

// Generate new correlation ID
String correlationId = CorrelationContext.executeWithNewCorrelationId(() -> {
    eventPublisher.publishUserCreated(user);
});
```

## 📈 Metrics

The framework automatically collects metrics:

### Publishing Metrics
- `event.publish.success` - Successful event publications
- `event.publish.failure` - Failed event publications
- `event.publish.duration` - Time taken to publish events
- `event.serialization.error` - Serialization failures

### Consumption Metrics
- `event.consume.success` - Successful event consumption
- `event.consume.failure` - Failed event consumption
- `event.processing.duration` - Event processing time
- `event.processing.lag` - Time between event creation and processing
- `event.deserialization.error` - Deserialization failures

### Metric Tags
All metrics include tags for:
- `topic` - Kafka topic name
- `event_type` - Type of event
- `source` - Source service
- `consumer_group` - Consumer group (for consumption metrics)

## 🧪 Testing

### Unit Testing

```java
@ExtendWith(MockitoExtension.class)
class UserEventPublisherTest {
    
    @Mock
    private KafkaTemplate<String, String> kafkaTemplate;
    
    private UserEventPublisher eventPublisher;
    
    @BeforeEach
    void setUp() {
        ObjectMapper objectMapper = new ObjectMapper();
        MeterRegistry meterRegistry = new SimpleMeterRegistry();
        eventPublisher = new UserEventPublisher(kafkaTemplate, objectMapper, meterRegistry);
    }
    
    @Test
    void shouldPublishUserCreatedEvent() {
        // Given
        User user = new User("john.doe", "<EMAIL>");
        when(kafkaTemplate.send(any(), any(), any()))
            .thenReturn(CompletableFuture.completedFuture(mock(SendResult.class)));
        
        // When
        CompletableFuture<SendResult<String, String>> result = 
            eventPublisher.publishUserCreated(user);
        
        // Then
        assertThat(result).isCompleted();
        verify(kafkaTemplate).send(eq("user.events"), eq(user.getId().toString()), any());
    }
}
```

### Integration Testing

```java
@SpringBootTest
@TestPropertySource(properties = {
    "spring.kafka.bootstrap-servers=${spring.embedded.kafka.brokers}"
})
@EmbeddedKafka(topics = "user.events")
class UserEventPublisherIntegrationTest {
    
    @Autowired
    private UserEventPublisher eventPublisher;
    
    @Test
    void shouldPublishAndConsumeUserCreatedEvent() {
        // Test implementation...
    }
}
```

## 🔒 Best Practices

### Event Design
1. **Immutable Events**: Events should be immutable once created
2. **Backward Compatibility**: Use event versioning for schema evolution
3. **Rich Events**: Include sufficient data to avoid additional lookups
4. **Idempotent Processing**: Design consumers to handle duplicate events

### Error Handling
1. **Graceful Degradation**: Don't fail business operations due to event publishing failures
2. **Retry Logic**: Use exponential backoff for transient failures
3. **Dead Letter Queues**: Handle permanently failed events
4. **Monitoring**: Alert on high error rates

### Performance
1. **Async Publishing**: Use async publishing to avoid blocking business operations
2. **Batching**: Configure appropriate batch sizes for throughput
3. **Compression**: Use compression for large events
4. **Partitioning**: Use appropriate partition keys for load distribution

This framework provides a solid foundation for event-driven architecture across all NDVN SCS services, ensuring consistency, reliability, and observability.

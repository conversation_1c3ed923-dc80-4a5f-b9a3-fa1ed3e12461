package com.nttdata.ndvn.shared.events;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

/**
 * Base class for all domain events in the NDVN SCS platform.
 * 
 * This class provides common event metadata and ensures consistency
 * across all event types in the system.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public abstract class BaseEvent {
    
    @NotNull
    private UUID eventId;
    
    @NotBlank
    private String eventType;
    
    @NotBlank
    private String eventVersion;
    
    @NotNull
    private Instant timestamp;
    
    @NotBlank
    private String source;
    
    private String correlationId;
    
    private String causationId;
    
    private String aggregateId;
    
    private String aggregateType;
    
    private Long aggregateVersion;
    
    private Map<String, Object> metadata;
    
    // Constructors
    protected BaseEvent() {
        this.eventId = UUID.randomUUID();
        this.timestamp = Instant.now();
        this.metadata = new HashMap<>();
    }
    
    protected BaseEvent(String eventType, String eventVersion, String source) {
        this();
        this.eventType = eventType;
        this.eventVersion = eventVersion;
        this.source = source;
    }
    
    protected BaseEvent(String eventType, String eventVersion, String source, 
                       String aggregateId, String aggregateType) {
        this(eventType, eventVersion, source);
        this.aggregateId = aggregateId;
        this.aggregateType = aggregateType;
    }
    
    // Business methods
    public void addMetadata(String key, Object value) {
        if (this.metadata == null) {
            this.metadata = new HashMap<>();
        }
        this.metadata.put(key, value);
    }
    
    public Object getMetadata(String key) {
        return this.metadata != null ? this.metadata.get(key) : null;
    }
    
    public boolean hasMetadata(String key) {
        return this.metadata != null && this.metadata.containsKey(key);
    }
    
    public void setCorrelationContext(String correlationId, String causationId) {
        this.correlationId = correlationId;
        this.causationId = causationId;
    }
    
    public void setAggregateContext(String aggregateId, String aggregateType, Long aggregateVersion) {
        this.aggregateId = aggregateId;
        this.aggregateType = aggregateType;
        this.aggregateVersion = aggregateVersion;
    }
    
    // Getters and setters
    public UUID getEventId() { return eventId; }
    public void setEventId(UUID eventId) { this.eventId = eventId; }
    
    public String getEventType() { return eventType; }
    public void setEventType(String eventType) { this.eventType = eventType; }
    
    public String getEventVersion() { return eventVersion; }
    public void setEventVersion(String eventVersion) { this.eventVersion = eventVersion; }
    
    public Instant getTimestamp() { return timestamp; }
    public void setTimestamp(Instant timestamp) { this.timestamp = timestamp; }
    
    public String getSource() { return source; }
    public void setSource(String source) { this.source = source; }
    
    public String getCorrelationId() { return correlationId; }
    public void setCorrelationId(String correlationId) { this.correlationId = correlationId; }
    
    public String getCausationId() { return causationId; }
    public void setCausationId(String causationId) { this.causationId = causationId; }
    
    public String getAggregateId() { return aggregateId; }
    public void setAggregateId(String aggregateId) { this.aggregateId = aggregateId; }
    
    public String getAggregateType() { return aggregateType; }
    public void setAggregateType(String aggregateType) { this.aggregateType = aggregateType; }
    
    public Long getAggregateVersion() { return aggregateVersion; }
    public void setAggregateVersion(Long aggregateVersion) { this.aggregateVersion = aggregateVersion; }
    
    public Map<String, Object> getMetadata() { return metadata; }
    public void setMetadata(Map<String, Object> metadata) { this.metadata = metadata; }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BaseEvent baseEvent = (BaseEvent) o;
        return Objects.equals(eventId, baseEvent.eventId);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(eventId);
    }
    
    @Override
    public String toString() {
        return "BaseEvent{" +
                "eventId=" + eventId +
                ", eventType='" + eventType + '\'' +
                ", eventVersion='" + eventVersion + '\'' +
                ", timestamp=" + timestamp +
                ", source='" + source + '\'' +
                ", correlationId='" + correlationId + '\'' +
                ", aggregateId='" + aggregateId + '\'' +
                ", aggregateType='" + aggregateType + '\'' +
                '}';
    }
}

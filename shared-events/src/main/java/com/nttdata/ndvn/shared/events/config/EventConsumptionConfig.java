package com.nttdata.ndvn.shared.events.config;

import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.listener.ContainerProperties;
import org.springframework.kafka.listener.DefaultErrorHandler;
import org.springframework.util.backoff.ExponentialBackOff;
import org.apache.kafka.common.utils.LogContext;

import java.util.HashMap;
import java.util.Map;

/**
 * Configuration for event consumption infrastructure.
 */
@Configuration
public class EventConsumptionConfig {
    
    @Value("${spring.kafka.bootstrap-servers:localhost:9092}")
    private String bootstrapServers;
    
    @Value("${spring.kafka.consumer.group-id:default-consumer-group}")
    private String defaultGroupId;
    
    @Value("${spring.kafka.consumer.auto-offset-reset:earliest}")
    private String autoOffsetReset;
    
    @Value("${spring.kafka.consumer.enable-auto-commit:false}")
    private Boolean enableAutoCommit;
    
    @Value("${spring.kafka.consumer.max-poll-records:500}")
    private Integer maxPollRecords;
    
    @Value("${spring.kafka.consumer.max-poll-interval-ms:300000}")
    private Integer maxPollIntervalMs;
    
    @Value("${spring.kafka.consumer.session-timeout-ms:30000}")
    private Integer sessionTimeoutMs;
    
    @Value("${spring.kafka.consumer.heartbeat-interval-ms:3000}")
    private Integer heartbeatIntervalMs;
    
    @Value("${spring.kafka.consumer.fetch-min-size:1}")
    private Integer fetchMinSize;
    
    @Value("${spring.kafka.consumer.fetch-max-wait:500}")
    private Integer fetchMaxWait;
    
    @Value("${app.events.consumer.retry.initial-interval:1000}")
    private Long retryInitialInterval;
    
    @Value("${app.events.consumer.retry.max-interval:30000}")
    private Long retryMaxInterval;
    
    @Value("${app.events.consumer.retry.multiplier:2.0}")
    private Double retryMultiplier;
    
    @Value("${app.events.consumer.retry.max-attempts:3}")
    private Integer retryMaxAttempts;
    
    @Value("${app.events.consumer.concurrency:3}")
    private Integer consumerConcurrency;
    
    /**
     * Kafka consumer factory for event consumption.
     */
    @Bean
    @ConditionalOnMissingBean
    public ConsumerFactory<String, String> eventConsumerFactory() {
        Map<String, Object> configProps = new HashMap<>();
        
        // Basic configuration
        configProps.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        configProps.put(ConsumerConfig.GROUP_ID_CONFIG, defaultGroupId);
        configProps.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        configProps.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        
        // Offset management
        configProps.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, autoOffsetReset);
        configProps.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, enableAutoCommit);
        
        // Performance configuration
        configProps.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, maxPollRecords);
        configProps.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, maxPollIntervalMs);
        configProps.put(ConsumerConfig.FETCH_MIN_BYTES_CONFIG, fetchMinSize);
        configProps.put(ConsumerConfig.FETCH_MAX_WAIT_MS_CONFIG, fetchMaxWait);
        
        // Session management
        configProps.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, sessionTimeoutMs);
        configProps.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG, heartbeatIntervalMs);
        
        // Reliability configuration
        configProps.put(ConsumerConfig.ISOLATION_LEVEL_CONFIG, "read_committed");
        
        return new DefaultKafkaConsumerFactory<>(configProps);
    }
    
    /**
     * Kafka listener container factory for event consumption.
     */
    @Bean
    @ConditionalOnMissingBean
    public ConcurrentKafkaListenerContainerFactory<String, String> eventKafkaListenerContainerFactory(
            ConsumerFactory<String, String> consumerFactory) {
        
        ConcurrentKafkaListenerContainerFactory<String, String> factory = 
            new ConcurrentKafkaListenerContainerFactory<>();
        
        factory.setConsumerFactory(consumerFactory);
        factory.setConcurrency(consumerConcurrency);
        
        // Configure container properties
        ContainerProperties containerProperties = factory.getContainerProperties();
        containerProperties.setAckMode(ContainerProperties.AckMode.MANUAL_IMMEDIATE);
        containerProperties.setSyncCommits(true);
        // Remove the problematic commit log level setting

        // Configure error handling with exponential backoff
        ExponentialBackOff backOff = new ExponentialBackOff();
        backOff.setInitialInterval(retryInitialInterval);
        backOff.setMaxInterval(retryMaxInterval);
        backOff.setMultiplier(retryMultiplier);

        DefaultErrorHandler errorHandler = new DefaultErrorHandler(backOff);
        errorHandler.setRetryListeners((record, ex, deliveryAttempt) -> {
            // Log retry attempts
            org.slf4j.LoggerFactory.getLogger(EventConsumptionConfig.class)
                .warn("Retrying event processing - Topic: {}, Partition: {}, Offset: {}, Attempt: {}",
                     record.topic(), record.partition(), record.offset(), deliveryAttempt);
        });

        // Error handler is already configured with backoff in constructor
        
        factory.setCommonErrorHandler(errorHandler);
        
        return factory;
    }
    
    /**
     * Consumer factory specifically for event replay operations.
     */
    @Bean
    @ConditionalOnMissingBean(name = "replayConsumerFactory")
    public ConsumerFactory<String, String> replayConsumerFactory() {
        Map<String, Object> configProps = new HashMap<>();
        
        // Basic configuration
        configProps.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        configProps.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        configProps.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        
        // Replay-specific configuration
        configProps.put(ConsumerConfig.GROUP_ID_CONFIG, "event-replay-" + System.currentTimeMillis());
        configProps.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");
        configProps.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        configProps.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, 1000); // Higher batch size for replay
        configProps.put(ConsumerConfig.FETCH_MIN_BYTES_CONFIG, 1024); // Larger fetch size for replay
        configProps.put(ConsumerConfig.FETCH_MAX_WAIT_MS_CONFIG, 100); // Lower wait time for replay
        
        return new DefaultKafkaConsumerFactory<>(configProps);
    }
}

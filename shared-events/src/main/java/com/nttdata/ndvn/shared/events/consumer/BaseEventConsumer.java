package com.nttdata.ndvn.shared.events.consumer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nttdata.ndvn.shared.events.BaseEvent;
import com.nttdata.ndvn.shared.events.util.CorrelationContext;
import com.nttdata.ndvn.shared.events.util.EventMetrics;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;

import java.time.Instant;
import java.util.Map;

/**
 * Abstract base class for event consumers.
 * 
 * Provides standardized event consumption functionality including:
 * - Event deserialization and validation
 * - Correlation ID management
 * - Error handling and retry logic
 * - Metrics collection
 * - Dead letter queue handling
 * - Idempotency support
 */
public abstract class BaseEventConsumer {
    
    private static final Logger logger = LoggerFactory.getLogger(BaseEventConsumer.class);
    
    protected final ObjectMapper objectMapper;
    protected final MeterRegistry meterRegistry;
    protected final EventMetrics eventMetrics;
    
    protected BaseEventConsumer(ObjectMapper objectMapper, MeterRegistry meterRegistry) {
        this.objectMapper = objectMapper;
        this.meterRegistry = meterRegistry;
        this.eventMetrics = new EventMetrics(meterRegistry);
    }
    
    /**
     * Processes an event with standardized error handling and metrics.
     * 
     * @param eventPayload the event payload as JSON string
     * @param headers Kafka headers
     * @param acknowledgment Kafka acknowledgment
     * @param eventClass the expected event class
     * @param processor the event processor function
     */
    protected <T extends BaseEvent> void processEvent(
            String eventPayload,
            Map<String, Object> headers,
            Acknowledgment acknowledgment,
            Class<T> eventClass,
            EventProcessor<T> processor) {
        
        Timer.Sample sample = Timer.start(meterRegistry);
        String topic = getHeaderValue(headers, KafkaHeaders.RECEIVED_TOPIC);
        String consumerGroup = getConsumerGroup();
        
        try {
            // Deserialize event
            T event = deserializeEvent(eventPayload, eventClass);
            
            // Set up correlation context
            setupCorrelationContext(event, headers);
            
            // Validate event
            validateEvent(event);
            
            // Check for duplicate processing (idempotency)
            if (isDuplicateEvent(event)) {
                logger.info("Skipping duplicate event: {} with ID: {}", event.getEventType(), event.getEventId());
                acknowledgment.acknowledge();
                return;
            }
            
            // Calculate event lag
            long eventLag = calculateEventLag(event);
            eventMetrics.recordEventLag(topic, event.getEventType(), consumerGroup, eventLag);
            
            // Process the event
            processor.process(event);
            
            // Mark event as processed for idempotency
            markEventAsProcessed(event);
            
            // Acknowledge successful processing
            acknowledgment.acknowledge();
            
            // Record success metrics
            eventMetrics.incrementConsumeSuccess(topic, event.getEventType(), consumerGroup);
            
            logger.info("Successfully processed event: {} with ID: {}", event.getEventType(), event.getEventId());
            
        } catch (JsonProcessingException e) {
            handleDeserializationError(eventPayload, topic, consumerGroup, e, acknowledgment);
        } catch (EventValidationException e) {
            handleValidationError(eventPayload, topic, consumerGroup, e, acknowledgment);
        } catch (DuplicateEventException e) {
            handleDuplicateEvent(eventPayload, topic, consumerGroup, e, acknowledgment);
        } catch (Exception e) {
            handleProcessingError(eventPayload, topic, consumerGroup, e, acknowledgment);
        } finally {
            sample.stop(Timer.builder("event.processing.duration")
                .tag("topic", topic)
                .tag("consumer_group", consumerGroup)
                .register(meterRegistry));
            
            // Clear correlation context
            CorrelationContext.clearCorrelationContext();
            MDC.clear();
        }
    }
    
    /**
     * Deserializes the event from JSON payload.
     */
    private <T extends BaseEvent> T deserializeEvent(String eventPayload, Class<T> eventClass) 
            throws JsonProcessingException {
        try {
            return objectMapper.readValue(eventPayload, eventClass);
        } catch (JsonProcessingException e) {
            logger.error("Failed to deserialize event payload: {}", eventPayload, e);
            throw e;
        }
    }
    
    /**
     * Sets up correlation context from event and headers.
     */
    private void setupCorrelationContext(BaseEvent event, Map<String, Object> headers) {
        // Set correlation ID from event
        if (event.getCorrelationId() != null) {
            CorrelationContext.setCorrelationId(event.getCorrelationId());
        }
        
        // Set current event ID for causation tracking
        CorrelationContext.setCurrentEventId(event.getEventId().toString());
        
        // Set trace information from headers if available
        String traceId = getHeaderValue(headers, "traceId");
        if (traceId != null) {
            CorrelationContext.setTraceId(traceId);
        }
        
        String spanId = getHeaderValue(headers, "spanId");
        if (spanId != null) {
            CorrelationContext.setSpanId(spanId);
        }
    }
    
    /**
     * Validates the event.
     */
    private void validateEvent(BaseEvent event) {
        if (event.getEventId() == null) {
            throw new EventValidationException("Event ID is required");
        }
        
        if (event.getEventType() == null || event.getEventType().trim().isEmpty()) {
            throw new EventValidationException("Event type is required");
        }
        
        if (event.getTimestamp() == null) {
            throw new EventValidationException("Event timestamp is required");
        }
        
        // Allow subclasses to add additional validation
        doAdditionalValidation(event);
    }
    
    /**
     * Calculates the lag between event creation and processing.
     */
    private long calculateEventLag(BaseEvent event) {
        if (event.getTimestamp() != null) {
            return Instant.now().toEpochMilli() - event.getTimestamp().toEpochMilli();
        }
        return 0;
    }
    
    /**
     * Handles deserialization errors.
     */
    private void handleDeserializationError(String eventPayload, String topic, String consumerGroup, 
                                          JsonProcessingException e, Acknowledgment acknowledgment) {
        eventMetrics.incrementDeserializationError(topic, consumerGroup);
        logger.error("Failed to deserialize event from topic: {} - Payload: {}", topic, eventPayload, e);
        
        // Send to dead letter queue
        sendToDeadLetterQueue(eventPayload, topic, "DESERIALIZATION_ERROR", e.getMessage());
        
        // Acknowledge to prevent reprocessing
        acknowledgment.acknowledge();
    }
    
    /**
     * Handles validation errors.
     */
    private void handleValidationError(String eventPayload, String topic, String consumerGroup, 
                                     EventValidationException e, Acknowledgment acknowledgment) {
        eventMetrics.incrementConsumeFailure(topic, "UNKNOWN", consumerGroup);
        logger.error("Event validation failed for topic: {} - Payload: {}", topic, eventPayload, e);
        
        // Send to dead letter queue
        sendToDeadLetterQueue(eventPayload, topic, "VALIDATION_ERROR", e.getMessage());
        
        // Acknowledge to prevent reprocessing
        acknowledgment.acknowledge();
    }
    
    /**
     * Handles duplicate events.
     */
    private void handleDuplicateEvent(String eventPayload, String topic, String consumerGroup, 
                                    DuplicateEventException e, Acknowledgment acknowledgment) {
        logger.info("Duplicate event detected for topic: {} - Event ID: {}", topic, e.getEventId());
        
        // Acknowledge duplicate events
        acknowledgment.acknowledge();
    }
    
    /**
     * Handles processing errors.
     */
    private void handleProcessingError(String eventPayload, String topic, String consumerGroup, 
                                     Exception e, Acknowledgment acknowledgment) {
        eventMetrics.incrementConsumeFailure(topic, "UNKNOWN", consumerGroup);
        logger.error("Failed to process event from topic: {} - Payload: {}", topic, eventPayload, e);
        
        // Check if this is a retryable error
        if (isRetryableError(e)) {
            // Don't acknowledge - let Kafka retry
            logger.info("Retryable error detected, will retry processing");
            throw new RuntimeException("Retryable error", e);
        } else {
            // Send to dead letter queue for non-retryable errors
            sendToDeadLetterQueue(eventPayload, topic, "PROCESSING_ERROR", e.getMessage());
            acknowledgment.acknowledge();
        }
    }
    
    /**
     * Gets header value as string.
     */
    private String getHeaderValue(Map<String, Object> headers, String key) {
        Object value = headers.get(key);
        return value != null ? value.toString() : null;
    }
    
    /**
     * Template method for subclasses to add additional validation.
     */
    protected void doAdditionalValidation(BaseEvent event) {
        // Default implementation does nothing
    }
    
    /**
     * Template method for checking if an event is a duplicate.
     */
    protected boolean isDuplicateEvent(BaseEvent event) {
        // Default implementation - subclasses should override for idempotency
        return false;
    }
    
    /**
     * Template method for marking an event as processed.
     */
    protected void markEventAsProcessed(BaseEvent event) {
        // Default implementation - subclasses should override for idempotency
    }
    
    /**
     * Template method for determining if an error is retryable.
     */
    protected boolean isRetryableError(Exception e) {
        // Default implementation - consider network and temporary errors as retryable
        return e instanceof java.net.ConnectException ||
               e instanceof java.net.SocketTimeoutException ||
               e instanceof org.springframework.dao.TransientDataAccessException;
    }
    
    /**
     * Template method for sending events to dead letter queue.
     */
    protected void sendToDeadLetterQueue(String eventPayload, String topic, String errorType, String errorMessage) {
        // Default implementation - subclasses should override for actual DLQ implementation
        logger.warn("Sending event to dead letter queue - Topic: {}, Error: {}, Message: {}", 
                   topic, errorType, errorMessage);
    }
    
    /**
     * Gets the consumer group name.
     */
    protected abstract String getConsumerGroup();
    
    /**
     * Functional interface for event processing.
     */
    @FunctionalInterface
    protected interface EventProcessor<T extends BaseEvent> {
        void process(T event) throws Exception;
    }
    
    /**
     * Exception thrown when event validation fails.
     */
    public static class EventValidationException extends RuntimeException {
        public EventValidationException(String message) {
            super(message);
        }
    }
    
    /**
     * Exception thrown when a duplicate event is detected.
     */
    public static class DuplicateEventException extends RuntimeException {
        private final String eventId;
        
        public DuplicateEventException(String eventId) {
            super("Duplicate event detected: " + eventId);
            this.eventId = eventId;
        }
        
        public String getEventId() {
            return eventId;
        }
    }
}

package com.nttdata.ndvn.shared.events.dlq;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Handler for managing dead letter queue operations.
 * 
 * This component handles failed events by sending them to appropriate
 * dead letter queues with metadata about the failure.
 */
@Component
public class DeadLetterQueueHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(DeadLetterQueueHandler.class);
    private static final String DLQ_SUFFIX = ".dlq";
    
    private final KafkaTemplate<String, String> kafkaTemplate;
    private final ObjectMapper objectMapper;
    
    public DeadLetterQueueHandler(KafkaTemplate<String, String> kafkaTemplate, ObjectMapper objectMapper) {
        this.kafkaTemplate = kafkaTemplate;
        this.objectMapper = objectMapper;
    }
    
    /**
     * Sends a failed event to the dead letter queue.
     * 
     * @param originalTopic the original topic where the event failed
     * @param eventPayload the original event payload
     * @param errorType the type of error that occurred
     * @param errorMessage the error message
     * @param originalHeaders the original headers from the failed event
     * @return CompletableFuture for the send result
     */
    public CompletableFuture<SendResult<String, String>> sendToDeadLetterQueue(
            String originalTopic,
            String eventPayload,
            String errorType,
            String errorMessage,
            Map<String, Object> originalHeaders) {
        
        try {
            String dlqTopic = originalTopic + DLQ_SUFFIX;
            
            // Create DLQ event wrapper
            DeadLetterEvent dlqEvent = new DeadLetterEvent(
                originalTopic,
                eventPayload,
                errorType,
                errorMessage,
                originalHeaders,
                Instant.now()
            );
            
            String dlqPayload = objectMapper.writeValueAsString(dlqEvent);
            
            // Send to DLQ topic
            CompletableFuture<SendResult<String, String>> future = 
                kafkaTemplate.send(dlqTopic, dlqPayload);
            
            future.whenComplete((result, ex) -> {
                if (ex == null) {
                    logger.info("Successfully sent event to DLQ topic: {} - Original topic: {}", 
                               dlqTopic, originalTopic);
                } else {
                    logger.error("Failed to send event to DLQ topic: {} - Original topic: {}", 
                                dlqTopic, originalTopic, ex);
                }
            });
            
            return future;
            
        } catch (Exception e) {
            logger.error("Failed to create DLQ event for topic: {}", originalTopic, e);
            return CompletableFuture.failedFuture(e);
        }
    }
    
    /**
     * Sends a failed event to the dead letter queue with minimal information.
     * 
     * @param originalTopic the original topic where the event failed
     * @param eventPayload the original event payload
     * @param errorType the type of error that occurred
     * @param errorMessage the error message
     * @return CompletableFuture for the send result
     */
    public CompletableFuture<SendResult<String, String>> sendToDeadLetterQueue(
            String originalTopic,
            String eventPayload,
            String errorType,
            String errorMessage) {
        
        return sendToDeadLetterQueue(originalTopic, eventPayload, errorType, errorMessage, new HashMap<>());
    }
    
    /**
     * Retries a failed event from the dead letter queue.
     * 
     * @param dlqEvent the dead letter event to retry
     * @return CompletableFuture for the send result
     */
    public CompletableFuture<SendResult<String, String>> retryFromDeadLetterQueue(DeadLetterEvent dlqEvent) {
        try {
            // Add retry metadata
            Map<String, Object> retryHeaders = new HashMap<>(dlqEvent.getOriginalHeaders());
            retryHeaders.put("dlq.retried", true);
            retryHeaders.put("dlq.retry.timestamp", Instant.now().toString());
            retryHeaders.put("dlq.original.error.type", dlqEvent.getErrorType());
            retryHeaders.put("dlq.original.error.message", dlqEvent.getErrorMessage());
            
            // Send back to original topic
            CompletableFuture<SendResult<String, String>> future = 
                kafkaTemplate.send(dlqEvent.getOriginalTopic(), dlqEvent.getOriginalPayload());
            
            future.whenComplete((result, ex) -> {
                if (ex == null) {
                    logger.info("Successfully retried event from DLQ to topic: {}", dlqEvent.getOriginalTopic());
                } else {
                    logger.error("Failed to retry event from DLQ to topic: {}", dlqEvent.getOriginalTopic(), ex);
                }
            });
            
            return future;
            
        } catch (Exception e) {
            logger.error("Failed to retry event from DLQ for topic: {}", dlqEvent.getOriginalTopic(), e);
            return CompletableFuture.failedFuture(e);
        }
    }
    
    /**
     * Gets the dead letter queue topic name for a given original topic.
     * 
     * @param originalTopic the original topic name
     * @return the DLQ topic name
     */
    public String getDlqTopicName(String originalTopic) {
        return originalTopic + DLQ_SUFFIX;
    }
    
    /**
     * Checks if a topic is a dead letter queue topic.
     * 
     * @param topicName the topic name to check
     * @return true if it's a DLQ topic, false otherwise
     */
    public boolean isDlqTopic(String topicName) {
        return topicName != null && topicName.endsWith(DLQ_SUFFIX);
    }
    
    /**
     * Gets the original topic name from a DLQ topic name.
     * 
     * @param dlqTopicName the DLQ topic name
     * @return the original topic name
     */
    public String getOriginalTopicName(String dlqTopicName) {
        if (isDlqTopic(dlqTopicName)) {
            return dlqTopicName.substring(0, dlqTopicName.length() - DLQ_SUFFIX.length());
        }
        return dlqTopicName;
    }
    
    /**
     * Dead letter event wrapper class.
     */
    public static class DeadLetterEvent {
        private String originalTopic;
        private String originalPayload;
        private String errorType;
        private String errorMessage;
        private Map<String, Object> originalHeaders;
        private Instant failedAt;
        private String dlqId;
        
        // Default constructor for Jackson
        public DeadLetterEvent() {}
        
        public DeadLetterEvent(String originalTopic, String originalPayload, String errorType,
                              String errorMessage, Map<String, Object> originalHeaders, Instant failedAt) {
            this.originalTopic = originalTopic;
            this.originalPayload = originalPayload;
            this.errorType = errorType;
            this.errorMessage = errorMessage;
            this.originalHeaders = originalHeaders != null ? new HashMap<>(originalHeaders) : new HashMap<>();
            this.failedAt = failedAt;
            this.dlqId = java.util.UUID.randomUUID().toString();
        }
        
        // Getters and setters
        public String getOriginalTopic() { return originalTopic; }
        public void setOriginalTopic(String originalTopic) { this.originalTopic = originalTopic; }
        
        public String getOriginalPayload() { return originalPayload; }
        public void setOriginalPayload(String originalPayload) { this.originalPayload = originalPayload; }
        
        public String getErrorType() { return errorType; }
        public void setErrorType(String errorType) { this.errorType = errorType; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        public Map<String, Object> getOriginalHeaders() { return originalHeaders; }
        public void setOriginalHeaders(Map<String, Object> originalHeaders) { this.originalHeaders = originalHeaders; }
        
        public Instant getFailedAt() { return failedAt; }
        public void setFailedAt(Instant failedAt) { this.failedAt = failedAt; }
        
        public String getDlqId() { return dlqId; }
        public void setDlqId(String dlqId) { this.dlqId = dlqId; }
        
        @Override
        public String toString() {
            return "DeadLetterEvent{" +
                    "originalTopic='" + originalTopic + '\'' +
                    ", errorType='" + errorType + '\'' +
                    ", errorMessage='" + errorMessage + '\'' +
                    ", failedAt=" + failedAt +
                    ", dlqId='" + dlqId + '\'' +
                    '}';
        }
    }
}

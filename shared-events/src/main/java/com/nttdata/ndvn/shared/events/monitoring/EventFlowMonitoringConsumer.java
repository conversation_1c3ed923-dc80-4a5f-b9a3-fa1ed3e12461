package com.nttdata.ndvn.shared.events.monitoring;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.nttdata.ndvn.shared.events.BaseEvent;
import com.nttdata.ndvn.shared.events.consumer.BaseEventConsumer;
import com.nttdata.ndvn.shared.events.dlq.DeadLetterQueueHandler;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Event consumer for monitoring cross-service event flows and collecting metrics.
 * 
 * This consumer tracks event flows across all services to provide insights into:
 * - Event processing latency
 * - Event correlation tracking
 * - Cross-service communication patterns
 * - Error rates and patterns
 * - Event volume and throughput
 */
@Component
public class EventFlowMonitoringConsumer extends BaseEventConsumer {
    
    private static final Logger logger = LoggerFactory.getLogger(EventFlowMonitoringConsumer.class);
    private static final String CONSUMER_GROUP = "event-flow-monitoring";
    
    private final DeadLetterQueueHandler dlqHandler;
    private final Counter eventFlowCounter;
    private final Counter correlationTrackingCounter;
    private final Timer eventProcessingLatencyTimer;
    private final Timer crossServiceLatencyTimer;
    
    // Simple in-memory store for processed event IDs
    private final Set<String> processedEventIds = ConcurrentHashMap.newKeySet();
    
    // Correlation tracking for cross-service flows
    private final Map<String, EventFlowTrace> correlationTraces = new ConcurrentHashMap<>();
    
    public EventFlowMonitoringConsumer(ObjectMapper objectMapper,
                                     MeterRegistry meterRegistry,
                                     DeadLetterQueueHandler dlqHandler) {
        super(objectMapper, meterRegistry);
        this.dlqHandler = dlqHandler;
        
        // Initialize metrics
        this.eventFlowCounter = Counter.builder("event.flow.total")
            .description("Total number of events processed for flow monitoring")
            .register(meterRegistry);
        
        this.correlationTrackingCounter = Counter.builder("event.correlation.tracked")
            .description("Number of events with correlation tracking")
            .register(meterRegistry);
        
        this.eventProcessingLatencyTimer = Timer.builder("event.processing.latency")
            .description("Event processing latency from creation to consumption")
            .register(meterRegistry);
        
        this.crossServiceLatencyTimer = Timer.builder("event.cross.service.latency")
            .description("Cross-service event propagation latency")
            .register(meterRegistry);
    }
    
    /**
     * Monitors all events from all topics for flow analysis.
     */
    @KafkaListener(topicPattern = ".*\\.events", groupId = CONSUMER_GROUP)
    public void monitorEventFlow(@Payload String eventPayload,
                               @Header Map<String, Object> headers,
                               Acknowledgment acknowledgment) {
        
        processEvent(eventPayload, headers, acknowledgment, BaseEvent.class, this::trackEventFlow);
    }
    
    /**
     * Tracks event flow and collects metrics.
     */
    private void trackEventFlow(BaseEvent event) {
        String topic = getCurrentTopic(event);
        String eventType = event.getEventType();
        String source = event.getSource();
        
        logger.debug("Tracking event flow: {} from {} on topic {}", eventType, source, topic);
        
        // Increment flow counter
        Counter.builder("event.flow.count")
            .tag("topic", topic)
            .tag("event_type", eventType)
            .tag("source", source)
            .register(meterRegistry)
            .increment();
        
        // Track event processing latency
        if (event.getTimestamp() != null) {
            Duration processingLatency = Duration.between(event.getTimestamp(), Instant.now());
            eventProcessingLatencyTimer.record(processingLatency);
            
            logger.debug("Event processing latency: {}ms for event: {}", 
                        processingLatency.toMillis(), event.getEventId());
        }
        
        // Track correlation flows
        if (event.getCorrelationId() != null) {
            trackCorrelationFlow(event, topic);
        }
        
        // Track causation chains
        if (event.getCausationId() != null) {
            trackCausationChain(event, topic);
        }
        
        // Detect cross-service patterns
        detectCrossServicePatterns(event, topic);
        
        // Log significant events
        logSignificantEvents(event, topic);
    }
    
    /**
     * Tracks correlation flow across services.
     */
    private void trackCorrelationFlow(BaseEvent event, String topic) {
        String correlationId = event.getCorrelationId();
        
        EventFlowTrace trace = correlationTraces.computeIfAbsent(correlationId, 
            k -> new EventFlowTrace(correlationId));
        
        trace.addEvent(new EventFlowStep(
            event.getEventId().toString(),
            event.getEventType(),
            event.getSource(),
            topic,
            Instant.now(),
            event.getTimestamp()
        ));
        
        Counter.builder("event.correlation.count")
            .tag("correlation_id", correlationId)
            .tag("event_type", event.getEventType())
            .tag("source", event.getSource())
            .register(meterRegistry)
            .increment();
        
        // Calculate cross-service latency if this is a response to another event
        calculateCrossServiceLatency(trace, event);
        
        logger.debug("Tracked correlation flow: {} for event: {}", correlationId, event.getEventId());
    }
    
    /**
     * Tracks causation chains between events.
     */
    private void trackCausationChain(BaseEvent event, String topic) {
        String causationId = event.getCausationId();
        
        logger.debug("Tracking causation chain: {} caused by {} for event: {}", 
                    event.getEventId(), causationId, event.getEventType());
        
        // Find the causing event in correlation traces
        for (EventFlowTrace trace : correlationTraces.values()) {
            EventFlowStep causingStep = trace.findEventById(causationId);
            if (causingStep != null) {
                // Calculate causation latency
                Duration causationLatency = Duration.between(causingStep.getProcessedAt(), Instant.now());
                
                Timer.builder("event.cross.service.latency")
                    .tag("causing_service", causingStep.getSource())
                    .tag("caused_service", event.getSource())
                    .tag("causing_event", causingStep.getEventType())
                    .tag("caused_event", event.getEventType())
                    .register(meterRegistry)
                    .record(causationLatency);
                
                logger.info("Cross-service causation latency: {}ms from {} to {}", 
                           causationLatency.toMillis(), causingStep.getSource(), event.getSource());
                break;
            }
        }
    }
    
    /**
     * Detects cross-service communication patterns.
     */
    private void detectCrossServicePatterns(BaseEvent event, String topic) {
        String source = event.getSource();
        String eventType = event.getEventType();
        
        // Detect common patterns
        if (eventType.contains("Created") && source.contains("customer")) {
            logger.info("Detected customer creation pattern: {} from {}", eventType, source);
        } else if (eventType.contains("Order") && source.contains("order")) {
            logger.info("Detected order lifecycle pattern: {} from {}", eventType, source);
        } else if (eventType.contains("Notification") && source.contains("notification")) {
            logger.info("Detected notification pattern: {} from {}", eventType, source);
        }
    }
    
    /**
     * Logs significant events for monitoring.
     */
    private void logSignificantEvents(BaseEvent event, String topic) {
        String eventType = event.getEventType();
        
        // Log high-priority events
        if (eventType.contains("Error") || eventType.contains("Failed")) {
            logger.warn("Detected error event: {} from {} on topic {}", 
                       eventType, event.getSource(), topic);
        } else if (eventType.contains("Cancelled") || eventType.contains("Rejected")) {
            logger.info("Detected cancellation event: {} from {} on topic {}", 
                       eventType, event.getSource(), topic);
        }
    }
    
    /**
     * Calculates cross-service latency between related events.
     */
    private void calculateCrossServiceLatency(EventFlowTrace trace, BaseEvent currentEvent) {
        if (trace.getSteps().size() > 1) {
            EventFlowStep previousStep = trace.getSteps().get(trace.getSteps().size() - 2);
            
            if (!previousStep.getSource().equals(currentEvent.getSource())) {
                // This is a cross-service event
                Duration crossServiceLatency = Duration.between(
                    previousStep.getProcessedAt(), 
                    Instant.now()
                );
                
                Timer.builder("event.cross.service.latency")
                    .tag("from_service", previousStep.getSource())
                    .tag("to_service", currentEvent.getSource())
                    .tag("from_event", previousStep.getEventType())
                    .tag("to_event", currentEvent.getEventType())
                    .register(meterRegistry)
                    .record(crossServiceLatency);
                
                logger.info("Cross-service latency: {}ms from {} to {}", 
                           crossServiceLatency.toMillis(), 
                           previousStep.getSource(), 
                           currentEvent.getSource());
            }
        }
    }
    
    /**
     * Gets current topic from event metadata.
     */
    private String getCurrentTopic(BaseEvent event) {
        // Try to get topic from metadata, fallback to source-based naming
        Object topicObj = event.getMetadata().get("topic");
        String topic = topicObj != null ? topicObj.toString() : null;
        if (topic == null) {
            topic = event.getSource().replace("-service", "") + ".events";
        }
        return topic;
    }
    
    /**
     * Cleanup old correlation traces periodically.
     */
    public void cleanupOldTraces() {
        Instant cutoff = Instant.now().minus(Duration.ofHours(24));
        
        correlationTraces.entrySet().removeIf(entry -> {
            EventFlowTrace trace = entry.getValue();
            return trace.getLastActivity().isBefore(cutoff);
        });
        
        logger.debug("Cleaned up old correlation traces, remaining: {}", correlationTraces.size());
    }
    
    @Override
    protected String getConsumerGroup() {
        return CONSUMER_GROUP;
    }
    
    @Override
    protected boolean isDuplicateEvent(BaseEvent event) {
        return processedEventIds.contains(event.getEventId().toString());
    }
    
    @Override
    protected void markEventAsProcessed(BaseEvent event) {
        processedEventIds.add(event.getEventId().toString());
    }
    
    @Override
    protected void sendToDeadLetterQueue(String eventPayload, String topic, String errorType, String errorMessage) {
        dlqHandler.sendToDeadLetterQueue(topic, eventPayload, errorType, errorMessage)
            .whenComplete((result, ex) -> {
                if (ex != null) {
                    logger.error("Failed to send monitoring event to DLQ for topic: {}", topic, ex);
                } else {
                    logger.debug("Successfully sent monitoring event to DLQ for topic: {}", topic);
                }
            });
    }

    /**
     * Represents a trace of events for a correlation ID.
     */
    public static class EventFlowTrace {
        private final String correlationId;
        private final java.util.List<EventFlowStep> steps;
        private Instant lastActivity;

        public EventFlowTrace(String correlationId) {
            this.correlationId = correlationId;
            this.steps = new java.util.ArrayList<>();
            this.lastActivity = Instant.now();
        }

        public void addEvent(EventFlowStep step) {
            steps.add(step);
            lastActivity = Instant.now();
        }

        public EventFlowStep findEventById(String eventId) {
            return steps.stream()
                .filter(step -> step.getEventId().equals(eventId))
                .findFirst()
                .orElse(null);
        }

        // Getters
        public String getCorrelationId() { return correlationId; }
        public java.util.List<EventFlowStep> getSteps() { return steps; }
        public Instant getLastActivity() { return lastActivity; }
    }

    /**
     * Represents a single step in an event flow.
     */
    public static class EventFlowStep {
        private final String eventId;
        private final String eventType;
        private final String source;
        private final String topic;
        private final Instant processedAt;
        private final Instant createdAt;

        public EventFlowStep(String eventId, String eventType, String source, String topic,
                           Instant processedAt, Instant createdAt) {
            this.eventId = eventId;
            this.eventType = eventType;
            this.source = source;
            this.topic = topic;
            this.processedAt = processedAt;
            this.createdAt = createdAt;
        }

        // Getters
        public String getEventId() { return eventId; }
        public String getEventType() { return eventType; }
        public String getSource() { return source; }
        public String getTopic() { return topic; }
        public Instant getProcessedAt() { return processedAt; }
        public Instant getCreatedAt() { return createdAt; }
    }
}

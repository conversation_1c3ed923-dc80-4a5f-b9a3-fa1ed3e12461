package com.nttdata.ndvn.shared.events.order;

import com.nttdata.ndvn.shared.events.BaseEvent;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Shared event for when an order is cancelled.
 * This event is published by the Order Management SCS and consumed by other services.
 */
public class OrderCancelledEvent extends BaseEvent {
    
    @JsonProperty("orderId")
    private UUID orderId;
    
    @JsonProperty("orderNumber")
    private String orderNumber;
    
    @JsonProperty("customerId")
    private UUID customerId;
    
    @JsonProperty("customerEmail")
    private String customerEmail;
    
    @JsonProperty("customerName")
    private String customerName;
    
    @JsonProperty("userId")
    private UUID userId;
    
    @JsonProperty("cancelledAt")
    private LocalDateTime cancelledAt;
    
    @JsonProperty("cancelledBy")
    private String cancelledBy;
    
    @JsonProperty("cancellationReason")
    private String cancellationReason;
    
    @JsonProperty("refundAmount")
    private BigDecimal refundAmount;
    
    @JsonProperty("currency")
    private String currency;
    
    @JsonProperty("refundMethod")
    private String refundMethod;
    
    @JsonProperty("refundStatus")
    private String refundStatus;
    
    @JsonProperty("originalOrderDate")
    private LocalDateTime originalOrderDate;
    
    @JsonProperty("orderStatus")
    private String orderStatus;
    
    // Default constructor for Jackson
    public OrderCancelledEvent() {
        super();
    }
    
    public OrderCancelledEvent(UUID orderId, String orderNumber, UUID customerId, String customerEmail,
                             String customerName, UUID userId, LocalDateTime cancelledAt, 
                             String cancelledBy, String cancellationReason, BigDecimal refundAmount,
                             String currency, String refundMethod, String refundStatus,
                             LocalDateTime originalOrderDate, String orderStatus) {
        super("OrderCancelled", "1.0", "order-management-service");
        this.orderId = orderId;
        this.orderNumber = orderNumber;
        this.customerId = customerId;
        this.customerEmail = customerEmail;
        this.customerName = customerName;
        this.userId = userId;
        this.cancelledAt = cancelledAt;
        this.cancelledBy = cancelledBy;
        this.cancellationReason = cancellationReason;
        this.refundAmount = refundAmount;
        this.currency = currency;
        this.refundMethod = refundMethod;
        this.refundStatus = refundStatus;
        this.originalOrderDate = originalOrderDate;
        this.orderStatus = orderStatus;
        
        // Set aggregate context
        setAggregateContext(orderId.toString(), "Order", 1L);
    }
    
    // Getters and setters
    public UUID getOrderId() { return orderId; }
    public void setOrderId(UUID orderId) { this.orderId = orderId; }
    
    public String getOrderNumber() { return orderNumber; }
    public void setOrderNumber(String orderNumber) { this.orderNumber = orderNumber; }
    
    public UUID getCustomerId() { return customerId; }
    public void setCustomerId(UUID customerId) { this.customerId = customerId; }
    
    public String getCustomerEmail() { return customerEmail; }
    public void setCustomerEmail(String customerEmail) { this.customerEmail = customerEmail; }
    
    public String getCustomerName() { return customerName; }
    public void setCustomerName(String customerName) { this.customerName = customerName; }
    
    public UUID getUserId() { return userId; }
    public void setUserId(UUID userId) { this.userId = userId; }
    
    public LocalDateTime getCancelledAt() { return cancelledAt; }
    public void setCancelledAt(LocalDateTime cancelledAt) { this.cancelledAt = cancelledAt; }
    
    public String getCancelledBy() { return cancelledBy; }
    public void setCancelledBy(String cancelledBy) { this.cancelledBy = cancelledBy; }
    
    public String getCancellationReason() { return cancellationReason; }
    public void setCancellationReason(String cancellationReason) { this.cancellationReason = cancellationReason; }
    
    public BigDecimal getRefundAmount() { return refundAmount; }
    public void setRefundAmount(BigDecimal refundAmount) { this.refundAmount = refundAmount; }
    
    public String getCurrency() { return currency; }
    public void setCurrency(String currency) { this.currency = currency; }
    
    public String getRefundMethod() { return refundMethod; }
    public void setRefundMethod(String refundMethod) { this.refundMethod = refundMethod; }
    
    public String getRefundStatus() { return refundStatus; }
    public void setRefundStatus(String refundStatus) { this.refundStatus = refundStatus; }
    
    public LocalDateTime getOriginalOrderDate() { return originalOrderDate; }
    public void setOriginalOrderDate(LocalDateTime originalOrderDate) { this.originalOrderDate = originalOrderDate; }
    
    public String getOrderStatus() { return orderStatus; }
    public void setOrderStatus(String orderStatus) { this.orderStatus = orderStatus; }
}

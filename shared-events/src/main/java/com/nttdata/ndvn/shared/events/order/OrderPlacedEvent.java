package com.nttdata.ndvn.shared.events.order;

import com.nttdata.ndvn.shared.events.BaseEvent;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * Shared event for when an order is placed.
 * This event is published by the Order Management SCS and consumed by other services.
 */
public class OrderPlacedEvent extends BaseEvent {
    
    @JsonProperty("orderId")
    private UUID orderId;
    
    @JsonProperty("orderNumber")
    private String orderNumber;
    
    @JsonProperty("customerId")
    private UUID customerId;
    
    @JsonProperty("customerEmail")
    private String customerEmail;
    
    @JsonProperty("customerName")
    private String customerName;
    
    @JsonProperty("userId")
    private UUID userId;
    
    @JsonProperty("orderDate")
    private LocalDateTime orderDate;
    
    @JsonProperty("status")
    private String status;
    
    @JsonProperty("totalAmount")
    private BigDecimal totalAmount;
    
    @JsonProperty("currency")
    private String currency;
    
    @JsonProperty("itemCount")
    private Integer itemCount;
    
    @JsonProperty("items")
    private List<OrderItemData> items;
    
    @JsonProperty("shippingAddress")
    private AddressData shippingAddress;
    
    @JsonProperty("billingAddress")
    private AddressData billingAddress;
    
    // Default constructor for Jackson
    public OrderPlacedEvent() {
        super();
    }
    
    public OrderPlacedEvent(UUID orderId, String orderNumber, UUID customerId, String customerEmail, 
                           String customerName, UUID userId, LocalDateTime orderDate, String status,
                           BigDecimal totalAmount, String currency, Integer itemCount,
                           List<OrderItemData> items, AddressData shippingAddress, AddressData billingAddress) {
        super("OrderPlaced", "1.0", "order-management-service");
        this.orderId = orderId;
        this.orderNumber = orderNumber;
        this.customerId = customerId;
        this.customerEmail = customerEmail;
        this.customerName = customerName;
        this.userId = userId;
        this.orderDate = orderDate;
        this.status = status;
        this.totalAmount = totalAmount;
        this.currency = currency;
        this.itemCount = itemCount;
        this.items = items;
        this.shippingAddress = shippingAddress;
        this.billingAddress = billingAddress;
        
        // Set aggregate context
        setAggregateContext(orderId.toString(), "Order", 1L);
    }
    
    // Getters and setters
    public UUID getOrderId() { return orderId; }
    public void setOrderId(UUID orderId) { this.orderId = orderId; }
    
    public String getOrderNumber() { return orderNumber; }
    public void setOrderNumber(String orderNumber) { this.orderNumber = orderNumber; }
    
    public UUID getCustomerId() { return customerId; }
    public void setCustomerId(UUID customerId) { this.customerId = customerId; }
    
    public String getCustomerEmail() { return customerEmail; }
    public void setCustomerEmail(String customerEmail) { this.customerEmail = customerEmail; }
    
    public String getCustomerName() { return customerName; }
    public void setCustomerName(String customerName) { this.customerName = customerName; }
    
    public UUID getUserId() { return userId; }
    public void setUserId(UUID userId) { this.userId = userId; }
    
    public LocalDateTime getOrderDate() { return orderDate; }
    public void setOrderDate(LocalDateTime orderDate) { this.orderDate = orderDate; }
    
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
    
    public BigDecimal getTotalAmount() { return totalAmount; }
    public void setTotalAmount(BigDecimal totalAmount) { this.totalAmount = totalAmount; }
    
    public String getCurrency() { return currency; }
    public void setCurrency(String currency) { this.currency = currency; }
    
    public Integer getItemCount() { return itemCount; }
    public void setItemCount(Integer itemCount) { this.itemCount = itemCount; }
    
    public List<OrderItemData> getItems() { return items; }
    public void setItems(List<OrderItemData> items) { this.items = items; }
    
    public AddressData getShippingAddress() { return shippingAddress; }
    public void setShippingAddress(AddressData shippingAddress) { this.shippingAddress = shippingAddress; }
    
    public AddressData getBillingAddress() { return billingAddress; }
    public void setBillingAddress(AddressData billingAddress) { this.billingAddress = billingAddress; }
    
    /**
     * Order item data for the event.
     */
    public static class OrderItemData {
        @JsonProperty("productId")
        private UUID productId;
        
        @JsonProperty("sku")
        private String sku;
        
        @JsonProperty("productName")
        private String productName;
        
        @JsonProperty("quantity")
        private Integer quantity;
        
        @JsonProperty("unitPrice")
        private BigDecimal unitPrice;
        
        @JsonProperty("totalPrice")
        private BigDecimal totalPrice;
        
        // Default constructor
        public OrderItemData() {}
        
        public OrderItemData(UUID productId, String sku, String productName, Integer quantity, 
                           BigDecimal unitPrice, BigDecimal totalPrice) {
            this.productId = productId;
            this.sku = sku;
            this.productName = productName;
            this.quantity = quantity;
            this.unitPrice = unitPrice;
            this.totalPrice = totalPrice;
        }
        
        // Getters and setters
        public UUID getProductId() { return productId; }
        public void setProductId(UUID productId) { this.productId = productId; }
        
        public String getSku() { return sku; }
        public void setSku(String sku) { this.sku = sku; }
        
        public String getProductName() { return productName; }
        public void setProductName(String productName) { this.productName = productName; }
        
        public Integer getQuantity() { return quantity; }
        public void setQuantity(Integer quantity) { this.quantity = quantity; }
        
        public BigDecimal getUnitPrice() { return unitPrice; }
        public void setUnitPrice(BigDecimal unitPrice) { this.unitPrice = unitPrice; }
        
        public BigDecimal getTotalPrice() { return totalPrice; }
        public void setTotalPrice(BigDecimal totalPrice) { this.totalPrice = totalPrice; }
    }
    
    /**
     * Address data for the event.
     */
    public static class AddressData {
        @JsonProperty("street")
        private String street;
        
        @JsonProperty("city")
        private String city;
        
        @JsonProperty("state")
        private String state;
        
        @JsonProperty("postalCode")
        private String postalCode;
        
        @JsonProperty("country")
        private String country;
        
        // Default constructor
        public AddressData() {}
        
        public AddressData(String street, String city, String state, String postalCode, String country) {
            this.street = street;
            this.city = city;
            this.state = state;
            this.postalCode = postalCode;
            this.country = country;
        }
        
        // Getters and setters
        public String getStreet() { return street; }
        public void setStreet(String street) { this.street = street; }
        
        public String getCity() { return city; }
        public void setCity(String city) { this.city = city; }
        
        public String getState() { return state; }
        public void setState(String state) { this.state = state; }
        
        public String getPostalCode() { return postalCode; }
        public void setPostalCode(String postalCode) { this.postalCode = postalCode; }
        
        public String getCountry() { return country; }
        public void setCountry(String country) { this.country = country; }
    }
}

package com.nttdata.ndvn.shared.events.order;

import com.nttdata.ndvn.shared.events.BaseEvent;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Shared event for when an order is shipped.
 * This event is published by the Order Management SCS and consumed by other services.
 */
public class OrderShippedEvent extends BaseEvent {
    
    @JsonProperty("orderId")
    private UUID orderId;
    
    @JsonProperty("shipmentId")
    private UUID shipmentId;
    
    @JsonProperty("orderNumber")
    private String orderNumber;
    
    @JsonProperty("customerId")
    private UUID customerId;
    
    @JsonProperty("customerEmail")
    private String customerEmail;
    
    @JsonProperty("customerName")
    private String customerName;
    
    @JsonProperty("trackingNumber")
    private String trackingNumber;
    
    @JsonProperty("carrier")
    private String carrier;
    
    @JsonProperty("serviceType")
    private String serviceType;
    
    @JsonProperty("recipientName")
    private String recipientName;
    
    @JsonProperty("recipientAddress")
    private String recipientAddress;
    
    @JsonProperty("estimatedDeliveryDate")
    private LocalDateTime estimatedDeliveryDate;
    
    @JsonProperty("shippedAt")
    private LocalDateTime shippedAt;
    
    // Default constructor for Jackson
    public OrderShippedEvent() {
        super();
    }
    
    public OrderShippedEvent(UUID orderId, UUID shipmentId, String orderNumber, UUID customerId,
                           String customerEmail, String customerName, String trackingNumber, 
                           String carrier, String serviceType, String recipientName, 
                           String recipientAddress, LocalDateTime estimatedDeliveryDate,
                           LocalDateTime shippedAt) {
        super("OrderShipped", "1.0", "order-management-service");
        this.orderId = orderId;
        this.shipmentId = shipmentId;
        this.orderNumber = orderNumber;
        this.customerId = customerId;
        this.customerEmail = customerEmail;
        this.customerName = customerName;
        this.trackingNumber = trackingNumber;
        this.carrier = carrier;
        this.serviceType = serviceType;
        this.recipientName = recipientName;
        this.recipientAddress = recipientAddress;
        this.estimatedDeliveryDate = estimatedDeliveryDate;
        this.shippedAt = shippedAt;
        
        // Set aggregate context
        setAggregateContext(orderId.toString(), "Order", 1L);
    }
    
    // Getters and setters
    public UUID getOrderId() { return orderId; }
    public void setOrderId(UUID orderId) { this.orderId = orderId; }
    
    public UUID getShipmentId() { return shipmentId; }
    public void setShipmentId(UUID shipmentId) { this.shipmentId = shipmentId; }
    
    public String getOrderNumber() { return orderNumber; }
    public void setOrderNumber(String orderNumber) { this.orderNumber = orderNumber; }
    
    public UUID getCustomerId() { return customerId; }
    public void setCustomerId(UUID customerId) { this.customerId = customerId; }
    
    public String getCustomerEmail() { return customerEmail; }
    public void setCustomerEmail(String customerEmail) { this.customerEmail = customerEmail; }
    
    public String getCustomerName() { return customerName; }
    public void setCustomerName(String customerName) { this.customerName = customerName; }
    
    public String getTrackingNumber() { return trackingNumber; }
    public void setTrackingNumber(String trackingNumber) { this.trackingNumber = trackingNumber; }
    
    public String getCarrier() { return carrier; }
    public void setCarrier(String carrier) { this.carrier = carrier; }
    
    public String getServiceType() { return serviceType; }
    public void setServiceType(String serviceType) { this.serviceType = serviceType; }
    
    public String getRecipientName() { return recipientName; }
    public void setRecipientName(String recipientName) { this.recipientName = recipientName; }
    
    public String getRecipientAddress() { return recipientAddress; }
    public void setRecipientAddress(String recipientAddress) { this.recipientAddress = recipientAddress; }
    
    public LocalDateTime getEstimatedDeliveryDate() { return estimatedDeliveryDate; }
    public void setEstimatedDeliveryDate(LocalDateTime estimatedDeliveryDate) { this.estimatedDeliveryDate = estimatedDeliveryDate; }
    
    public LocalDateTime getShippedAt() { return shippedAt; }
    public void setShippedAt(LocalDateTime shippedAt) { this.shippedAt = shippedAt; }
}

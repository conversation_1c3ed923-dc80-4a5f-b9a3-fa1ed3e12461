package com.nttdata.ndvn.shared.events.publisher;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nttdata.ndvn.shared.events.BaseEvent;
import com.nttdata.ndvn.shared.events.util.CorrelationContext;
import com.nttdata.ndvn.shared.events.util.EventMetrics;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;

import java.util.concurrent.CompletableFuture;

/**
 * Abstract base class for event publishers.
 * 
 * Provides standardized event publishing functionality including:
 * - Correlation ID management
 * - Event serialization
 * - Metrics collection
 * - Error handling and logging
 * - Kafka publishing with callbacks
 */
public abstract class BaseEventPublisher {
    
    private static final Logger logger = LoggerFactory.getLogger(BaseEventPublisher.class);
    
    protected final KafkaTemplate<String, String> kafkaTemplate;
    protected final ObjectMapper objectMapper;
    protected final MeterRegistry meterRegistry;
    protected final EventMetrics eventMetrics;
    
    protected BaseEventPublisher(KafkaTemplate<String, String> kafkaTemplate,
                                ObjectMapper objectMapper,
                                MeterRegistry meterRegistry) {
        this.kafkaTemplate = kafkaTemplate;
        this.objectMapper = objectMapper;
        this.meterRegistry = meterRegistry;
        this.eventMetrics = new EventMetrics(meterRegistry);
    }
    
    /**
     * Publishes an event to the specified topic.
     * 
     * @param topic the Kafka topic
     * @param key the message key
     * @param event the event to publish
     * @return CompletableFuture for the send result
     */
    protected CompletableFuture<SendResult<String, String>> publishEvent(String topic, String key, BaseEvent event) {
        Timer.Sample sample = Timer.start(meterRegistry);
        
        try {
            // Enrich event with correlation context
            enrichEventWithContext(event);
            
            // Validate event
            validateEvent(event);
            
            // Serialize event
            String eventJson = serializeEvent(event);
            
            // Publish to Kafka
            CompletableFuture<SendResult<String, String>> future = kafkaTemplate.send(topic, key, eventJson);
            
            // Add completion callbacks
            future.whenComplete((result, ex) -> {
                sample.stop(Timer.builder("event.publish.duration")
                    .tag("topic", topic)
                    .tag("event_type", event.getEventType() != null ? event.getEventType() : "unknown")
                    .tag("source", event.getSource() != null ? event.getSource() : "unknown")
                    .register(meterRegistry));
                
                if (ex == null) {
                    handlePublishSuccess(topic, key, event, result);
                } else {
                    handlePublishFailure(topic, key, event, ex);
                }
            });
            
            return future;
            
        } catch (Exception e) {
            sample.stop(Timer.builder("event.publish.duration")
                .tag("topic", topic)
                .tag("event_type", event.getEventType() != null ? event.getEventType() : "unknown")
                .tag("source", event.getSource() != null ? event.getSource() : "unknown")
                .tag("error", "true")
                .register(meterRegistry));
            
            handlePublishFailure(topic, key, event, e);
            return CompletableFuture.failedFuture(e);
        }
    }
    
    /**
     * Enriches the event with correlation context from MDC and current thread.
     */
    private void enrichEventWithContext(BaseEvent event) {
        // Set correlation ID from MDC if not already set
        if (event.getCorrelationId() == null) {
            String correlationId = MDC.get("correlationId");
            if (correlationId == null) {
                correlationId = CorrelationContext.getCurrentCorrelationId();
            }
            if (correlationId == null) {
                correlationId = CorrelationContext.generateCorrelationId();
                CorrelationContext.setCorrelationId(correlationId);
            }
            event.setCorrelationId(correlationId);
        }
        
        // Set causation ID from current event ID in context
        if (event.getCausationId() == null) {
            String causationId = CorrelationContext.getCurrentEventId();
            if (causationId != null) {
                event.setCausationId(causationId);
            }
        }
        
        // Add trace information to metadata
        String traceId = MDC.get("traceId");
        if (traceId != null) {
            event.addMetadata("traceId", traceId);
        }
        
        String spanId = MDC.get("spanId");
        if (spanId != null) {
            event.addMetadata("spanId", spanId);
        }
        
        // Add publisher information
        event.addMetadata("publishedBy", getClass().getSimpleName());
        event.addMetadata("publishedAt", System.currentTimeMillis());
    }
    
    /**
     * Validates the event before publishing.
     */
    protected void validateEvent(BaseEvent event) {
        if (event.getEventType() == null || event.getEventType().trim().isEmpty()) {
            throw new IllegalArgumentException("Event type is required");
        }
        
        if (event.getEventVersion() == null || event.getEventVersion().trim().isEmpty()) {
            throw new IllegalArgumentException("Event version is required");
        }
        
        if (event.getSource() == null || event.getSource().trim().isEmpty()) {
            throw new IllegalArgumentException("Event source is required");
        }
        
        // Allow subclasses to add additional validation
        doAdditionalValidation(event);
    }
    
    /**
     * Template method for subclasses to add additional validation.
     */
    protected void doAdditionalValidation(BaseEvent event) {
        // Default implementation does nothing
    }
    
    /**
     * Serializes the event to JSON.
     */
    private String serializeEvent(BaseEvent event) throws JsonProcessingException {
        try {
            return objectMapper.writeValueAsString(event);
        } catch (JsonProcessingException e) {
            eventMetrics.incrementSerializationError(event.getEventType(), event.getSource());
            logger.error("Failed to serialize event: {} - {}", event.getEventType(), e.getMessage());
            throw e;
        }
    }
    
    /**
     * Handles successful event publishing.
     */
    private void handlePublishSuccess(String topic, String key, BaseEvent event, SendResult<String, String> result) {
        eventMetrics.incrementPublishSuccess(topic, event.getEventType(), event.getSource());
        
        logger.info("Successfully published event: {} to topic: {} with key: {} - Partition: {}, Offset: {}",
                   event.getEventType(), topic, key,
                   result.getRecordMetadata().partition(),
                   result.getRecordMetadata().offset());
        
        // Set current event ID in context for causation tracking
        CorrelationContext.setCurrentEventId(event.getEventId().toString());
    }
    
    /**
     * Handles failed event publishing.
     */
    private void handlePublishFailure(String topic, String key, BaseEvent event, Throwable ex) {
        eventMetrics.incrementPublishFailure(topic, event.getEventType(), event.getSource());
        
        logger.error("Failed to publish event: {} to topic: {} with key: {} - Error: {}",
                    event.getEventType(), topic, key, ex.getMessage(), ex);
    }
    
    /**
     * Gets the service name for this publisher.
     * Subclasses should override this to provide their service name.
     */
    protected abstract String getServiceName();
}

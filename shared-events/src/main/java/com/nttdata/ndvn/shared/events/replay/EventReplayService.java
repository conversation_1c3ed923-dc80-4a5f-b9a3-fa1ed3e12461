package com.nttdata.ndvn.shared.events.replay;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.micrometer.core.instrument.MeterRegistry;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.common.TopicPartition;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Service for replaying events from Kafka topics.
 * 
 * This service allows replaying events from a specific timestamp or offset,
 * which is useful for:
 * - Recovering from processing failures
 * - Rebuilding read models
 * - Testing and debugging
 * - Data migration scenarios
 */
@Service
public class EventReplayService {
    
    private static final Logger logger = LoggerFactory.getLogger(EventReplayService.class);
    
    private final ConsumerFactory<String, String> consumerFactory;
    private final ObjectMapper objectMapper;
    private final MeterRegistry meterRegistry;
    private final ExecutorService executorService;
    
    public EventReplayService(ConsumerFactory<String, String> consumerFactory,
                             ObjectMapper objectMapper,
                             MeterRegistry meterRegistry) {
        this.consumerFactory = consumerFactory;
        this.objectMapper = objectMapper;
        this.meterRegistry = meterRegistry;
        this.executorService = Executors.newCachedThreadPool();
    }
    
    /**
     * Replays events from a specific timestamp.
     * 
     * @param topic the Kafka topic to replay from
     * @param fromTimestamp the timestamp to start replay from
     * @param toTimestamp the timestamp to end replay at (optional)
     * @param eventProcessor the processor to handle replayed events
     * @return CompletableFuture that completes when replay is finished
     */
    public CompletableFuture<ReplayResult> replayFromTimestamp(String topic, 
                                                              LocalDateTime fromTimestamp,
                                                              LocalDateTime toTimestamp,
                                                              EventReplayProcessor eventProcessor) {
        
        return CompletableFuture.supplyAsync(() -> {
            logger.info("Starting event replay for topic: {} from timestamp: {}", topic, fromTimestamp);
            
            try (Consumer<String, String> consumer = consumerFactory.createConsumer()) {
                // Get topic partitions
                List<TopicPartition> partitions = getTopicPartitions(consumer, topic);
                consumer.assign(partitions);
                
                // Seek to timestamp
                Map<TopicPartition, Long> timestampOffsets = seekToTimestamp(consumer, partitions, fromTimestamp);
                
                // Calculate end offsets if toTimestamp is provided
                Map<TopicPartition, Long> endOffsets = null;
                if (toTimestamp != null) {
                    endOffsets = seekToTimestamp(consumer, partitions, toTimestamp);
                }
                
                return replayEvents(consumer, topic, timestampOffsets, endOffsets, eventProcessor);
                
            } catch (Exception e) {
                logger.error("Failed to replay events from topic: {}", topic, e);
                throw new RuntimeException("Event replay failed", e);
            }
        }, executorService);
    }
    
    /**
     * Replays events from a specific offset.
     * 
     * @param topic the Kafka topic to replay from
     * @param partition the partition to replay from
     * @param fromOffset the offset to start replay from
     * @param toOffset the offset to end replay at (optional)
     * @param eventProcessor the processor to handle replayed events
     * @return CompletableFuture that completes when replay is finished
     */
    public CompletableFuture<ReplayResult> replayFromOffset(String topic,
                                                           int partition,
                                                           long fromOffset,
                                                           Long toOffset,
                                                           EventReplayProcessor eventProcessor) {
        
        return CompletableFuture.supplyAsync(() -> {
            logger.info("Starting event replay for topic: {} partition: {} from offset: {}", 
                       topic, partition, fromOffset);
            
            try (Consumer<String, String> consumer = consumerFactory.createConsumer()) {
                TopicPartition topicPartition = new TopicPartition(topic, partition);
                consumer.assign(Collections.singletonList(topicPartition));
                consumer.seek(topicPartition, fromOffset);
                
                Map<TopicPartition, Long> startOffsets = Map.of(topicPartition, fromOffset);
                Map<TopicPartition, Long> endOffsets = toOffset != null ? 
                    Map.of(topicPartition, toOffset) : null;
                
                return replayEvents(consumer, topic, startOffsets, endOffsets, eventProcessor);
                
            } catch (Exception e) {
                logger.error("Failed to replay events from topic: {} partition: {}", topic, partition, e);
                throw new RuntimeException("Event replay failed", e);
            }
        }, executorService);
    }
    
    /**
     * Gets all partitions for a topic.
     */
    private List<TopicPartition> getTopicPartitions(Consumer<String, String> consumer, String topic) {
        return consumer.partitionsFor(topic).stream()
                .map(partitionInfo -> new TopicPartition(topic, partitionInfo.partition()))
                .toList();
    }
    
    /**
     * Seeks consumer to the specified timestamp for all partitions.
     */
    private Map<TopicPartition, Long> seekToTimestamp(Consumer<String, String> consumer,
                                                     List<TopicPartition> partitions,
                                                     LocalDateTime timestamp) {
        
        long epochMilli = timestamp.toInstant(ZoneOffset.UTC).toEpochMilli();
        
        // Create timestamp map for all partitions
        Map<TopicPartition, Long> timestampsToSearch = new HashMap<>();
        for (TopicPartition partition : partitions) {
            timestampsToSearch.put(partition, epochMilli);
        }
        
        // Get offsets for timestamps
        Map<TopicPartition, org.apache.kafka.clients.consumer.OffsetAndTimestamp> offsetsForTimes = 
            consumer.offsetsForTimes(timestampsToSearch);
        
        // Seek to the found offsets
        Map<TopicPartition, Long> resultOffsets = new HashMap<>();
        for (TopicPartition partition : partitions) {
            org.apache.kafka.clients.consumer.OffsetAndTimestamp offsetAndTimestamp = 
                offsetsForTimes.get(partition);
            
            if (offsetAndTimestamp != null) {
                consumer.seek(partition, offsetAndTimestamp.offset());
                resultOffsets.put(partition, offsetAndTimestamp.offset());
                logger.debug("Seeking partition {} to offset {} for timestamp {}", 
                           partition.partition(), offsetAndTimestamp.offset(), timestamp);
            } else {
                // If no offset found for timestamp, seek to end
                consumer.seekToEnd(Collections.singletonList(partition));
                long endOffset = consumer.position(partition);
                resultOffsets.put(partition, endOffset);
                logger.debug("No offset found for timestamp {}, seeking partition {} to end offset {}", 
                           timestamp, partition.partition(), endOffset);
            }
        }
        
        return resultOffsets;
    }
    
    /**
     * Replays events from the consumer.
     */
    private ReplayResult replayEvents(Consumer<String, String> consumer,
                                     String topic,
                                     Map<TopicPartition, Long> startOffsets,
                                     Map<TopicPartition, Long> endOffsets,
                                     EventReplayProcessor eventProcessor) {
        
        ReplayResult result = new ReplayResult(topic, startOffsets);
        Instant replayStartTime = Instant.now();
        
        try {
            boolean hasMoreEvents = true;
            
            while (hasMoreEvents) {
                ConsumerRecords<String, String> records = consumer.poll(Duration.ofSeconds(5));
                
                if (records.isEmpty()) {
                    // No more records available
                    hasMoreEvents = false;
                    continue;
                }
                
                for (ConsumerRecord<String, String> record : records) {
                    // Check if we've reached the end offset for this partition
                    if (endOffsets != null) {
                        TopicPartition partition = new TopicPartition(record.topic(), record.partition());
                        Long endOffset = endOffsets.get(partition);
                        if (endOffset != null && record.offset() >= endOffset) {
                            logger.debug("Reached end offset {} for partition {}", endOffset, partition.partition());
                            continue;
                        }
                    }
                    
                    try {
                        // Process the event
                        eventProcessor.processEvent(record);
                        result.incrementProcessedCount();
                        
                        if (result.getProcessedCount() % 1000 == 0) {
                            logger.info("Replayed {} events from topic: {}", result.getProcessedCount(), topic);
                        }
                        
                    } catch (Exception e) {
                        result.incrementErrorCount();
                        logger.error("Failed to process replayed event at offset {} partition {}", 
                                   record.offset(), record.partition(), e);
                        
                        // Continue processing other events
                    }
                }
                
                // Check if we've processed all partitions to their end offsets
                if (endOffsets != null && hasReachedAllEndOffsets(consumer, endOffsets)) {
                    hasMoreEvents = false;
                }
            }
            
        } catch (Exception e) {
            result.setError(e);
            logger.error("Error during event replay for topic: {}", topic, e);
        }
        
        result.setDuration(Duration.between(replayStartTime, Instant.now()));
        
        logger.info("Completed event replay for topic: {} - Processed: {}, Errors: {}, Duration: {}ms",
                   topic, result.getProcessedCount(), result.getErrorCount(), result.getDuration().toMillis());
        
        return result;
    }
    
    /**
     * Checks if all partitions have reached their end offsets.
     */
    private boolean hasReachedAllEndOffsets(Consumer<String, String> consumer, 
                                          Map<TopicPartition, Long> endOffsets) {
        for (Map.Entry<TopicPartition, Long> entry : endOffsets.entrySet()) {
            TopicPartition partition = entry.getKey();
            Long endOffset = entry.getValue();
            long currentPosition = consumer.position(partition);
            
            if (currentPosition < endOffset) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * Functional interface for processing replayed events.
     */
    @FunctionalInterface
    public interface EventReplayProcessor {
        void processEvent(ConsumerRecord<String, String> record) throws Exception;
    }
    
    /**
     * Result of an event replay operation.
     */
    public static class ReplayResult {
        private final String topic;
        private final Map<TopicPartition, Long> startOffsets;
        private long processedCount = 0;
        private long errorCount = 0;
        private Duration duration;
        private Exception error;
        
        public ReplayResult(String topic, Map<TopicPartition, Long> startOffsets) {
            this.topic = topic;
            this.startOffsets = new HashMap<>(startOffsets);
        }
        
        public void incrementProcessedCount() { processedCount++; }
        public void incrementErrorCount() { errorCount++; }
        
        // Getters and setters
        public String getTopic() { return topic; }
        public Map<TopicPartition, Long> getStartOffsets() { return startOffsets; }
        public long getProcessedCount() { return processedCount; }
        public long getErrorCount() { return errorCount; }
        public Duration getDuration() { return duration; }
        public void setDuration(Duration duration) { this.duration = duration; }
        public Exception getError() { return error; }
        public void setError(Exception error) { this.error = error; }
        
        public boolean isSuccessful() { return error == null; }
    }
}

package com.nttdata.ndvn.shared.events.schema;

import io.confluent.kafka.schemaregistry.client.CachedSchemaRegistryClient;
import io.confluent.kafka.schemaregistry.client.SchemaRegistryClient;
import io.confluent.kafka.serializers.AbstractKafkaSchemaSerDeConfig;
import io.confluent.kafka.serializers.KafkaAvroDeserializer;
import io.confluent.kafka.serializers.KafkaAvroSerializer;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.*;

import java.util.HashMap;
import java.util.Map;

/**
 * Configuration for Confluent Schema Registry integration.
 * 
 * This configuration sets up:
 * - Schema Registry client
 * - Avro serializers/deserializers
 * - Schema evolution policies
 * - Kafka producers/consumers with schema validation
 */
@Configuration
@ConditionalOnProperty(name = "app.events.schema-registry.enabled", havingValue = "true", matchIfMissing = false)
public class SchemaRegistryConfig {
    
    @Value("${app.events.schema-registry.url:http://localhost:8081}")
    private String schemaRegistryUrl;
    
    @Value("${app.events.schema-registry.basic-auth-user-info:}")
    private String basicAuthUserInfo;
    
    @Value("${app.events.schema-registry.basic-auth-credentials-source:USER_INFO}")
    private String basicAuthCredentialsSource;
    
    @Value("${spring.kafka.bootstrap-servers:localhost:9092}")
    private String bootstrapServers;
    
    @Value("${app.events.schema-registry.auto-register-schemas:true}")
    private boolean autoRegisterSchemas;
    
    @Value("${app.events.schema-registry.use-latest-version:true}")
    private boolean useLatestVersion;
    
    @Value("${app.events.schema-registry.cache-capacity:1000}")
    private int cacheCapacity;
    
    /**
     * Schema Registry client for managing schemas.
     */
    @Bean
    public SchemaRegistryClient schemaRegistryClient() {
        Map<String, Object> configs = new HashMap<>();
        configs.put(AbstractKafkaSchemaSerDeConfig.SCHEMA_REGISTRY_URL_CONFIG, schemaRegistryUrl);
        
        if (!basicAuthUserInfo.isEmpty()) {
            configs.put(AbstractKafkaSchemaSerDeConfig.BASIC_AUTH_CREDENTIALS_SOURCE, basicAuthCredentialsSource);
            configs.put(AbstractKafkaSchemaSerDeConfig.USER_INFO_CONFIG, basicAuthUserInfo);
        }
        
        return new CachedSchemaRegistryClient(schemaRegistryUrl, cacheCapacity, configs);
    }
    
    /**
     * Kafka producer factory with Avro serialization.
     */
    @Bean
    public ProducerFactory<String, Object> avroProducerFactory() {
        Map<String, Object> configProps = new HashMap<>();
        
        // Basic Kafka configuration
        configProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        configProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        configProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, KafkaAvroSerializer.class);
        
        // Schema Registry configuration
        configProps.put(AbstractKafkaSchemaSerDeConfig.SCHEMA_REGISTRY_URL_CONFIG, schemaRegistryUrl);
        configProps.put(AbstractKafkaSchemaSerDeConfig.AUTO_REGISTER_SCHEMAS, autoRegisterSchemas);
        configProps.put(AbstractKafkaSchemaSerDeConfig.USE_LATEST_VERSION, useLatestVersion);
        
        // Authentication if configured
        if (!basicAuthUserInfo.isEmpty()) {
            configProps.put(AbstractKafkaSchemaSerDeConfig.BASIC_AUTH_CREDENTIALS_SOURCE, basicAuthCredentialsSource);
            configProps.put(AbstractKafkaSchemaSerDeConfig.USER_INFO_CONFIG, basicAuthUserInfo);
        }
        
        // Producer optimization
        configProps.put(ProducerConfig.ACKS_CONFIG, "all");
        configProps.put(ProducerConfig.RETRIES_CONFIG, 3);
        configProps.put(ProducerConfig.BATCH_SIZE_CONFIG, 16384);
        configProps.put(ProducerConfig.LINGER_MS_CONFIG, 1);
        configProps.put(ProducerConfig.BUFFER_MEMORY_CONFIG, 33554432);
        configProps.put(ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG, true);
        
        return new DefaultKafkaProducerFactory<>(configProps);
    }
    
    /**
     * Kafka consumer factory with Avro deserialization.
     */
    @Bean
    public ConsumerFactory<String, Object> avroConsumerFactory() {
        Map<String, Object> configProps = new HashMap<>();
        
        // Basic Kafka configuration
        configProps.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        configProps.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        configProps.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, KafkaAvroDeserializer.class);
        
        // Schema Registry configuration
        configProps.put(AbstractKafkaSchemaSerDeConfig.SCHEMA_REGISTRY_URL_CONFIG, schemaRegistryUrl);
        configProps.put(AbstractKafkaSchemaSerDeConfig.AUTO_REGISTER_SCHEMAS, autoRegisterSchemas);
        configProps.put(AbstractKafkaSchemaSerDeConfig.USE_LATEST_VERSION, useLatestVersion);
        
        // Avro specific configuration
        configProps.put("specific.avro.reader", true);
        
        // Authentication if configured
        if (!basicAuthUserInfo.isEmpty()) {
            configProps.put(AbstractKafkaSchemaSerDeConfig.BASIC_AUTH_CREDENTIALS_SOURCE, basicAuthCredentialsSource);
            configProps.put(AbstractKafkaSchemaSerDeConfig.USER_INFO_CONFIG, basicAuthUserInfo);
        }
        
        // Consumer optimization
        configProps.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");
        configProps.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        configProps.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, 30000);
        configProps.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG, 3000);
        
        return new DefaultKafkaConsumerFactory<>(configProps);
    }
    
    /**
     * Kafka template for Avro message publishing.
     */
    @Bean
    public KafkaTemplate<String, Object> avroKafkaTemplate() {
        return new KafkaTemplate<>(avroProducerFactory());
    }
    
    /**
     * Schema evolution configuration properties.
     */
    @Bean
    public SchemaEvolutionConfig schemaEvolutionConfig() {
        return SchemaEvolutionConfig.builder()
            .compatibilityLevel(CompatibilityLevel.BACKWARD)
            .autoRegisterSchemas(autoRegisterSchemas)
            .useLatestVersion(useLatestVersion)
            .validateSchemas(true)
            .enableSchemaEvolution(true)
            .build();
    }
    
    /**
     * Schema evolution compatibility levels.
     */
    public enum CompatibilityLevel {
        BACKWARD("BACKWARD"),
        BACKWARD_TRANSITIVE("BACKWARD_TRANSITIVE"),
        FORWARD("FORWARD"),
        FORWARD_TRANSITIVE("FORWARD_TRANSITIVE"),
        FULL("FULL"),
        FULL_TRANSITIVE("FULL_TRANSITIVE"),
        NONE("NONE");
        
        private final String value;
        
        CompatibilityLevel(String value) {
            this.value = value;
        }
        
        public String getValue() {
            return value;
        }
    }
    
    /**
     * Schema evolution configuration.
     */
    public static class SchemaEvolutionConfig {
        private final CompatibilityLevel compatibilityLevel;
        private final boolean autoRegisterSchemas;
        private final boolean useLatestVersion;
        private final boolean validateSchemas;
        private final boolean enableSchemaEvolution;
        
        private SchemaEvolutionConfig(Builder builder) {
            this.compatibilityLevel = builder.compatibilityLevel;
            this.autoRegisterSchemas = builder.autoRegisterSchemas;
            this.useLatestVersion = builder.useLatestVersion;
            this.validateSchemas = builder.validateSchemas;
            this.enableSchemaEvolution = builder.enableSchemaEvolution;
        }
        
        public static Builder builder() {
            return new Builder();
        }
        
        // Getters
        public CompatibilityLevel getCompatibilityLevel() { return compatibilityLevel; }
        public boolean isAutoRegisterSchemas() { return autoRegisterSchemas; }
        public boolean isUseLatestVersion() { return useLatestVersion; }
        public boolean isValidateSchemas() { return validateSchemas; }
        public boolean isEnableSchemaEvolution() { return enableSchemaEvolution; }
        
        public static class Builder {
            private CompatibilityLevel compatibilityLevel = CompatibilityLevel.BACKWARD;
            private boolean autoRegisterSchemas = true;
            private boolean useLatestVersion = true;
            private boolean validateSchemas = true;
            private boolean enableSchemaEvolution = true;
            
            public Builder compatibilityLevel(CompatibilityLevel compatibilityLevel) {
                this.compatibilityLevel = compatibilityLevel;
                return this;
            }
            
            public Builder autoRegisterSchemas(boolean autoRegisterSchemas) {
                this.autoRegisterSchemas = autoRegisterSchemas;
                return this;
            }
            
            public Builder useLatestVersion(boolean useLatestVersion) {
                this.useLatestVersion = useLatestVersion;
                return this;
            }
            
            public Builder validateSchemas(boolean validateSchemas) {
                this.validateSchemas = validateSchemas;
                return this;
            }
            
            public Builder enableSchemaEvolution(boolean enableSchemaEvolution) {
                this.enableSchemaEvolution = enableSchemaEvolution;
                return this;
            }
            
            public SchemaEvolutionConfig build() {
                return new SchemaEvolutionConfig(this);
            }
        }
    }
}

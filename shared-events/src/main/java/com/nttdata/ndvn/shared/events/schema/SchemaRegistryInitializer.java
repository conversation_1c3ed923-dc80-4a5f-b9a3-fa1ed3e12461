package com.nttdata.ndvn.shared.events.schema;

import org.apache.avro.Schema;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * Initializes Schema Registry with predefined schemas on application startup.
 * 
 * This component automatically registers schemas found in the classpath
 * and sets up compatibility levels for different subjects.
 */
@Component
@ConditionalOnBean(SchemaManagementService.class)
@ConditionalOnProperty(name = "app.events.schema-registry.auto-initialize", havingValue = "true", matchIfMissing = true)
public class SchemaRegistryInitializer implements ApplicationRunner {
    
    private static final Logger logger = LoggerFactory.getLogger(SchemaRegistryInitializer.class);
    
    private final SchemaManagementService schemaManagementService;
    private final SchemaValidationPipeline validationPipeline;
    private final SchemaRegistryConfig.SchemaEvolutionConfig evolutionConfig;
    
    public SchemaRegistryInitializer(SchemaManagementService schemaManagementService,
                                   SchemaValidationPipeline validationPipeline,
                                   SchemaRegistryConfig.SchemaEvolutionConfig evolutionConfig) {
        this.schemaManagementService = schemaManagementService;
        this.validationPipeline = validationPipeline;
        this.evolutionConfig = evolutionConfig;
    }
    
    @Override
    public void run(ApplicationArguments args) throws Exception {
        logger.info("Initializing Schema Registry with predefined schemas...");
        
        try {
            // Check Schema Registry health
            if (!schemaManagementService.isHealthy()) {
                logger.warn("Schema Registry is not healthy, skipping initialization");
                return;
            }
            
            // Register predefined schemas
            registerPredefinedSchemas();
            
            // Set up compatibility levels
            setupCompatibilityLevels();
            
            logger.info("Schema Registry initialization completed successfully");
            
        } catch (Exception e) {
            logger.error("Failed to initialize Schema Registry", e);
            // Don't fail application startup, just log the error
        }
    }
    
    /**
     * Registers predefined schemas from classpath.
     */
    private void registerPredefinedSchemas() {
        try {
            PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
            Resource[] schemaResources = resolver.getResources("classpath*:avro/*.avsc");
            
            logger.info("Found {} schema files to register", schemaResources.length);
            
            for (Resource resource : schemaResources) {
                try {
                    registerSchemaFromResource(resource);
                } catch (Exception e) {
                    logger.error("Failed to register schema from resource: {}", resource.getFilename(), e);
                }
            }
            
        } catch (IOException e) {
            logger.error("Failed to scan for schema files", e);
        }
    }
    
    /**
     * Registers a schema from a resource file.
     */
    private void registerSchemaFromResource(Resource resource) throws IOException {
        String schemaContent = resource.getContentAsString(StandardCharsets.UTF_8);
        Schema schema = new Schema.Parser().parse(schemaContent);
        
        String subject = determineSubjectName(schema, resource.getFilename());
        
        logger.info("Registering schema: {} for subject: {}", schema.getName(), subject);
        
        // Validate schema before registration
        SchemaValidationResult validationResult = validationPipeline.validateSchema(subject, schema);
        
        if (!validationResult.isValid()) {
            logger.error("Schema validation failed for {}: {}", subject, validationResult.getSummary());
            logger.debug("Validation details:\n{}", validationResult.getDetailedReport());
            return;
        }
        
        if (validationResult.hasWarnings()) {
            logger.warn("Schema validation warnings for {}: {}", subject, validationResult.getSummary());
            logger.debug("Validation details:\n{}", validationResult.getDetailedReport());
        }
        
        // Register the schema
        try {
            int schemaId = schemaManagementService.registerSchema(subject, schema);
            logger.info("Successfully registered schema {} with ID: {}", subject, schemaId);
            
        } catch (Exception e) {
            logger.error("Failed to register schema for subject: {}", subject, e);
        }
    }
    
    /**
     * Determines the subject name for a schema.
     */
    private String determineSubjectName(Schema schema, String filename) {
        // Extract service name from namespace
        String namespace = schema.getNamespace();
        if (namespace != null && namespace.contains(".")) {
            String[] parts = namespace.split("\\.");
            if (parts.length >= 3) {
                String serviceName = parts[2]; // e.g., "user" from "com.nttdata.ndvn.user.events"
                return serviceName + ".events-value";
            }
        }
        
        // Fallback to filename-based naming
        String baseName = filename.replace(".avsc", "");
        if (baseName.endsWith("Event")) {
            String eventType = baseName.substring(0, baseName.length() - 5).toLowerCase();
            return eventType + ".events-value";
        }
        
        return baseName.toLowerCase() + ".events-value";
    }
    
    /**
     * Sets up compatibility levels for different subject patterns.
     */
    private void setupCompatibilityLevels() {
        Map<String, SchemaRegistryConfig.CompatibilityLevel> compatibilitySettings = getCompatibilitySettings();
        
        for (Map.Entry<String, SchemaRegistryConfig.CompatibilityLevel> entry : compatibilitySettings.entrySet()) {
            String subjectPattern = entry.getKey();
            SchemaRegistryConfig.CompatibilityLevel level = entry.getValue();
            
            try {
                // For now, we'll set compatibility for known subjects
                // In a more advanced setup, you might query existing subjects and apply patterns
                setCompatibilityForKnownSubjects(subjectPattern, level);
                
            } catch (Exception e) {
                logger.error("Failed to set compatibility level {} for pattern: {}", level, subjectPattern, e);
            }
        }
    }
    
    /**
     * Gets compatibility settings for different subject patterns.
     */
    private Map<String, SchemaRegistryConfig.CompatibilityLevel> getCompatibilitySettings() {
        Map<String, SchemaRegistryConfig.CompatibilityLevel> settings = new HashMap<>();
        
        // Event schemas should be backward compatible
        settings.put("*.events-value", SchemaRegistryConfig.CompatibilityLevel.BACKWARD);
        
        // Command schemas can be more strict
        settings.put("*.commands-value", SchemaRegistryConfig.CompatibilityLevel.FULL);
        
        // Snapshot schemas should be forward compatible
        settings.put("*.snapshots-value", SchemaRegistryConfig.CompatibilityLevel.FORWARD);
        
        return settings;
    }
    
    /**
     * Sets compatibility level for known subjects matching a pattern.
     */
    private void setCompatibilityForKnownSubjects(String pattern, SchemaRegistryConfig.CompatibilityLevel level) {
        try {
            // Get all subjects and filter by pattern
            var allSubjects = schemaManagementService.getAllSubjects();
            
            String regexPattern = pattern.replace("*", ".*");
            
            for (String subject : allSubjects) {
                if (subject.matches(regexPattern)) {
                    try {
                        schemaManagementService.setCompatibilityLevel(subject, level);
                        logger.info("Set compatibility level {} for subject: {}", level, subject);
                        
                    } catch (Exception e) {
                        logger.error("Failed to set compatibility level for subject: {}", subject, e);
                    }
                }
            }
            
        } catch (Exception e) {
            logger.error("Failed to get subjects for compatibility setup", e);
        }
    }
    
    /**
     * Validates Schema Registry setup.
     */
    public boolean validateSetup() {
        try {
            // Check basic connectivity
            if (!schemaManagementService.isHealthy()) {
                logger.error("Schema Registry health check failed");
                return false;
            }
            
            // Check if we can list subjects
            var subjects = schemaManagementService.getAllSubjects();
            logger.info("Schema Registry contains {} subjects", subjects.size());
            
            // Validate a few key schemas exist
            String[] expectedSubjects = {
                "user.events-value",
                "customer.events-value",
                "order.events-value"
            };
            
            for (String expectedSubject : expectedSubjects) {
                if (subjects.contains(expectedSubject)) {
                    logger.info("Found expected subject: {}", expectedSubject);
                } else {
                    logger.warn("Expected subject not found: {}", expectedSubject);
                }
            }
            
            return true;
            
        } catch (Exception e) {
            logger.error("Schema Registry setup validation failed", e);
            return false;
        }
    }
}

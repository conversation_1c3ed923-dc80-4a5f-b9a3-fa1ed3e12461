package com.nttdata.ndvn.shared.events.schema;

import org.apache.avro.Schema;
import org.apache.avro.SchemaValidationException;
import org.apache.avro.SchemaValidator;
import org.apache.avro.SchemaValidatorBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Pipeline for validating schema evolution and compatibility.
 * 
 * This pipeline provides comprehensive schema validation including:
 * - Syntax validation
 * - Compatibility validation
 * - Evolution rules validation
 * - Custom business rules validation
 */
@Component
@ConditionalOnBean(SchemaManagementService.class)
public class SchemaValidationPipeline {
    
    private static final Logger logger = LoggerFactory.getLogger(SchemaValidationPipeline.class);
    
    private final SchemaManagementService schemaManagementService;
    private final List<SchemaValidationRule> customValidationRules;
    
    public SchemaValidationPipeline(SchemaManagementService schemaManagementService) {
        this.schemaManagementService = schemaManagementService;
        this.customValidationRules = new ArrayList<>();
        
        // Initialize default validation rules
        initializeDefaultRules();
    }
    
    /**
     * Validates a schema against all validation rules.
     * 
     * @param subject the schema subject
     * @param newSchema the schema to validate
     * @return validation result
     */
    public SchemaValidationResult validateSchema(String subject, Schema newSchema) {
        logger.info("Starting schema validation for subject: {}", subject);
        
        SchemaValidationResult result = new SchemaValidationResult(subject, newSchema);
        
        try {
            // Step 1: Syntax validation
            validateSyntax(newSchema, result);
            
            // Step 2: Compatibility validation
            validateCompatibility(subject, newSchema, result);
            
            // Step 3: Evolution rules validation
            validateEvolutionRules(subject, newSchema, result);
            
            // Step 4: Custom business rules validation
            validateCustomRules(subject, newSchema, result);
            
            if (result.isValid()) {
                logger.info("Schema validation passed for subject: {}", subject);
            } else {
                logger.warn("Schema validation failed for subject: {} with {} errors", 
                           subject, result.getErrors().size());
            }
            
        } catch (Exception e) {
            logger.error("Schema validation failed with exception for subject: {}", subject, e);
            result.addError("Validation failed with exception: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * Validates schema syntax.
     */
    private void validateSyntax(Schema schema, SchemaValidationResult result) {
        try {
            // Basic syntax validation - if we can parse it, it's syntactically valid
            if (schema == null) {
                result.addError("Schema cannot be null");
                return;
            }
            
            // Validate required fields
            if (schema.getName() == null || schema.getName().trim().isEmpty()) {
                result.addError("Schema name is required");
            }
            
            if (schema.getNamespace() == null || schema.getNamespace().trim().isEmpty()) {
                result.addWarning("Schema namespace is recommended");
            }
            
            // Validate field names and types
            if (schema.getType() == Schema.Type.RECORD) {
                validateRecordFields(schema, result);
            }
            
            logger.debug("Syntax validation completed for schema: {}", schema.getName());
            
        } catch (Exception e) {
            result.addError("Syntax validation failed: " + e.getMessage());
        }
    }
    
    /**
     * Validates record fields.
     */
    private void validateRecordFields(Schema schema, SchemaValidationResult result) {
        for (Schema.Field field : schema.getFields()) {
            // Check field name conventions
            if (!isValidFieldName(field.name())) {
                result.addWarning("Field name '" + field.name() + "' does not follow naming conventions");
            }
            
            // Check for required documentation
            if (field.doc() == null || field.doc().trim().isEmpty()) {
                result.addWarning("Field '" + field.name() + "' should have documentation");
            }
            
            // Validate field types
            validateFieldType(field, result);
        }
    }
    
    /**
     * Validates field types.
     */
    private void validateFieldType(Schema.Field field, SchemaValidationResult result) {
        Schema fieldSchema = field.schema();
        
        // Check for union types with null (optional fields)
        if (fieldSchema.getType() == Schema.Type.UNION) {
            List<Schema> unionTypes = fieldSchema.getTypes();
            boolean hasNull = unionTypes.stream().anyMatch(s -> s.getType() == Schema.Type.NULL);
            
            if (hasNull && unionTypes.size() > 2) {
                result.addWarning("Field '" + field.name() + "' has complex union type with null - consider simplifying");
            }
        }
        
        // Check for deprecated types
        if (fieldSchema.getType() == Schema.Type.BYTES && field.name().toLowerCase().contains("string")) {
            result.addWarning("Field '" + field.name() + "' uses BYTES type but name suggests STRING");
        }
    }
    
    /**
     * Validates compatibility with existing schemas.
     */
    private void validateCompatibility(String subject, Schema newSchema, SchemaValidationResult result) {
        try {
            Optional<Schema> latestSchema = schemaManagementService.getLatestSchema(subject);
            
            if (latestSchema.isEmpty()) {
                logger.debug("No existing schema found for subject: {}, skipping compatibility check", subject);
                return;
            }
            
            // Use Avro's built-in compatibility validation
            SchemaValidator validator = new SchemaValidatorBuilder()
                .canReadStrategy()
                .validateLatest();
            
            try {
                validator.validate(newSchema, List.of(latestSchema.get()));
                logger.debug("Compatibility validation passed for subject: {}", subject);
                
            } catch (SchemaValidationException e) {
                result.addError("Schema compatibility validation failed: " + e.getMessage());
            }
            
        } catch (Exception e) {
            result.addError("Compatibility validation failed: " + e.getMessage());
        }
    }
    
    /**
     * Validates schema evolution rules.
     */
    private void validateEvolutionRules(String subject, Schema newSchema, SchemaValidationResult result) {
        try {
            Optional<Schema> latestSchema = schemaManagementService.getLatestSchema(subject);
            
            if (latestSchema.isEmpty()) {
                return;
            }
            
            Schema oldSchema = latestSchema.get();
            
            // Check for breaking changes
            validateBreakingChanges(oldSchema, newSchema, result);
            
            // Check for recommended practices
            validateEvolutionBestPractices(oldSchema, newSchema, result);
            
        } catch (Exception e) {
            result.addError("Evolution rules validation failed: " + e.getMessage());
        }
    }
    
    /**
     * Validates breaking changes.
     */
    private void validateBreakingChanges(Schema oldSchema, Schema newSchema, SchemaValidationResult result) {
        // Check for removed fields without defaults
        for (Schema.Field oldField : oldSchema.getFields()) {
            Schema.Field newField = newSchema.getField(oldField.name());
            if (newField == null && !oldField.hasDefaultValue()) {
                result.addError("Field '" + oldField.name() + "' was removed without default value");
            }
        }
        
        // Check for type changes
        for (Schema.Field newField : newSchema.getFields()) {
            Schema.Field oldField = oldSchema.getField(newField.name());
            if (oldField != null && !areTypesCompatible(oldField.schema(), newField.schema())) {
                result.addError("Field '" + newField.name() + "' type changed incompatibly");
            }
        }
    }
    
    /**
     * Validates evolution best practices.
     */
    private void validateEvolutionBestPractices(Schema oldSchema, Schema newSchema, SchemaValidationResult result) {
        // Check for new required fields
        for (Schema.Field newField : newSchema.getFields()) {
            Schema.Field oldField = oldSchema.getField(newField.name());
            if (oldField == null && !newField.hasDefaultValue()) {
                result.addWarning("New field '" + newField.name() + "' should have default value for backward compatibility");
            }
        }
        
        // Check version increment
        String oldVersion = oldSchema.getProp("version");
        String newVersion = newSchema.getProp("version");
        if (oldVersion != null && newVersion != null) {
            if (oldVersion.equals(newVersion)) {
                result.addWarning("Schema version should be incremented when making changes");
            }
        }
    }
    
    /**
     * Validates custom business rules.
     */
    private void validateCustomRules(String subject, Schema newSchema, SchemaValidationResult result) {
        for (SchemaValidationRule rule : customValidationRules) {
            try {
                rule.validate(subject, newSchema, result);
            } catch (Exception e) {
                result.addError("Custom validation rule failed: " + e.getMessage());
            }
        }
    }
    
    /**
     * Checks if field name follows conventions.
     */
    private boolean isValidFieldName(String fieldName) {
        // Check camelCase convention
        return fieldName.matches("^[a-z][a-zA-Z0-9]*$");
    }
    
    /**
     * Checks if two schema types are compatible.
     */
    private boolean areTypesCompatible(Schema oldType, Schema newType) {
        if (oldType.getType() == newType.getType()) {
            return true;
        }
        
        // Check for promotable types
        if (oldType.getType() == Schema.Type.INT && newType.getType() == Schema.Type.LONG) {
            return true;
        }
        
        if (oldType.getType() == Schema.Type.FLOAT && newType.getType() == Schema.Type.DOUBLE) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Initializes default validation rules.
     */
    private void initializeDefaultRules() {
        // Add event-specific validation rules
        addValidationRule(new EventSchemaValidationRule());
        addValidationRule(new NamingConventionValidationRule());
        addValidationRule(new DocumentationValidationRule());
    }
    
    /**
     * Adds a custom validation rule.
     */
    public void addValidationRule(SchemaValidationRule rule) {
        customValidationRules.add(rule);
        logger.debug("Added custom validation rule: {}", rule.getClass().getSimpleName());
    }
    
    /**
     * Interface for custom validation rules.
     */
    public interface SchemaValidationRule {
        void validate(String subject, Schema schema, SchemaValidationResult result);
    }
    
    /**
     * Validation rule for event schemas.
     */
    private static class EventSchemaValidationRule implements SchemaValidationRule {
        @Override
        public void validate(String subject, Schema schema, SchemaValidationResult result) {
            if (subject.contains("events")) {
                // Event schemas should have certain required fields
                if (schema.getField("eventId") == null) {
                    result.addError("Event schema must have 'eventId' field");
                }
                
                if (schema.getField("eventType") == null) {
                    result.addError("Event schema must have 'eventType' field");
                }
                
                if (schema.getField("timestamp") == null) {
                    result.addError("Event schema must have 'timestamp' field");
                }
            }
        }
    }
    
    /**
     * Validation rule for naming conventions.
     */
    private static class NamingConventionValidationRule implements SchemaValidationRule {
        @Override
        public void validate(String subject, Schema schema, SchemaValidationResult result) {
            // Schema name should end with "Event" for event schemas
            if (subject.contains("events") && !schema.getName().endsWith("Event")) {
                result.addWarning("Event schema name should end with 'Event'");
            }
            
            // Schema should have proper namespace
            if (schema.getNamespace() == null || !schema.getNamespace().startsWith("com.nttdata.ndvn")) {
                result.addWarning("Schema should use proper namespace starting with 'com.nttdata.ndvn'");
            }
        }
    }
    
    /**
     * Validation rule for documentation.
     */
    private static class DocumentationValidationRule implements SchemaValidationRule {
        @Override
        public void validate(String subject, Schema schema, SchemaValidationResult result) {
            if (schema.getDoc() == null || schema.getDoc().trim().isEmpty()) {
                result.addWarning("Schema should have documentation");
            }
            
            // Check field documentation coverage
            if (schema.getType() == Schema.Type.RECORD) {
                long undocumentedFields = schema.getFields().stream()
                    .filter(field -> field.doc() == null || field.doc().trim().isEmpty())
                    .count();
                
                if (undocumentedFields > 0) {
                    result.addWarning(undocumentedFields + " fields are missing documentation");
                }
            }
        }
    }
}

package com.nttdata.ndvn.shared.events.schema;

import org.apache.avro.Schema;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

/**
 * Result of schema validation containing errors, warnings, and metadata.
 */
public class SchemaValidationResult {
    
    private final String subject;
    private final Schema schema;
    private final Instant validationTime;
    private final List<ValidationMessage> errors;
    private final List<ValidationMessage> warnings;
    private final List<ValidationMessage> infos;
    
    public SchemaValidationResult(String subject, Schema schema) {
        this.subject = subject;
        this.schema = schema;
        this.validationTime = Instant.now();
        this.errors = new ArrayList<>();
        this.warnings = new ArrayList<>();
        this.infos = new ArrayList<>();
    }
    
    /**
     * Adds an error message.
     */
    public void addError(String message) {
        errors.add(new ValidationMessage(ValidationLevel.ERROR, message));
    }
    
    /**
     * Adds an error message with details.
     */
    public void addError(String message, String details) {
        errors.add(new ValidationMessage(ValidationLevel.ERROR, message, details));
    }
    
    /**
     * Adds a warning message.
     */
    public void addWarning(String message) {
        warnings.add(new ValidationMessage(ValidationLevel.WARNING, message));
    }
    
    /**
     * Adds a warning message with details.
     */
    public void addWarning(String message, String details) {
        warnings.add(new ValidationMessage(ValidationLevel.WARNING, message, details));
    }
    
    /**
     * Adds an info message.
     */
    public void addInfo(String message) {
        infos.add(new ValidationMessage(ValidationLevel.INFO, message));
    }
    
    /**
     * Adds an info message with details.
     */
    public void addInfo(String message, String details) {
        infos.add(new ValidationMessage(ValidationLevel.INFO, message, details));
    }
    
    /**
     * Checks if validation passed (no errors).
     */
    public boolean isValid() {
        return errors.isEmpty();
    }
    
    /**
     * Checks if there are any warnings.
     */
    public boolean hasWarnings() {
        return !warnings.isEmpty();
    }
    
    /**
     * Gets total message count.
     */
    public int getTotalMessageCount() {
        return errors.size() + warnings.size() + infos.size();
    }
    
    /**
     * Gets a summary of the validation result.
     */
    public String getSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("Schema validation for subject '").append(subject).append("': ");
        
        if (isValid()) {
            summary.append("PASSED");
        } else {
            summary.append("FAILED");
        }
        
        summary.append(" (").append(errors.size()).append(" errors, ")
               .append(warnings.size()).append(" warnings, ")
               .append(infos.size()).append(" infos)");
        
        return summary.toString();
    }
    
    /**
     * Gets detailed validation report.
     */
    public String getDetailedReport() {
        StringBuilder report = new StringBuilder();
        report.append("=== Schema Validation Report ===\n");
        report.append("Subject: ").append(subject).append("\n");
        report.append("Schema: ").append(schema.getName()).append("\n");
        report.append("Validation Time: ").append(validationTime).append("\n");
        report.append("Status: ").append(isValid() ? "PASSED" : "FAILED").append("\n\n");
        
        if (!errors.isEmpty()) {
            report.append("ERRORS (").append(errors.size()).append("):\n");
            for (int i = 0; i < errors.size(); i++) {
                report.append("  ").append(i + 1).append(". ").append(errors.get(i)).append("\n");
            }
            report.append("\n");
        }
        
        if (!warnings.isEmpty()) {
            report.append("WARNINGS (").append(warnings.size()).append("):\n");
            for (int i = 0; i < warnings.size(); i++) {
                report.append("  ").append(i + 1).append(". ").append(warnings.get(i)).append("\n");
            }
            report.append("\n");
        }
        
        if (!infos.isEmpty()) {
            report.append("INFO (").append(infos.size()).append("):\n");
            for (int i = 0; i < infos.size(); i++) {
                report.append("  ").append(i + 1).append(". ").append(infos.get(i)).append("\n");
            }
            report.append("\n");
        }
        
        return report.toString();
    }
    
    // Getters
    public String getSubject() { return subject; }
    public Schema getSchema() { return schema; }
    public Instant getValidationTime() { return validationTime; }
    public List<ValidationMessage> getErrors() { return new ArrayList<>(errors); }
    public List<ValidationMessage> getWarnings() { return new ArrayList<>(warnings); }
    public List<ValidationMessage> getInfos() { return new ArrayList<>(infos); }
    
    /**
     * Validation message with level and details.
     */
    public static class ValidationMessage {
        private final ValidationLevel level;
        private final String message;
        private final String details;
        private final Instant timestamp;
        
        public ValidationMessage(ValidationLevel level, String message) {
            this(level, message, null);
        }
        
        public ValidationMessage(ValidationLevel level, String message, String details) {
            this.level = level;
            this.message = message;
            this.details = details;
            this.timestamp = Instant.now();
        }
        
        public ValidationLevel getLevel() { return level; }
        public String getMessage() { return message; }
        public String getDetails() { return details; }
        public Instant getTimestamp() { return timestamp; }
        
        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append("[").append(level).append("] ").append(message);
            if (details != null && !details.trim().isEmpty()) {
                sb.append(" - ").append(details);
            }
            return sb.toString();
        }
    }
    
    /**
     * Validation message levels.
     */
    public enum ValidationLevel {
        ERROR,
        WARNING,
        INFO
    }
    
    @Override
    public String toString() {
        return getSummary();
    }
}

package com.nttdata.ndvn.shared.events.util;

import org.slf4j.MDC;

import java.util.UUID;

/**
 * Utility class for managing correlation context across threads and events.
 * 
 * This class provides thread-safe correlation ID management for distributed tracing
 * and event causation tracking.
 */
public final class CorrelationContext {
    
    private static final String CORRELATION_ID_KEY = "correlationId";
    private static final String EVENT_ID_KEY = "currentEventId";
    private static final String TRACE_ID_KEY = "traceId";
    private static final String SPAN_ID_KEY = "spanId";
    
    private static final ThreadLocal<String> correlationIdHolder = new ThreadLocal<>();
    private static final ThreadLocal<String> currentEventIdHolder = new ThreadLocal<>();
    
    private CorrelationContext() {
        // Utility class
    }
    
    /**
     * Generates a new correlation ID.
     */
    public static String generateCorrelationId() {
        return UUID.randomUUID().toString();
    }
    
    /**
     * Sets the correlation ID for the current thread.
     */
    public static void setCorrelationId(String correlationId) {
        correlationIdHolder.set(correlationId);
        MDC.put(CORRELATION_ID_KEY, correlationId);
    }
    
    /**
     * Gets the correlation ID for the current thread.
     */
    public static String getCurrentCorrelationId() {
        String correlationId = correlationIdHolder.get();
        if (correlationId == null) {
            correlationId = MDC.get(CORRELATION_ID_KEY);
        }
        return correlationId;
    }
    
    /**
     * Sets the current event ID for causation tracking.
     */
    public static void setCurrentEventId(String eventId) {
        currentEventIdHolder.set(eventId);
        MDC.put(EVENT_ID_KEY, eventId);
    }
    
    /**
     * Gets the current event ID for causation tracking.
     */
    public static String getCurrentEventId() {
        String eventId = currentEventIdHolder.get();
        if (eventId == null) {
            eventId = MDC.get(EVENT_ID_KEY);
        }
        return eventId;
    }
    
    /**
     * Sets the trace ID for distributed tracing.
     */
    public static void setTraceId(String traceId) {
        MDC.put(TRACE_ID_KEY, traceId);
    }
    
    /**
     * Gets the trace ID for distributed tracing.
     */
    public static String getTraceId() {
        return MDC.get(TRACE_ID_KEY);
    }
    
    /**
     * Sets the span ID for distributed tracing.
     */
    public static void setSpanId(String spanId) {
        MDC.put(SPAN_ID_KEY, spanId);
    }
    
    /**
     * Gets the span ID for distributed tracing.
     */
    public static String getSpanId() {
        return MDC.get(SPAN_ID_KEY);
    }
    
    /**
     * Initializes correlation context with a new correlation ID.
     */
    public static String initializeCorrelationContext() {
        String correlationId = generateCorrelationId();
        setCorrelationId(correlationId);
        return correlationId;
    }
    
    /**
     * Initializes correlation context with the provided correlation ID.
     */
    public static void initializeCorrelationContext(String correlationId) {
        setCorrelationId(correlationId);
    }
    
    /**
     * Clears all correlation context for the current thread.
     */
    public static void clearCorrelationContext() {
        correlationIdHolder.remove();
        currentEventIdHolder.remove();
        MDC.remove(CORRELATION_ID_KEY);
        MDC.remove(EVENT_ID_KEY);
        MDC.remove(TRACE_ID_KEY);
        MDC.remove(SPAN_ID_KEY);
    }
    
    /**
     * Executes a runnable with the specified correlation context.
     */
    public static void executeWithCorrelationId(String correlationId, Runnable runnable) {
        String previousCorrelationId = getCurrentCorrelationId();
        try {
            setCorrelationId(correlationId);
            runnable.run();
        } finally {
            if (previousCorrelationId != null) {
                setCorrelationId(previousCorrelationId);
            } else {
                clearCorrelationContext();
            }
        }
    }
    
    /**
     * Executes a runnable with a new correlation context.
     */
    public static String executeWithNewCorrelationId(Runnable runnable) {
        String correlationId = generateCorrelationId();
        executeWithCorrelationId(correlationId, runnable);
        return correlationId;
    }
    
    /**
     * Copies correlation context from MDC to thread local variables.
     */
    public static void copyFromMDC() {
        String correlationId = MDC.get(CORRELATION_ID_KEY);
        if (correlationId != null) {
            correlationIdHolder.set(correlationId);
        }
        
        String eventId = MDC.get(EVENT_ID_KEY);
        if (eventId != null) {
            currentEventIdHolder.set(eventId);
        }
    }
    
    /**
     * Copies correlation context to MDC from thread local variables.
     */
    public static void copyToMDC() {
        String correlationId = correlationIdHolder.get();
        if (correlationId != null) {
            MDC.put(CORRELATION_ID_KEY, correlationId);
        }
        
        String eventId = currentEventIdHolder.get();
        if (eventId != null) {
            MDC.put(EVENT_ID_KEY, eventId);
        }
    }
}

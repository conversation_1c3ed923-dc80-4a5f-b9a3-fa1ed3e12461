package com.nttdata.ndvn.shared.events.util;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;

/**
 * Utility class for collecting event publishing and consumption metrics.
 */
public class EventMetrics {
    
    private final MeterRegistry meterRegistry;
    
    public EventMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
    }
    
    /**
     * Increments the counter for successful event publishing.
     */
    public void incrementPublishSuccess(String topic, String eventType, String source) {
        Counter.builder("event.publish.success")
                .tag("topic", topic)
                .tag("event_type", eventType)
                .tag("source", source)
                .register(meterRegistry)
                .increment();
    }
    
    /**
     * Increments the counter for failed event publishing.
     */
    public void incrementPublishFailure(String topic, String eventType, String source) {
        Counter.builder("event.publish.failure")
                .tag("topic", topic)
                .tag("event_type", eventType)
                .tag("source", source)
                .register(meterRegistry)
                .increment();
    }
    
    /**
     * Increments the counter for event serialization errors.
     */
    public void incrementSerializationError(String eventType, String source) {
        Counter.builder("event.serialization.error")
                .tag("event_type", eventType)
                .tag("source", source)
                .register(meterRegistry)
                .increment();
    }
    
    /**
     * Increments the counter for successful event consumption.
     */
    public void incrementConsumeSuccess(String topic, String eventType, String consumerGroup) {
        Counter.builder("event.consume.success")
                .tag("topic", topic)
                .tag("event_type", eventType)
                .tag("consumer_group", consumerGroup)
                .register(meterRegistry)
                .increment();
    }
    
    /**
     * Increments the counter for failed event consumption.
     */
    public void incrementConsumeFailure(String topic, String eventType, String consumerGroup) {
        Counter.builder("event.consume.failure")
                .tag("topic", topic)
                .tag("event_type", eventType)
                .tag("consumer_group", consumerGroup)
                .register(meterRegistry)
                .increment();
    }
    
    /**
     * Increments the counter for event deserialization errors.
     */
    public void incrementDeserializationError(String topic, String consumerGroup) {
        Counter.builder("event.deserialization.error")
                .tag("topic", topic)
                .tag("consumer_group", consumerGroup)
                .register(meterRegistry)
                .increment();
    }
    
    /**
     * Records the time taken to process an event.
     */
    public Timer.Sample startProcessingTimer() {
        return Timer.start(meterRegistry);
    }
    
    /**
     * Stops the processing timer and records the duration.
     */
    public void stopProcessingTimer(Timer.Sample sample, String topic, String eventType, String consumerGroup) {
        sample.stop(Timer.builder("event.processing.duration")
                .tag("topic", topic)
                .tag("event_type", eventType)
                .tag("consumer_group", consumerGroup)
                .register(meterRegistry));
    }
    
    /**
     * Records the lag between event timestamp and processing time.
     */
    public void recordEventLag(String topic, String eventType, String consumerGroup, long lagMillis) {
        Timer.builder("event.processing.lag")
                .tag("topic", topic)
                .tag("event_type", eventType)
                .tag("consumer_group", consumerGroup)
                .register(meterRegistry)
                .record(java.time.Duration.ofMillis(lagMillis));
    }
}

package com.nttdata.ndvn.shared.events.validation;

import com.nttdata.ndvn.shared.events.BaseEvent;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.stream.Collectors;

/**
 * Validator for event schemas and content.
 * 
 * This component provides validation for events before they are published,
 * ensuring data integrity and schema compliance.
 */
@Component
public class EventSchemaValidator {
    
    private static final Logger logger = LoggerFactory.getLogger(EventSchemaValidator.class);
    
    private final Validator validator;
    
    public EventSchemaValidator(Validator validator) {
        this.validator = validator;
    }
    
    /**
     * Validates an event using Bean Validation annotations.
     * 
     * @param event the event to validate
     * @throws EventValidationException if validation fails
     */
    public void validateEvent(BaseEvent event) {
        if (event == null) {
            throw new EventValidationException("Event cannot be null");
        }
        
        Set<ConstraintViolation<BaseEvent>> violations = validator.validate(event);
        
        if (!violations.isEmpty()) {
            String errorMessage = violations.stream()
                    .map(violation -> violation.getPropertyPath() + ": " + violation.getMessage())
                    .collect(Collectors.joining(", "));
            
            logger.error("Event validation failed for {}: {}", event.getEventType(), errorMessage);
            throw new EventValidationException("Event validation failed: " + errorMessage);
        }
        
        // Additional custom validation
        validateEventVersion(event);
        validateEventSource(event);
    }
    
    /**
     * Validates event version format.
     */
    private void validateEventVersion(BaseEvent event) {
        String version = event.getEventVersion();
        if (version != null && !version.matches("\\d+\\.\\d+(\\.\\d+)?")) {
            throw new EventValidationException(
                "Event version must follow semantic versioning format (e.g., 1.0, 1.0.0): " + version);
        }
    }
    
    /**
     * Validates event source format.
     */
    private void validateEventSource(BaseEvent event) {
        String source = event.getSource();
        if (source != null && !source.matches("[a-z0-9-]+")) {
            throw new EventValidationException(
                "Event source must contain only lowercase letters, numbers, and hyphens: " + source);
        }
    }
    
    /**
     * Validates event type format.
     */
    public void validateEventType(String eventType) {
        if (eventType == null || eventType.trim().isEmpty()) {
            throw new EventValidationException("Event type cannot be null or empty");
        }
        
        if (!eventType.matches("[A-Z][a-zA-Z0-9]*")) {
            throw new EventValidationException(
                "Event type must start with uppercase letter and contain only alphanumeric characters: " + eventType);
        }
    }
    
    /**
     * Validates correlation ID format.
     */
    public void validateCorrelationId(String correlationId) {
        if (correlationId != null && !correlationId.matches("[a-fA-F0-9-]{36}")) {
            throw new EventValidationException(
                "Correlation ID must be a valid UUID format: " + correlationId);
        }
    }
    
    /**
     * Exception thrown when event validation fails.
     */
    public static class EventValidationException extends RuntimeException {
        public EventValidationException(String message) {
            super(message);
        }
        
        public EventValidationException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}

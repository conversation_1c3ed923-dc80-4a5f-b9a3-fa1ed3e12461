{"type": "record", "name": "BaseEvent", "namespace": "com.nttdata.ndvn.shared.events.avro", "doc": "Base event schema containing common fields for all events in the system", "fields": [{"name": "eventId", "type": "string", "doc": "Unique identifier for this event instance"}, {"name": "eventType", "type": "string", "doc": "Type of the event (e.g., UserCreated, OrderPlaced)"}, {"name": "version", "type": "string", "default": "1.0", "doc": "Version of the event schema"}, {"name": "source", "type": "string", "doc": "Source service that published this event"}, {"name": "timestamp", "type": "long", "logicalType": "timestamp-millis", "doc": "Timestamp when the event was created (milliseconds since epoch)"}, {"name": "correlationId", "type": ["null", "string"], "default": null, "doc": "Correlation ID for tracking related events across services"}, {"name": "causationId", "type": ["null", "string"], "default": null, "doc": "ID of the event that caused this event (for causation tracking)"}, {"name": "aggregateId", "type": ["null", "string"], "default": null, "doc": "ID of the aggregate that this event relates to"}, {"name": "aggregateType", "type": ["null", "string"], "default": null, "doc": "Type of the aggregate (e.g., User, Customer, Order)"}, {"name": "aggregateVersion", "type": ["null", "long"], "default": null, "doc": "Version of the aggregate when this event was created"}, {"name": "metadata", "type": {"type": "map", "values": "string"}, "default": {}, "doc": "Additional metadata as key-value pairs"}]}
package com.nttdata.ndvn.shared.events.publisher;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.nttdata.ndvn.shared.events.BaseEvent;
import com.nttdata.ndvn.shared.events.util.CorrelationContext;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;

import java.util.concurrent.CompletableFuture;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

/**
 * Unit tests for BaseEventPublisher.
 */
@ExtendWith(MockitoExtension.class)
class BaseEventPublisherTest {
    
    @Mock
    private KafkaTemplate<String, String> kafkaTemplate;
    
    @Mock
    private SendResult<String, String> sendResult;
    
    private ObjectMapper objectMapper;
    private MeterRegistry meterRegistry;
    private TestEventPublisher eventPublisher;
    
    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        objectMapper.enable(SerializationFeature.WRITE_ENUMS_USING_TO_STRING);
        meterRegistry = new SimpleMeterRegistry();
        eventPublisher = new TestEventPublisher(kafkaTemplate, objectMapper, meterRegistry);

        // Clear correlation context
        CorrelationContext.clearCorrelationContext();
    }
    
    @Test
    void shouldPublishEventSuccessfully() {
        // Given
        TestEvent event = new TestEvent("test-data");
        CompletableFuture<SendResult<String, String>> future = CompletableFuture.completedFuture(sendResult);
        
        lenient().when(kafkaTemplate.send(eq("test.topic"), eq("test-key"), any(String.class)))
                .thenReturn(future);
        
        // When
        CompletableFuture<SendResult<String, String>> result = eventPublisher.publishTestEvent("test-key", event);
        
        // Then
        assertThat(result).isCompleted();
        assertThat(event.getCorrelationId()).isNotNull();
        assertThat(event.getEventId()).isNotNull();
        assertThat(event.getTimestamp()).isNotNull();
    }
    
    @Test
    void shouldEnrichEventWithCorrelationContext() {
        // Given
        String correlationId = "test-correlation-id";
        CorrelationContext.setCorrelationId(correlationId);
        
        TestEvent event = new TestEvent("test-data");
        CompletableFuture<SendResult<String, String>> future = CompletableFuture.completedFuture(sendResult);
        
        lenient().when(kafkaTemplate.send(eq("test.topic"), eq("test-key"), any(String.class)))
                .thenReturn(future);
        
        // When
        eventPublisher.publishTestEvent("test-key", event);
        
        // Then
        assertThat(event.getCorrelationId()).isEqualTo(correlationId);
    }
    
    @Test
    void shouldValidateEventBeforePublishing() {
        // Given
        TestEvent event = new TestEvent("test-data");
        event.setEventType(null); // Invalid event type
        
        // When & Then
        assertThatThrownBy(() -> eventPublisher.publishTestEvent("test-key", event))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("Event type is required");
    }
    
    @Test
    void shouldGenerateCorrelationIdIfNotPresent() {
        // Given
        TestEvent event = new TestEvent("test-data");
        CompletableFuture<SendResult<String, String>> future = CompletableFuture.completedFuture(sendResult);
        
        lenient().when(kafkaTemplate.send(eq("test.topic"), eq("test-key"), any(String.class)))
                .thenReturn(future);
        
        // When
        eventPublisher.publishTestEvent("test-key", event);
        
        // Then
        assertThat(event.getCorrelationId()).isNotNull();
        assertThat(CorrelationContext.getCurrentCorrelationId()).isEqualTo(event.getCorrelationId());
    }
    
    /**
     * Test implementation of BaseEventPublisher.
     */
    private static class TestEventPublisher extends BaseEventPublisher {
        
        public TestEventPublisher(KafkaTemplate<String, String> kafkaTemplate,
                                 ObjectMapper objectMapper,
                                 MeterRegistry meterRegistry) {
            super(kafkaTemplate, objectMapper, meterRegistry);
        }
        
        public CompletableFuture<SendResult<String, String>> publishTestEvent(String key, TestEvent event) {
            return publishEvent("test.topic", key, event);
        }
        
        @Override
        protected String getServiceName() {
            return "test-service";
        }
    }
    
    /**
     * Test event class.
     */
    private static class TestEvent extends BaseEvent {
        
        private String testData;
        
        public TestEvent() {
            super();
        }
        
        public TestEvent(String testData) {
            super("TestEvent", "1.0", "test-service");
            this.testData = testData;
        }
        
        public String getTestData() { return testData; }
        public void setTestData(String testData) { this.testData = testData; }
    }
}

package com.nttdata.ndvn.shared.events.schema;

import io.confluent.kafka.schemaregistry.client.SchemaRegistryClient;
import org.apache.avro.Schema;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

/**
 * Unit tests for SchemaValidationPipeline.
 */
@ExtendWith(MockitoExtension.class)
class SchemaValidationPipelineTest {
    
    @Mock
    private SchemaRegistryClient schemaRegistryClient;
    
    @Mock
    private SchemaRegistryConfig.SchemaEvolutionConfig evolutionConfig;
    
    private SchemaManagementService schemaManagementService;
    private SchemaValidationPipeline validationPipeline;
    
    @BeforeEach
    void setUp() {
        schemaManagementService = new SchemaManagementService(schemaRegistryClient, evolutionConfig);
        validationPipeline = new SchemaValidationPipeline(schemaManagementService);
    }
    
    @Test
    void shouldPassValidationForValidEventSchema() {
        // Given
        String schemaJson = """
            {
              "type": "record",
              "name": "UserCreatedEvent",
              "namespace": "com.nttdata.ndvn.user.events",
              "doc": "Event published when a user is created",
              "fields": [
                {
                  "name": "eventId",
                  "type": "string",
                  "doc": "Unique event identifier"
                },
                {
                  "name": "eventType",
                  "type": "string",
                  "doc": "Type of the event"
                },
                {
                  "name": "timestamp",
                  "type": "long",
                  "logicalType": "timestamp-millis",
                  "doc": "Event timestamp"
                },
                {
                  "name": "userId",
                  "type": "string",
                  "doc": "User identifier"
                },
                {
                  "name": "username",
                  "type": "string",
                  "doc": "Username"
                },
                {
                  "name": "email",
                  "type": "string",
                  "doc": "User email"
                }
              ]
            }
            """;
        
        Schema schema = new Schema.Parser().parse(schemaJson);
        String subject = "user.events-value";
        
        when(evolutionConfig.isEnableSchemaEvolution()).thenReturn(true);
        
        // When
        SchemaValidationResult result = validationPipeline.validateSchema(subject, schema);
        
        // Then
        assertThat(result.isValid()).isTrue();
        assertThat(result.getErrors()).isEmpty();
        assertThat(result.getSummary()).contains("PASSED");
    }
    
    @Test
    void shouldFailValidationForSchemaWithoutRequiredEventFields() {
        // Given
        String schemaJson = """
            {
              "type": "record",
              "name": "InvalidEvent",
              "namespace": "com.nttdata.ndvn.test",
              "fields": [
                {
                  "name": "someField",
                  "type": "string"
                }
              ]
            }
            """;
        
        Schema schema = new Schema.Parser().parse(schemaJson);
        String subject = "test.events-value";
        
        when(evolutionConfig.isEnableSchemaEvolution()).thenReturn(true);
        
        // When
        SchemaValidationResult result = validationPipeline.validateSchema(subject, schema);
        
        // Then
        assertThat(result.isValid()).isFalse();
        assertThat(result.getErrors()).hasSize(3); // Missing eventId, eventType, timestamp
        assertThat(result.getSummary()).contains("FAILED");
        
        // Check specific error messages
        assertThat(result.getErrors().stream().map(msg -> msg.getMessage()))
            .contains("Event schema must have 'eventId' field")
            .contains("Event schema must have 'eventType' field")
            .contains("Event schema must have 'timestamp' field");
    }
    
    @Test
    void shouldGenerateWarningsForMissingDocumentation() {
        // Given
        String schemaJson = """
            {
              "type": "record",
              "name": "UndocumentedEvent",
              "namespace": "com.nttdata.ndvn.test",
              "fields": [
                {
                  "name": "eventId",
                  "type": "string"
                },
                {
                  "name": "eventType",
                  "type": "string"
                },
                {
                  "name": "timestamp",
                  "type": "long"
                },
                {
                  "name": "undocumentedField",
                  "type": "string"
                }
              ]
            }
            """;
        
        Schema schema = new Schema.Parser().parse(schemaJson);
        String subject = "test.events-value";
        
        when(evolutionConfig.isEnableSchemaEvolution()).thenReturn(true);
        
        // When
        SchemaValidationResult result = validationPipeline.validateSchema(subject, schema);
        
        // Then
        assertThat(result.isValid()).isTrue(); // No errors, just warnings
        assertThat(result.hasWarnings()).isTrue();
        
        // Check for documentation warnings
        assertThat(result.getWarnings().stream().map(msg -> msg.getMessage()))
            .anyMatch(msg -> msg.contains("should have documentation"))
            .anyMatch(msg -> msg.contains("fields are missing documentation"));
    }
    
    @Test
    void shouldValidateNamingConventions() {
        // Given
        String schemaJson = """
            {
              "type": "record",
              "name": "BadNaming",
              "namespace": "com.example.wrong",
              "fields": [
                {
                  "name": "eventId",
                  "type": "string"
                },
                {
                  "name": "eventType",
                  "type": "string"
                },
                {
                  "name": "timestamp",
                  "type": "long"
                },
                {
                  "name": "BadFieldName",
                  "type": "string"
                }
              ]
            }
            """;
        
        Schema schema = new Schema.Parser().parse(schemaJson);
        String subject = "test.events-value";
        
        when(evolutionConfig.isEnableSchemaEvolution()).thenReturn(true);
        
        // When
        SchemaValidationResult result = validationPipeline.validateSchema(subject, schema);
        
        // Then
        assertThat(result.isValid()).isTrue(); // Naming issues are warnings, not errors
        assertThat(result.hasWarnings()).isTrue();
        
        // Check for naming convention warnings
        assertThat(result.getWarnings().stream().map(msg -> msg.getMessage()))
            .anyMatch(msg -> msg.contains("should end with 'Event'"))
            .anyMatch(msg -> msg.contains("should use proper namespace"))
            .anyMatch(msg -> msg.contains("does not follow naming conventions"));
    }
    
    @Test
    void shouldHandleNullSchema() {
        // Given
        String subject = "test.events-value";
        
        when(evolutionConfig.isEnableSchemaEvolution()).thenReturn(true);
        
        // When
        SchemaValidationResult result = validationPipeline.validateSchema(subject, null);
        
        // Then
        assertThat(result.isValid()).isFalse();
        assertThat(result.getErrors()).hasSize(1);
        assertThat(result.getErrors().get(0).getMessage()).isEqualTo("Schema cannot be null");
    }
    
    @Test
    void shouldValidateFieldTypes() {
        // Given
        String schemaJson = """
            {
              "type": "record",
              "name": "TypeTestEvent",
              "namespace": "com.nttdata.ndvn.test",
              "fields": [
                {
                  "name": "eventId",
                  "type": "string"
                },
                {
                  "name": "eventType",
                  "type": "string"
                },
                {
                  "name": "timestamp",
                  "type": "long"
                },
                {
                  "name": "stringField",
                  "type": "bytes"
                },
                {
                  "name": "complexUnion",
                  "type": ["null", "string", "int", "boolean"]
                }
              ]
            }
            """;
        
        Schema schema = new Schema.Parser().parse(schemaJson);
        String subject = "test.events-value";
        
        when(evolutionConfig.isEnableSchemaEvolution()).thenReturn(true);
        
        // When
        SchemaValidationResult result = validationPipeline.validateSchema(subject, schema);
        
        // Then
        assertThat(result.isValid()).isTrue();
        assertThat(result.hasWarnings()).isTrue();
        
        // Check for type-related warnings
        assertThat(result.getWarnings().stream().map(msg -> msg.getMessage()))
            .anyMatch(msg -> msg.contains("uses BYTES type but name suggests STRING"))
            .anyMatch(msg -> msg.contains("complex union type with null"));
    }
    
    @Test
    void shouldGenerateDetailedReport() {
        // Given
        String schemaJson = """
            {
              "type": "record",
              "name": "TestEvent",
              "fields": [
                {
                  "name": "eventId",
                  "type": "string"
                }
              ]
            }
            """;
        
        Schema schema = new Schema.Parser().parse(schemaJson);
        String subject = "test.events-value";
        
        when(evolutionConfig.isEnableSchemaEvolution()).thenReturn(true);
        
        // When
        SchemaValidationResult result = validationPipeline.validateSchema(subject, schema);
        
        // Then
        String detailedReport = result.getDetailedReport();
        assertThat(detailedReport).contains("Schema Validation Report");
        assertThat(detailedReport).contains("Subject: " + subject);
        assertThat(detailedReport).contains("Schema: TestEvent");
        assertThat(detailedReport).contains("Status:");
        
        if (result.hasWarnings()) {
            assertThat(detailedReport).contains("WARNINGS");
        }
        
        if (!result.isValid()) {
            assertThat(detailedReport).contains("ERRORS");
        }
    }
}

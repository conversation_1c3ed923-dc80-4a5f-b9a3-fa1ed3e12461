# Shared Infrastructure Framework

## Overview

The Shared Infrastructure Framework provides common service-to-service communication components for all SCS services in the NDVN Terasoluna Base platform. This module implements Phase 4.2.1 requirements for robust, scalable inter-service communication.

## Features

### Core Components

- **BaseApiClient**: Abstract base class for service-to-service API clients
- **ServiceDiscoveryClient**: Dynamic service discovery using Consul
- **JWT Token Propagation**: Automatic authentication token forwarding
- **Resilience Patterns**: Circuit breakers, retries, timeouts, bulkheads
- **API Versioning**: Version negotiation and backward compatibility
- **Load Balancing**: Client-side load balancing with health checks
- **Monitoring**: Comprehensive metrics and observability

### Technology Stack

- **Spring Boot 3.4.1**: Core framework
- **Spring Cloud 2024.0.0**: Service discovery and load balancing
- **Resilience4j 2.2.0**: Circuit breaker and resilience patterns
- **Spring Security**: JWT token handling and propagation
- **Micrometer**: Metrics and monitoring
- **Consul**: Service discovery backend

## Usage

### 1. Add Dependency

Add the shared-infrastructure dependency to your service's `build.gradle`:

```gradle
dependencies {
    implementation project(':shared-infrastructure')
}
```

### 2. Create Service Client

Extend `BaseApiClient` to create service-specific clients:

```java
@Component
public class CustomerServiceClient extends BaseApiClient {
    
    public CustomerServiceClient(ServiceDiscoveryClient serviceDiscovery,
                               WebClient.Builder webClientBuilder) {
        super("customer-management-service", serviceDiscovery, webClientBuilder);
    }
    
    @CircuitBreaker(name = "customer-service")
    @Retry(name = "customer-service")
    public CompletableFuture<Customer> getCustomer(String customerId) {
        return get("/api/v1/customers/{id}", Customer.class, customerId);
    }
}
```

### 3. Configure Resilience

Configure circuit breakers and retry policies in `application.yml`:

```yaml
resilience4j:
  circuitbreaker:
    instances:
      customer-service:
        failure-rate-threshold: 50
        wait-duration-in-open-state: 30s
        sliding-window-size: 10
  retry:
    instances:
      customer-service:
        max-attempts: 3
        wait-duration: 1s
```

## Configuration

### Service Discovery

Configure Consul service discovery:

```yaml
spring:
  cloud:
    consul:
      host: localhost
      port: 8500
      discovery:
        enabled: true
        health-check-interval: 30s
```

### Security

Configure JWT token propagation:

```yaml
spring:
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: http://localhost:8090/auth/realms/ndvn
```

## Architecture

The framework follows the established Terasoluna layered architecture pattern:

```
shared-infrastructure/
├── client/           # API client framework
├── discovery/        # Service discovery
├── security/         # JWT token propagation
├── resilience/       # Circuit breakers and retries
├── versioning/       # API versioning support
├── monitoring/       # Metrics and observability
└── config/          # Auto-configuration
```

## Testing

The module includes comprehensive test support:

- **WireMock**: HTTP service mocking
- **TestContainers**: Consul integration testing
- **Resilience4j Test**: Circuit breaker testing

## Monitoring

Built-in metrics for:

- Service call latency
- Success/failure rates
- Circuit breaker states
- Service discovery health
- Token propagation status

## Best Practices

1. **Always use circuit breakers** for external service calls
2. **Configure appropriate timeouts** for each service
3. **Implement fallback methods** for critical operations
4. **Monitor service health** and adjust thresholds accordingly
5. **Use versioning headers** for API evolution
6. **Propagate correlation IDs** for distributed tracing

## Contributing

Follow the established Terasoluna framework patterns and ensure all changes include:

- Unit tests with >90% coverage
- Integration tests for external dependencies
- Documentation updates
- Metrics and monitoring support

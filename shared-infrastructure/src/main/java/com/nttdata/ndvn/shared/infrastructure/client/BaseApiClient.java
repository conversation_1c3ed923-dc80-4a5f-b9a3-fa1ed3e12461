package com.nttdata.ndvn.shared.infrastructure.client;

import com.nttdata.ndvn.shared.infrastructure.client.exception.*;
import com.nttdata.ndvn.shared.infrastructure.discovery.ServiceDiscoveryClient;
import com.nttdata.ndvn.shared.infrastructure.security.JwtTokenProvider;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;

/**
 * Abstract base class for service-to-service API clients.
 * 
 * Provides common functionality for:
 * - Service discovery integration
 * - JWT token propagation
 * - Error handling and logging
 * - Metrics collection
 * - Request/response transformation
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-28
 */
public abstract class BaseApiClient {
    
    protected final Logger logger = LoggerFactory.getLogger(getClass());
    
    private final String serviceName;
    private final ServiceDiscoveryClient serviceDiscovery;
    private final WebClient webClient;
    private final JwtTokenProvider tokenProvider;
    private final MeterRegistry meterRegistry;
    
    /**
     * Constructor for BaseApiClient.
     * 
     * @param serviceName the target service name for discovery
     * @param serviceDiscovery service discovery client
     * @param webClientBuilder WebClient builder for HTTP communication
     * @param tokenProvider JWT token provider for authentication
     * @param meterRegistry metrics registry for monitoring
     */
    protected BaseApiClient(String serviceName,
                          ServiceDiscoveryClient serviceDiscovery,
                          WebClient.Builder webClientBuilder,
                          JwtTokenProvider tokenProvider,
                          MeterRegistry meterRegistry) {
        this.serviceName = serviceName;
        this.serviceDiscovery = serviceDiscovery;
        this.tokenProvider = tokenProvider;
        this.meterRegistry = meterRegistry;
        this.webClient = configureWebClient(webClientBuilder);
    }
    
    /**
     * Configure WebClient with common settings.
     */
    private WebClient configureWebClient(WebClient.Builder builder) {
        return builder
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
                .defaultHeader("X-Client-Service", getClientServiceName())
                .defaultHeader("X-API-Version", "v1")
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(1024 * 1024)) // 1MB
                .build();
    }
    
    /**
     * Get the client service name for identification.
     */
    protected abstract String getClientServiceName();
    
    /**
     * Perform GET request.
     * 
     * @param path the API path
     * @param responseType the expected response type
     * @param uriVariables URI variables for path parameters
     * @return CompletableFuture with response
     */
    protected <T> CompletableFuture<T> get(String path, Class<T> responseType, Object... uriVariables) {
        return executeRequest("GET", path, null, responseType, uriVariables);
    }
    
    /**
     * Perform POST request.
     * 
     * @param path the API path
     * @param requestBody the request body
     * @param responseType the expected response type
     * @param uriVariables URI variables for path parameters
     * @return CompletableFuture with response
     */
    protected <T> CompletableFuture<T> post(String path, Object requestBody, Class<T> responseType, Object... uriVariables) {
        return executeRequest("POST", path, requestBody, responseType, uriVariables);
    }
    
    /**
     * Perform PUT request.
     * 
     * @param path the API path
     * @param requestBody the request body
     * @param responseType the expected response type
     * @param uriVariables URI variables for path parameters
     * @return CompletableFuture with response
     */
    protected <T> CompletableFuture<T> put(String path, Object requestBody, Class<T> responseType, Object... uriVariables) {
        return executeRequest("PUT", path, requestBody, responseType, uriVariables);
    }
    
    /**
     * Perform DELETE request.
     * 
     * @param path the API path
     * @param responseType the expected response type
     * @param uriVariables URI variables for path parameters
     * @return CompletableFuture with response
     */
    protected <T> CompletableFuture<T> delete(String path, Class<T> responseType, Object... uriVariables) {
        return executeRequest("DELETE", path, null, responseType, uriVariables);
    }
    
    /**
     * Execute HTTP request with common error handling and metrics.
     */
    private <T> CompletableFuture<T> executeRequest(String method, String path, Object requestBody, 
                                                   Class<T> responseType, Object... uriVariables) {
        Timer.Sample sample = Timer.start(meterRegistry);
        
        return serviceDiscovery.getServiceUrl(serviceName)
                .thenCompose(baseUrl -> {
                    WebClient.RequestBodySpec request = webClient
                            .method(org.springframework.http.HttpMethod.valueOf(method))
                            .uri(baseUrl + path, uriVariables)
                            .headers(this::addAuthHeaders);
                    
                    Mono<T> responseMono;
                    if (requestBody != null) {
                        responseMono = request.bodyValue(requestBody)
                                .retrieve()
                                .bodyToMono(responseType);
                    } else {
                        responseMono = request.retrieve()
                                .bodyToMono(responseType);
                    }
                    
                    return responseMono
                            .doOnSuccess(response -> {
                                sample.stop(Timer.builder("api.client.request")
                                        .tag("service", serviceName)
                                        .tag("method", method)
                                        .tag("path", path)
                                        .tag("status", "success")
                                        .register(meterRegistry));
                                logger.debug("Successfully called {} {} {}", method, serviceName, path);
                            })
                            .doOnError(error -> {
                                sample.stop(Timer.builder("api.client.request")
                                        .tag("service", serviceName)
                                        .tag("method", method)
                                        .tag("path", path)
                                        .tag("status", "error")
                                        .register(meterRegistry));
                                logger.error("Failed to call {} {} {}: {}", method, serviceName, path, error.getMessage());
                            })
                            .onErrorMap(this::mapException)
                            .toFuture();
                });
    }
    
    /**
     * Add authentication headers to the request.
     */
    private void addAuthHeaders(HttpHeaders headers) {
        tokenProvider.getCurrentToken().ifPresent(token -> {
            headers.setBearerAuth(token);
            logger.debug("Added JWT token to request for service: {}", serviceName);
        });
    }
    
    /**
     * Map WebClient exceptions to appropriate runtime exceptions.
     */
    private RuntimeException mapException(Throwable throwable) {
        if (throwable instanceof WebClientResponseException webClientException) {
            HttpStatus status = HttpStatus.valueOf(webClientException.getStatusCode().value());
            String responseBody = webClientException.getResponseBodyAsString();
            
            return switch (status) {
                case NOT_FOUND -> new ServiceNotFoundException(
                        String.format("Service %s not found: %s", serviceName, responseBody));
                case UNAUTHORIZED -> new ServiceAuthenticationException(
                        String.format("Authentication failed for service %s: %s", serviceName, responseBody));
                case FORBIDDEN -> new ServiceAuthorizationException(
                        String.format("Authorization failed for service %s: %s", serviceName, responseBody));
                case BAD_REQUEST -> new ServiceValidationException(
                        String.format("Validation failed for service %s: %s", serviceName, responseBody));
                case INTERNAL_SERVER_ERROR -> new ServiceInternalException(
                        String.format("Internal error in service %s: %s", serviceName, responseBody));
                case SERVICE_UNAVAILABLE -> new ServiceUnavailableException(
                        String.format("Service %s is unavailable: %s", serviceName, responseBody));
                default -> new ServiceCommunicationException(
                        String.format("Communication error with service %s [%s]: %s", 
                                serviceName, status, responseBody));
            };
        }
        
        return new ServiceCommunicationException(
                String.format("Unexpected error communicating with service %s: %s", 
                        serviceName, throwable.getMessage()), throwable);
    }
    
    /**
     * Get the target service name.
     */
    protected String getServiceName() {
        return serviceName;
    }
}

package com.nttdata.ndvn.shared.infrastructure.client;

import com.nttdata.ndvn.shared.infrastructure.discovery.ServiceDiscoveryClient;
import com.nttdata.ndvn.shared.infrastructure.security.JwtTokenProvider;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import io.github.resilience4j.retry.annotation.Retry;
import io.github.resilience4j.timelimiter.annotation.TimeLimiter;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * API client for User Management Service.
 * 
 * Provides methods for:
 * - User CRUD operations
 * - User authentication and authorization
 * - User profile management
 * - User role and permission queries
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-28
 */
@Component
public class UserServiceClient extends BaseApiClient {
    
    private static final String SERVICE_NAME = "user-management-service";
    
    public UserServiceClient(ServiceDiscoveryClient serviceDiscovery,
                           WebClient.Builder webClientBuilder,
                           JwtTokenProvider tokenProvider,
                           MeterRegistry meterRegistry) {
        super(SERVICE_NAME, serviceDiscovery, webClientBuilder, tokenProvider, meterRegistry);
    }
    
    @Override
    protected String getClientServiceName() {
        return "shared-infrastructure";
    }
    
    /**
     * Get user by ID.
     * 
     * @param userId the user ID
     * @return CompletableFuture with user data
     */
    @CircuitBreaker(name = SERVICE_NAME, fallbackMethod = "getUserFallback")
    @Retry(name = SERVICE_NAME)
    @TimeLimiter(name = SERVICE_NAME)
    public CompletableFuture<UserDto> getUser(String userId) {
        return get("/api/v1/users/{id}", UserDto.class, userId);
    }
    
    /**
     * Get user by username.
     * 
     * @param username the username
     * @return CompletableFuture with user data
     */
    @CircuitBreaker(name = SERVICE_NAME, fallbackMethod = "getUserByUsernameFallback")
    @Retry(name = SERVICE_NAME)
    @TimeLimiter(name = SERVICE_NAME)
    public CompletableFuture<UserDto> getUserByUsername(String username) {
        return get("/api/v1/users/username/{username}", UserDto.class, username);
    }
    
    /**
     * Get user by email.
     * 
     * @param email the email address
     * @return CompletableFuture with user data
     */
    @CircuitBreaker(name = SERVICE_NAME, fallbackMethod = "getUserByEmailFallback")
    @Retry(name = SERVICE_NAME)
    @TimeLimiter(name = SERVICE_NAME)
    public CompletableFuture<UserDto> getUserByEmail(String email) {
        return get("/api/v1/users/email/{email}", UserDto.class, email);
    }
    
    /**
     * Create a new user.
     * 
     * @param createUserRequest the user creation request
     * @return CompletableFuture with created user data
     */
    @CircuitBreaker(name = SERVICE_NAME, fallbackMethod = "createUserFallback")
    @Retry(name = SERVICE_NAME)
    @TimeLimiter(name = SERVICE_NAME)
    public CompletableFuture<UserDto> createUser(CreateUserRequest createUserRequest) {
        return post("/api/v1/users", createUserRequest, UserDto.class);
    }
    
    /**
     * Update user information.
     * 
     * @param userId the user ID
     * @param updateUserRequest the user update request
     * @return CompletableFuture with updated user data
     */
    @CircuitBreaker(name = SERVICE_NAME, fallbackMethod = "updateUserFallback")
    @Retry(name = SERVICE_NAME)
    @TimeLimiter(name = SERVICE_NAME)
    public CompletableFuture<UserDto> updateUser(String userId, UpdateUserRequest updateUserRequest) {
        return put("/api/v1/users/{id}", updateUserRequest, UserDto.class, userId);
    }
    
    /**
     * Delete a user.
     * 
     * @param userId the user ID
     * @return CompletableFuture with deletion result
     */
    @CircuitBreaker(name = SERVICE_NAME, fallbackMethod = "deleteUserFallback")
    @Retry(name = SERVICE_NAME)
    @TimeLimiter(name = SERVICE_NAME)
    public CompletableFuture<Void> deleteUser(String userId) {
        return delete("/api/v1/users/{id}", Void.class, userId);
    }
    
    /**
     * Get user roles.
     * 
     * @param userId the user ID
     * @return CompletableFuture with user roles
     */
    @CircuitBreaker(name = SERVICE_NAME, fallbackMethod = "getUserRolesFallback")
    @Retry(name = SERVICE_NAME)
    @TimeLimiter(name = SERVICE_NAME)
    public CompletableFuture<List<String>> getUserRoles(String userId) {
        return get("/api/v1/users/{id}/roles", (Class<List<String>>) (Class<?>) List.class, userId);
    }
    
    /**
     * Check if user has permission.
     * 
     * @param userId the user ID
     * @param permission the permission to check
     * @return CompletableFuture with permission check result
     */
    @CircuitBreaker(name = SERVICE_NAME, fallbackMethod = "hasPermissionFallback")
    @Retry(name = SERVICE_NAME)
    @TimeLimiter(name = SERVICE_NAME)
    public CompletableFuture<Boolean> hasPermission(String userId, String permission) {
        return get("/api/v1/users/{id}/permissions/{permission}", Boolean.class, userId, permission);
    }
    
    /**
     * Validate user credentials.
     * 
     * @param username the username
     * @param password the password
     * @return CompletableFuture with validation result
     */
    @CircuitBreaker(name = SERVICE_NAME, fallbackMethod = "validateCredentialsFallback")
    @Retry(name = SERVICE_NAME)
    @TimeLimiter(name = SERVICE_NAME)
    public CompletableFuture<Boolean> validateCredentials(String username, String password) {
        ValidateCredentialsRequest request = new ValidateCredentialsRequest(username, password);
        return post("/api/v1/auth/validate", request, Boolean.class);
    }
    
    // Fallback methods
    
    public CompletableFuture<UserDto> getUserFallback(String userId, Exception ex) {
        logger.warn("Fallback: Failed to get user {}", userId, ex);
        return CompletableFuture.completedFuture(null);
    }
    
    public CompletableFuture<UserDto> getUserByUsernameFallback(String username, Exception ex) {
        logger.warn("Fallback: Failed to get user by username {}", username, ex);
        return CompletableFuture.completedFuture(null);
    }
    
    public CompletableFuture<UserDto> getUserByEmailFallback(String email, Exception ex) {
        logger.warn("Fallback: Failed to get user by email {}", email, ex);
        return CompletableFuture.completedFuture(null);
    }
    
    public CompletableFuture<UserDto> createUserFallback(CreateUserRequest request, Exception ex) {
        logger.error("Fallback: Failed to create user", ex);
        throw new RuntimeException("User creation failed", ex);
    }
    
    public CompletableFuture<UserDto> updateUserFallback(String userId, UpdateUserRequest request, Exception ex) {
        logger.error("Fallback: Failed to update user {}", userId, ex);
        throw new RuntimeException("User update failed", ex);
    }
    
    public CompletableFuture<Void> deleteUserFallback(String userId, Exception ex) {
        logger.error("Fallback: Failed to delete user {}", userId, ex);
        throw new RuntimeException("User deletion failed", ex);
    }
    
    public CompletableFuture<List<String>> getUserRolesFallback(String userId, Exception ex) {
        logger.warn("Fallback: Failed to get user roles for {}", userId, ex);
        return CompletableFuture.completedFuture(List.of());
    }
    
    public CompletableFuture<Boolean> hasPermissionFallback(String userId, String permission, Exception ex) {
        logger.warn("Fallback: Failed to check permission {} for user {}", permission, userId, ex);
        return CompletableFuture.completedFuture(false);
    }
    
    public CompletableFuture<Boolean> validateCredentialsFallback(String username, String password, Exception ex) {
        logger.warn("Fallback: Failed to validate credentials for {}", username, ex);
        return CompletableFuture.completedFuture(false);
    }
    
    // DTOs
    
    public static class UserDto {
        private String id;
        private String username;
        private String email;
        private String firstName;
        private String lastName;
        private boolean enabled;
        private java.time.LocalDateTime createdAt;
        private java.time.LocalDateTime updatedAt;
        
        // Getters and setters
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        
        public String getFirstName() { return firstName; }
        public void setFirstName(String firstName) { this.firstName = firstName; }
        
        public String getLastName() { return lastName; }
        public void setLastName(String lastName) { this.lastName = lastName; }
        
        public boolean isEnabled() { return enabled; }
        public void setEnabled(boolean enabled) { this.enabled = enabled; }
        
        public java.time.LocalDateTime getCreatedAt() { return createdAt; }
        public void setCreatedAt(java.time.LocalDateTime createdAt) { this.createdAt = createdAt; }
        
        public java.time.LocalDateTime getUpdatedAt() { return updatedAt; }
        public void setUpdatedAt(java.time.LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
    }
    
    public static class CreateUserRequest {
        private String username;
        private String email;
        private String password;
        private String firstName;
        private String lastName;
        
        // Constructors, getters and setters
        public CreateUserRequest() {}
        
        public CreateUserRequest(String username, String email, String password, String firstName, String lastName) {
            this.username = username;
            this.email = email;
            this.password = password;
            this.firstName = firstName;
            this.lastName = lastName;
        }
        
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        
        public String getPassword() { return password; }
        public void setPassword(String password) { this.password = password; }
        
        public String getFirstName() { return firstName; }
        public void setFirstName(String firstName) { this.firstName = firstName; }
        
        public String getLastName() { return lastName; }
        public void setLastName(String lastName) { this.lastName = lastName; }
    }
    
    public static class UpdateUserRequest {
        private String email;
        private String firstName;
        private String lastName;
        private Boolean enabled;
        
        // Getters and setters
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        
        public String getFirstName() { return firstName; }
        public void setFirstName(String firstName) { this.firstName = firstName; }
        
        public String getLastName() { return lastName; }
        public void setLastName(String lastName) { this.lastName = lastName; }
        
        public Boolean getEnabled() { return enabled; }
        public void setEnabled(Boolean enabled) { this.enabled = enabled; }
    }
    
    public static class ValidateCredentialsRequest {
        private String username;
        private String password;
        
        public ValidateCredentialsRequest(String username, String password) {
            this.username = username;
            this.password = password;
        }
        
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        
        public String getPassword() { return password; }
        public void setPassword(String password) { this.password = password; }
    }
}

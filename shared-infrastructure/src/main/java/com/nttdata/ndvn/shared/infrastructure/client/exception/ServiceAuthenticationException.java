package com.nttdata.ndvn.shared.infrastructure.client.exception;

/**
 * Exception thrown when authentication fails.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-28
 */
public class ServiceAuthenticationException extends ServiceCommunicationException {

    public ServiceAuthenticationException(String message) {
        super(message);
    }
    
    public ServiceAuthenticationException(String message, Throwable cause) {
        super(message, cause);
    }
}

package com.nttdata.ndvn.shared.infrastructure.client.exception;

/**
 * Exception thrown when authorization fails.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-28
 */
public class ServiceAuthorizationException extends ServiceCommunicationException {

    public ServiceAuthorizationException(String message) {
        super(message);
    }
    
    public ServiceAuthorizationException(String message, Throwable cause) {
        super(message, cause);
    }
}

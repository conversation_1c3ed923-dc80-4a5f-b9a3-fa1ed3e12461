package com.nttdata.ndvn.shared.infrastructure.client.exception;

/**
 * Exception thrown when a service has internal errors.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-28
 */
public class ServiceInternalException extends ServiceCommunicationException {

    public ServiceInternalException(String message) {
        super(message);
    }
    
    public ServiceInternalException(String message, Throwable cause) {
        super(message, cause);
    }
}

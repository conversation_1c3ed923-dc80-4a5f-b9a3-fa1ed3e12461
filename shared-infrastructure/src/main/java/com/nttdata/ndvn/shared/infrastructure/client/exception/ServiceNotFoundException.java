package com.nttdata.ndvn.shared.infrastructure.client.exception;

/**
 * Exception thrown when a service is not found.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-28
 */
public class ServiceNotFoundException extends ServiceCommunicationException {

    public ServiceNotFoundException(String message) {
        super(message);
    }
    
    public ServiceNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
}

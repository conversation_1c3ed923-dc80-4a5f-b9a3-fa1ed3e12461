package com.nttdata.ndvn.shared.infrastructure.client.exception;

/**
 * Exception thrown when a service is unavailable.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-28
 */
public class ServiceUnavailableException extends ServiceCommunicationException {

    public ServiceUnavailableException(String message) {
        super(message);
    }
    
    public ServiceUnavailableException(String message, Throwable cause) {
        super(message, cause);
    }
}

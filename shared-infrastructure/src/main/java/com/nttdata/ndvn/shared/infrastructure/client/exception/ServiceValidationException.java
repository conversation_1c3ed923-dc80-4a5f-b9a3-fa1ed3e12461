package com.nttdata.ndvn.shared.infrastructure.client.exception;

/**
 * Exception thrown when validation fails.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-28
 */
public class ServiceValidationException extends ServiceCommunicationException {

    public ServiceValidationException(String message) {
        super(message);
    }
    
    public ServiceValidationException(String message, Throwable cause) {
        super(message, cause);
    }
}

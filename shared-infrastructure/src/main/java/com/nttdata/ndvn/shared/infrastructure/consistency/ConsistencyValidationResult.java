package com.nttdata.ndvn.shared.infrastructure.consistency;

/**
 * Result of consistency validation.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-28
 */
public class ConsistencyValidationResult {
    
    private final String entityId;
    private final boolean consistent;
    private final String message;
    
    private ConsistencyValidationResult(String entityId, boolean consistent, String message) {
        this.entityId = entityId;
        this.consistent = consistent;
        this.message = message;
    }
    
    public static ConsistencyValidationResult consistent(String entityId) {
        return new ConsistencyValidationResult(entityId, true, "Entity is consistent");
    }
    
    public static ConsistencyValidationResult inconsistent(String entityId, String message) {
        return new ConsistencyValidationResult(entityId, false, message);
    }
    
    public static ConsistencyValidationResult error(String entityId, String message) {
        return new ConsistencyValidationResult(entityId, false, "Validation error: " + message);
    }
    
    public String getEntityId() {
        return entityId;
    }
    
    public boolean isConsistent() {
        return consistent;
    }
    
    public String getMessage() {
        return message;
    }
    
    @Override
    public String toString() {
        return String.format("ConsistencyValidationResult{entityId='%s', consistent=%s, message='%s'}", 
                entityId, consistent, message);
    }
}

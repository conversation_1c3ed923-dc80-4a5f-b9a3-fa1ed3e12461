package com.nttdata.ndvn.shared.infrastructure.consistency;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * Framework for synchronizing read models across SCS services.
 * 
 * Provides mechanisms for:
 * - Event-driven read model updates
 * - Conflict resolution strategies
 * - Synchronization monitoring
 * - Recovery from synchronization failures
 * - Consistency validation
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-28
 */
@Component
public class ReadModelSynchronizer {
    
    private static final Logger logger = LoggerFactory.getLogger(ReadModelSynchronizer.class);
    
    private final MeterRegistry meterRegistry;
    private final ConcurrentMap<String, ReadModelHandler> handlers = new ConcurrentHashMap<>();
    
    // Metrics
    private final Counter syncSuccessCounter;
    private final Counter syncFailureCounter;
    private final Counter conflictCounter;
    private final Timer syncDurationTimer;
    
    public ReadModelSynchronizer(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        
        // Initialize metrics
        this.syncSuccessCounter = Counter.builder("readmodel.sync.success")
                .description("Successful read model synchronizations")
                .register(meterRegistry);
        this.syncFailureCounter = Counter.builder("readmodel.sync.failure")
                .description("Failed read model synchronizations")
                .register(meterRegistry);
        this.conflictCounter = Counter.builder("readmodel.sync.conflict")
                .description("Read model synchronization conflicts")
                .register(meterRegistry);
        this.syncDurationTimer = Timer.builder("readmodel.sync.duration")
                .description("Read model synchronization duration")
                .register(meterRegistry);
    }
    
    /**
     * Register a read model handler for a specific entity type.
     * 
     * @param entityType the entity type (e.g., "Customer", "Order")
     * @param handler the read model handler
     */
    public void registerHandler(String entityType, ReadModelHandler handler) {
        handlers.put(entityType, handler);
        logger.info("Registered read model handler for entity type: {}", entityType);
    }
    
    /**
     * Synchronize read model based on domain event.
     * 
     * @param event the domain event
     * @return CompletableFuture with synchronization result
     */
    public CompletableFuture<SynchronizationResult> synchronize(DomainEvent event) {
        Timer.Sample sample = Timer.start(meterRegistry);
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                String entityType = event.getEntityType();
                ReadModelHandler handler = handlers.get(entityType);
                
                if (handler == null) {
                    logger.warn("No handler registered for entity type: {}", entityType);
                    return SynchronizationResult.skipped(event.getEventId(), 
                            "No handler for entity type: " + entityType);
                }
                
                logger.debug("Synchronizing read model for event: {} entity: {} type: {}", 
                        event.getEventId(), event.getEntityId(), entityType);
                
                // Check for conflicts
                ConflictResolution conflictResolution = checkForConflicts(event, handler);
                if (conflictResolution.hasConflict()) {
                    conflictCounter.increment();
                    logger.warn("Conflict detected for event: {} resolution: {}", 
                            event.getEventId(), conflictResolution.getStrategy());
                    
                    if (conflictResolution.getStrategy() == ConflictStrategy.REJECT) {
                        return SynchronizationResult.conflict(event.getEventId(), 
                                "Event rejected due to conflict");
                    }
                }
                
                // Apply the event to read model
                ReadModelUpdateResult updateResult = handler.handleEvent(event);
                
                if (updateResult.isSuccess()) {
                    syncSuccessCounter.increment();
                    sample.stop(syncDurationTimer);
                    
                    logger.debug("Read model synchronized successfully for event: {}", event.getEventId());
                    return SynchronizationResult.success(event.getEventId(), updateResult.getVersion());
                    
                } else {
                    syncFailureCounter.increment();
                    sample.stop(syncDurationTimer);
                    
                    logger.error("Read model synchronization failed for event: {} error: {}", 
                            event.getEventId(), updateResult.getErrorMessage());
                    return SynchronizationResult.failure(event.getEventId(), updateResult.getErrorMessage());
                }
                
            } catch (Exception e) {
                syncFailureCounter.increment();
                sample.stop(syncDurationTimer);
                
                logger.error("Error during read model synchronization for event: {}", 
                        event.getEventId(), e);
                return SynchronizationResult.failure(event.getEventId(), 
                        "Synchronization error: " + e.getMessage());
            }
        });
    }
    
    /**
     * Batch synchronize multiple events.
     * 
     * @param events list of domain events
     * @return CompletableFuture with batch synchronization results
     */
    public CompletableFuture<List<SynchronizationResult>> synchronizeBatch(List<DomainEvent> events) {
        logger.info("Starting batch synchronization for {} events", events.size());
        
        List<CompletableFuture<SynchronizationResult>> futures = events.stream()
                .map(this::synchronize)
                .toList();
        
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> futures.stream()
                        .map(CompletableFuture::join)
                        .toList());
    }
    
    /**
     * Validate consistency of read model against source of truth.
     * 
     * @param entityType the entity type to validate
     * @param entityId the entity ID to validate
     * @return CompletableFuture with validation result
     */
    public CompletableFuture<ConsistencyValidationResult> validateConsistency(String entityType, String entityId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                ReadModelHandler handler = handlers.get(entityType);
                if (handler == null) {
                    return ConsistencyValidationResult.error(entityId, 
                            "No handler for entity type: " + entityType);
                }
                
                logger.debug("Validating consistency for entity: {} type: {}", entityId, entityType);
                
                return handler.validateConsistency(entityId);
                
            } catch (Exception e) {
                logger.error("Error validating consistency for entity: {} type: {}", 
                        entityId, entityType, e);
                return ConsistencyValidationResult.error(entityId, 
                        "Validation error: " + e.getMessage());
            }
        });
    }
    
    /**
     * Reconcile read model with source of truth.
     * 
     * @param entityType the entity type to reconcile
     * @param entityId the entity ID to reconcile
     * @return CompletableFuture with reconciliation result
     */
    public CompletableFuture<ReconciliationResult> reconcile(String entityType, String entityId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                ReadModelHandler handler = handlers.get(entityType);
                if (handler == null) {
                    return ReconciliationResult.error(entityId, 
                            "No handler for entity type: " + entityType);
                }
                
                logger.info("Starting reconciliation for entity: {} type: {}", entityId, entityType);
                
                return handler.reconcile(entityId);
                
            } catch (Exception e) {
                logger.error("Error during reconciliation for entity: {} type: {}", 
                        entityId, entityType, e);
                return ReconciliationResult.error(entityId, 
                        "Reconciliation error: " + e.getMessage());
            }
        });
    }
    
    /**
     * Get synchronization statistics.
     * 
     * @return synchronization statistics
     */
    public SynchronizationStatistics getStatistics() {
        return new SynchronizationStatistics(
                (long) syncSuccessCounter.count(),
                (long) syncFailureCounter.count(),
                (long) conflictCounter.count(),
                syncDurationTimer.mean(java.util.concurrent.TimeUnit.MILLISECONDS),
                handlers.size()
        );
    }
    
    /**
     * Check for conflicts before applying event.
     */
    private ConflictResolution checkForConflicts(DomainEvent event, ReadModelHandler handler) {
        try {
            return handler.checkConflict(event);
        } catch (Exception e) {
            logger.error("Error checking for conflicts for event: {}", event.getEventId(), e);
            // Default to no conflict on error
            return ConflictResolution.noConflict();
        }
    }
    
    /**
     * Get registered handlers.
     */
    public ConcurrentMap<String, ReadModelHandler> getHandlers() {
        return new ConcurrentHashMap<>(handlers);
    }
    
    /**
     * Remove handler for entity type.
     */
    public void removeHandler(String entityType) {
        ReadModelHandler removed = handlers.remove(entityType);
        if (removed != null) {
            logger.info("Removed read model handler for entity type: {}", entityType);
        }
    }
}

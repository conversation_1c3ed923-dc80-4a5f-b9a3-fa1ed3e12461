package com.nttdata.ndvn.shared.infrastructure.consistency;

/**
 * Result of read model update operation.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-28
 */
public class ReadModelUpdateResult {
    
    private final boolean success;
    private final String errorMessage;
    private final Long version;
    
    private ReadModelUpdateResult(boolean success, String errorMessage, Long version) {
        this.success = success;
        this.errorMessage = errorMessage;
        this.version = version;
    }
    
    public static ReadModelUpdateResult success(Long version) {
        return new ReadModelUpdateResult(true, null, version);
    }
    
    public static ReadModelUpdateResult failure(String errorMessage) {
        return new ReadModelUpdateResult(false, errorMessage, null);
    }
    
    public boolean isSuccess() {
        return success;
    }
    
    public boolean isFailure() {
        return !success;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public Long getVersion() {
        return version;
    }
    
    @Override
    public String toString() {
        return String.format("ReadModelUpdateResult{success=%s, errorMessage='%s', version=%d}", 
                success, errorMessage, version);
    }
}



/**
 * Synchronization statistics.
 */
class SynchronizationStatistics {
    
    private final long successCount;
    private final long failureCount;
    private final long conflictCount;
    private final double averageDuration;
    private final int handlerCount;
    
    public SynchronizationStatistics(long successCount, long failureCount, long conflictCount,
                                   double averageDuration, int handlerCount) {
        this.successCount = successCount;
        this.failureCount = failureCount;
        this.conflictCount = conflictCount;
        this.averageDuration = averageDuration;
        this.handlerCount = handlerCount;
    }
    
    public long getSuccessCount() {
        return successCount;
    }
    
    public long getFailureCount() {
        return failureCount;
    }
    
    public long getConflictCount() {
        return conflictCount;
    }
    
    public double getAverageDuration() {
        return averageDuration;
    }
    
    public int getHandlerCount() {
        return handlerCount;
    }
    
    public long getTotalCount() {
        return successCount + failureCount + conflictCount;
    }
    
    public double getSuccessRate() {
        long total = getTotalCount();
        return total > 0 ? (double) successCount / total : 0.0;
    }
    
    @Override
    public String toString() {
        return String.format("SynchronizationStatistics{success=%d, failure=%d, conflict=%d, " +
                "avgDuration=%.2fms, handlers=%d, successRate=%.2f%%}", 
                successCount, failureCount, conflictCount, averageDuration, handlerCount, 
                getSuccessRate() * 100);
    }
}

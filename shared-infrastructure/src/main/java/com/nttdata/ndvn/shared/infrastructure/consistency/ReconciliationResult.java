package com.nttdata.ndvn.shared.infrastructure.consistency;

/**
 * Result of reconciliation operation.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-28
 */
public class ReconciliationResult {
    
    private final String entityId;
    private final boolean success;
    private final String message;
    private final int changesApplied;
    
    private ReconciliationResult(String entityId, boolean success, String message, int changesApplied) {
        this.entityId = entityId;
        this.success = success;
        this.message = message;
        this.changesApplied = changesApplied;
    }
    
    public static ReconciliationResult success(String entityId, int changesApplied) {
        return new ReconciliationResult(entityId, true, 
                "Reconciliation completed successfully", changesApplied);
    }
    
    public static ReconciliationResult error(String entityId, String message) {
        return new ReconciliationResult(entityId, false, message, 0);
    }
    
    public String getEntityId() {
        return entityId;
    }
    
    public boolean isSuccess() {
        return success;
    }
    
    public String getMessage() {
        return message;
    }
    
    public int getChangesApplied() {
        return changesApplied;
    }
    
    @Override
    public String toString() {
        return String.format("ReconciliationResult{entityId='%s', success=%s, message='%s', changes=%d}", 
                entityId, success, message, changesApplied);
    }
}

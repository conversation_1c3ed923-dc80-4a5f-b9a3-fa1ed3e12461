package com.nttdata.ndvn.shared.infrastructure.monitoring;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Metrics collector for service-to-service communication.
 * 
 * Provides comprehensive metrics for:
 * - Request latency and throughput
 * - Success and error rates
 * - Circuit breaker states
 * - Service discovery performance
 * - Token propagation status
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-28
 */
@Component
public class ServiceCommunicationMetrics {
    
    private static final Logger logger = LoggerFactory.getLogger(ServiceCommunicationMetrics.class);
    
    private final MeterRegistry meterRegistry;
    
    // Counters for tracking call statistics
    private final ConcurrentHashMap<String, Counter> successCounters = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Counter> errorCounters = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Counter> timeoutCounters = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Counter> circuitBreakerOpenCounters = new ConcurrentHashMap<>();
    
    // Timers for tracking latency
    private final ConcurrentHashMap<String, Timer> requestTimers = new ConcurrentHashMap<>();
    
    // Gauges for tracking current state
    private final ConcurrentHashMap<String, AtomicLong> activeConnections = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, AtomicLong> queuedRequests = new ConcurrentHashMap<>();
    
    public ServiceCommunicationMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        initializeCommonMetrics();
    }
    
    /**
     * Initialize common metrics that are always available.
     */
    private void initializeCommonMetrics() {
        // Service discovery metrics
        Counter.builder("service.discovery.requests")
                .description("Total service discovery requests")
                .register(meterRegistry);
        
        Counter.builder("service.discovery.cache.hits")
                .description("Service discovery cache hits")
                .register(meterRegistry);
        
        Counter.builder("service.discovery.cache.misses")
                .description("Service discovery cache misses")
                .register(meterRegistry);
        
        // JWT token metrics
        Counter.builder("jwt.token.propagated")
                .description("JWT tokens successfully propagated")
                .register(meterRegistry);
        
        Counter.builder("jwt.token.missing")
                .description("Requests without JWT tokens")
                .register(meterRegistry);
        
        Counter.builder("jwt.token.expired")
                .description("Expired JWT tokens encountered")
                .register(meterRegistry);
        
        logger.info("Initialized service communication metrics");
    }
    
    /**
     * Record a successful service call.
     * 
     * @param serviceName the target service name
     * @param method the HTTP method
     * @param path the API path
     * @param duration the call duration in milliseconds
     */
    public void recordSuccess(String serviceName, String method, String path, long duration) {
        getSuccessCounter(serviceName, method, path).increment();
        getRequestTimer(serviceName, method, path).record(duration, java.util.concurrent.TimeUnit.MILLISECONDS);
        
        logger.debug("Recorded successful call to {} {} {} in {}ms", serviceName, method, path, duration);
    }
    
    /**
     * Record a failed service call.
     * 
     * @param serviceName the target service name
     * @param method the HTTP method
     * @param path the API path
     * @param errorType the type of error
     * @param duration the call duration in milliseconds
     */
    public void recordError(String serviceName, String method, String path, String errorType, long duration) {
        getErrorCounter(serviceName, method, path, errorType).increment();
        getRequestTimer(serviceName, method, path).record(duration, java.util.concurrent.TimeUnit.MILLISECONDS);
        
        logger.debug("Recorded failed call to {} {} {} with error {} in {}ms", 
                serviceName, method, path, errorType, duration);
    }
    
    /**
     * Record a timeout.
     * 
     * @param serviceName the target service name
     * @param method the HTTP method
     * @param path the API path
     */
    public void recordTimeout(String serviceName, String method, String path) {
        getTimeoutCounter(serviceName, method, path).increment();
        
        logger.debug("Recorded timeout for {} {} {}", serviceName, method, path);
    }
    
    /**
     * Record circuit breaker opening.
     * 
     * @param serviceName the target service name
     */
    public void recordCircuitBreakerOpen(String serviceName) {
        getCircuitBreakerOpenCounter(serviceName).increment();
        
        logger.warn("Recorded circuit breaker open for service: {}", serviceName);
    }
    
    /**
     * Update active connections count.
     * 
     * @param serviceName the target service name
     * @param count the current active connections
     */
    public void updateActiveConnections(String serviceName, long count) {
        getActiveConnectionsGauge(serviceName).set(count);
    }
    
    /**
     * Update queued requests count.
     * 
     * @param serviceName the target service name
     * @param count the current queued requests
     */
    public void updateQueuedRequests(String serviceName, long count) {
        getQueuedRequestsGauge(serviceName).set(count);
    }
    
    /**
     * Get or create success counter for a service endpoint.
     */
    private Counter getSuccessCounter(String serviceName, String method, String path) {
        String key = serviceName + ":" + method + ":" + path;
        return successCounters.computeIfAbsent(key, k -> 
                Counter.builder("service.call.success")
                        .description("Successful service calls")
                        .tag("service", serviceName)
                        .tag("method", method)
                        .tag("path", sanitizePath(path))
                        .register(meterRegistry));
    }
    
    /**
     * Get or create error counter for a service endpoint.
     */
    private Counter getErrorCounter(String serviceName, String method, String path, String errorType) {
        String key = serviceName + ":" + method + ":" + path + ":" + errorType;
        return errorCounters.computeIfAbsent(key, k -> 
                Counter.builder("service.call.error")
                        .description("Failed service calls")
                        .tag("service", serviceName)
                        .tag("method", method)
                        .tag("path", sanitizePath(path))
                        .tag("error_type", errorType)
                        .register(meterRegistry));
    }
    
    /**
     * Get or create timeout counter for a service endpoint.
     */
    private Counter getTimeoutCounter(String serviceName, String method, String path) {
        String key = serviceName + ":" + method + ":" + path;
        return timeoutCounters.computeIfAbsent(key, k -> 
                Counter.builder("service.call.timeout")
                        .description("Service call timeouts")
                        .tag("service", serviceName)
                        .tag("method", method)
                        .tag("path", sanitizePath(path))
                        .register(meterRegistry));
    }
    
    /**
     * Get or create circuit breaker open counter for a service.
     */
    private Counter getCircuitBreakerOpenCounter(String serviceName) {
        return circuitBreakerOpenCounters.computeIfAbsent(serviceName, k -> 
                Counter.builder("service.circuit_breaker.open")
                        .description("Circuit breaker open events")
                        .tag("service", serviceName)
                        .register(meterRegistry));
    }
    
    /**
     * Get or create request timer for a service endpoint.
     */
    private Timer getRequestTimer(String serviceName, String method, String path) {
        String key = serviceName + ":" + method + ":" + path;
        return requestTimers.computeIfAbsent(key, k -> 
                Timer.builder("service.call.duration")
                        .description("Service call duration")
                        .tag("service", serviceName)
                        .tag("method", method)
                        .tag("path", sanitizePath(path))
                        .register(meterRegistry));
    }
    
    /**
     * Get or create active connections gauge for a service.
     */
    private AtomicLong getActiveConnectionsGauge(String serviceName) {
        return activeConnections.computeIfAbsent(serviceName, k -> {
            AtomicLong gauge = new AtomicLong(0);
            Gauge.builder("service.connections.active", gauge, AtomicLong::doubleValue)
                    .description("Active connections to service")
                    .tag("service", serviceName)
                    .register(meterRegistry);
            return gauge;
        });
    }
    
    /**
     * Get or create queued requests gauge for a service.
     */
    private AtomicLong getQueuedRequestsGauge(String serviceName) {
        return queuedRequests.computeIfAbsent(serviceName, k -> {
            AtomicLong gauge = new AtomicLong(0);
            Gauge.builder("service.requests.queued", gauge, AtomicLong::doubleValue)
                    .description("Queued requests for service")
                    .tag("service", serviceName)
                    .register(meterRegistry);
            return gauge;
        });
    }
    
    /**
     * Sanitize path for metrics to avoid high cardinality.
     */
    private String sanitizePath(String path) {
        if (path == null) {
            return "unknown";
        }
        
        // Replace path parameters with placeholders
        return path.replaceAll("/\\d+", "/{id}")
                   .replaceAll("/[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}", "/{uuid}")
                   .replaceAll("/[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}", "/{email}");
    }
    
    /**
     * Get current metrics summary for a service.
     * 
     * @param serviceName the service name
     * @return metrics summary
     */
    public ServiceMetricsSummary getMetricsSummary(String serviceName) {
        long totalSuccesses = successCounters.entrySet().stream()
                .filter(entry -> entry.getKey().startsWith(serviceName + ":"))
                .mapToLong(entry -> (long) entry.getValue().count())
                .sum();
        
        long totalErrors = errorCounters.entrySet().stream()
                .filter(entry -> entry.getKey().startsWith(serviceName + ":"))
                .mapToLong(entry -> (long) entry.getValue().count())
                .sum();
        
        long totalTimeouts = timeoutCounters.entrySet().stream()
                .filter(entry -> entry.getKey().startsWith(serviceName + ":"))
                .mapToLong(entry -> (long) entry.getValue().count())
                .sum();
        
        long activeConns = activeConnections.getOrDefault(serviceName, new AtomicLong(0)).get();
        long queuedReqs = queuedRequests.getOrDefault(serviceName, new AtomicLong(0)).get();
        
        return new ServiceMetricsSummary(serviceName, totalSuccesses, totalErrors, totalTimeouts, activeConns, queuedReqs);
    }
    
    /**
     * Metrics summary for a service.
     */
    public static class ServiceMetricsSummary {
        private final String serviceName;
        private final long totalSuccesses;
        private final long totalErrors;
        private final long totalTimeouts;
        private final long activeConnections;
        private final long queuedRequests;
        
        public ServiceMetricsSummary(String serviceName, long totalSuccesses, long totalErrors, 
                                   long totalTimeouts, long activeConnections, long queuedRequests) {
            this.serviceName = serviceName;
            this.totalSuccesses = totalSuccesses;
            this.totalErrors = totalErrors;
            this.totalTimeouts = totalTimeouts;
            this.activeConnections = activeConnections;
            this.queuedRequests = queuedRequests;
        }
        
        public String getServiceName() { return serviceName; }
        public long getTotalSuccesses() { return totalSuccesses; }
        public long getTotalErrors() { return totalErrors; }
        public long getTotalTimeouts() { return totalTimeouts; }
        public long getActiveConnections() { return activeConnections; }
        public long getQueuedRequests() { return queuedRequests; }
        
        public double getSuccessRate() {
            long total = totalSuccesses + totalErrors;
            return total > 0 ? (double) totalSuccesses / total : 0.0;
        }
        
        @Override
        public String toString() {
            return String.format("ServiceMetrics{service='%s', successes=%d, errors=%d, timeouts=%d, " +
                    "activeConns=%d, queuedReqs=%d, successRate=%.2f%%}", 
                    serviceName, totalSuccesses, totalErrors, totalTimeouts, 
                    activeConnections, queuedRequests, getSuccessRate() * 100);
        }
    }
}

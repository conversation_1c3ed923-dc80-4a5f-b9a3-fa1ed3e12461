/**
 * Shared Infrastructure Framework
 * 
 * This package provides common infrastructure components for service-to-service
 * communication across all SCS services in the NDVN Terasoluna Base platform.
 * 
 * Key Components:
 * - BaseApiClient: Abstract base class for service-to-service API clients
 * - ServiceDiscoveryClient: Service discovery and load balancing
 * - JWT Token Propagation: Authentication token forwarding
 * - Resilience Patterns: Circuit breakers, retries, timeouts
 * - API Versioning: Version negotiation and compatibility
 * - Monitoring: Metrics and observability
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-28
 */
package com.nttdata.ndvn.shared.infrastructure;

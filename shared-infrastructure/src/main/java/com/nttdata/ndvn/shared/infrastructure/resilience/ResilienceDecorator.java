package com.nttdata.ndvn.shared.infrastructure.resilience;

import io.github.resilience4j.bulkhead.Bulkhead;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.retry.Retry;
import io.github.resilience4j.timelimiter.TimeLimiter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.function.Supplier;

/**
 * Decorator for applying resilience patterns to service calls.
 * 
 * Provides convenient methods to wrap service calls with:
 * - Circuit breaker protection
 * - Retry mechanisms
 * - Time limiting
 * - Bulkhead isolation
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-28
 */
@Component
public class ResilienceDecorator {
    
    private static final Logger logger = LoggerFactory.getLogger(ResilienceDecorator.class);

    private final ResilienceConfiguration resilienceConfig;
    private final ScheduledExecutorService scheduledExecutorService;

    public ResilienceDecorator(ResilienceConfiguration resilienceConfig) {
        this.resilienceConfig = resilienceConfig;
        this.scheduledExecutorService = Executors.newScheduledThreadPool(10);
    }
    
    /**
     * Decorate a supplier with full resilience patterns.
     * 
     * @param serviceName the name of the target service
     * @param supplier the operation to execute
     * @param fallback optional fallback supplier
     * @return decorated supplier with resilience patterns
     */
    public <T> CompletableFuture<T> decorateSupplier(String serviceName, 
                                                   Supplier<CompletionStage<T>> supplier,
                                                   Supplier<T> fallback) {
        
        logger.debug("Decorating call to service: {}", serviceName);
        
        // Get resilience components
        CircuitBreaker circuitBreaker = resilienceConfig.getCircuitBreaker(serviceName);
        Retry retry = resilienceConfig.getRetry(serviceName);
        TimeLimiter timeLimiter = resilienceConfig.getTimeLimiter(serviceName);
        Bulkhead bulkhead = resilienceConfig.getBulkhead(serviceName);
        
        // Create the decorated supplier
        Supplier<CompletionStage<T>> decoratedSupplier = Bulkhead
                .decorateCompletionStage(bulkhead, supplier);
        
        decoratedSupplier = TimeLimiter
                .decorateCompletionStage(timeLimiter, scheduledExecutorService, decoratedSupplier);

        decoratedSupplier = CircuitBreaker
                .decorateCompletionStage(circuitBreaker, decoratedSupplier);

        decoratedSupplier = Retry
                .decorateCompletionStage(retry, scheduledExecutorService, decoratedSupplier);
        
        // Execute with fallback handling
        return decoratedSupplier.get()
                .toCompletableFuture()
                .exceptionally(throwable -> {
                    logger.error("Service call to {} failed, attempting fallback", serviceName, throwable);
                    if (fallback != null) {
                        try {
                            T fallbackResult = fallback.get();
                            logger.info("Fallback successful for service: {}", serviceName);
                            return fallbackResult;
                        } catch (Exception fallbackException) {
                            logger.error("Fallback also failed for service: {}", serviceName, fallbackException);
                            throw new RuntimeException("Service call and fallback both failed", throwable);
                        }
                    } else {
                        throw new RuntimeException("Service call failed and no fallback available", throwable);
                    }
                });
    }
    
    /**
     * Decorate a supplier with full resilience patterns (no fallback).
     * 
     * @param serviceName the name of the target service
     * @param supplier the operation to execute
     * @return decorated supplier with resilience patterns
     */
    public <T> CompletableFuture<T> decorateSupplier(String serviceName, 
                                                   Supplier<CompletionStage<T>> supplier) {
        return decorateSupplier(serviceName, supplier, null);
    }
    
    /**
     * Decorate a supplier with circuit breaker only.
     * 
     * @param serviceName the name of the target service
     * @param supplier the operation to execute
     * @return decorated supplier with circuit breaker
     */
    public <T> CompletableFuture<T> decorateWithCircuitBreaker(String serviceName, 
                                                             Supplier<CompletionStage<T>> supplier) {
        
        CircuitBreaker circuitBreaker = resilienceConfig.getCircuitBreaker(serviceName);
        
        Supplier<CompletionStage<T>> decoratedSupplier = CircuitBreaker
                .decorateCompletionStage(circuitBreaker, supplier);
        
        return decoratedSupplier.get().toCompletableFuture();
    }
    
    /**
     * Decorate a supplier with retry only.
     * 
     * @param serviceName the name of the target service
     * @param supplier the operation to execute
     * @return decorated supplier with retry
     */
    public <T> CompletableFuture<T> decorateWithRetry(String serviceName, 
                                                    Supplier<CompletionStage<T>> supplier) {
        
        Retry retry = resilienceConfig.getRetry(serviceName);
        
        Supplier<CompletionStage<T>> decoratedSupplier = Retry
                .decorateCompletionStage(retry, scheduledExecutorService, supplier);
        
        return decoratedSupplier.get().toCompletableFuture();
    }
    
    /**
     * Decorate a supplier with time limiter only.
     * 
     * @param serviceName the name of the target service
     * @param supplier the operation to execute
     * @return decorated supplier with time limiter
     */
    public <T> CompletableFuture<T> decorateWithTimeLimiter(String serviceName, 
                                                          Supplier<CompletionStage<T>> supplier) {
        
        TimeLimiter timeLimiter = resilienceConfig.getTimeLimiter(serviceName);
        
        Supplier<CompletionStage<T>> decoratedSupplier = TimeLimiter
                .decorateCompletionStage(timeLimiter, scheduledExecutorService, supplier);
        
        return decoratedSupplier.get().toCompletableFuture();
    }
    
    /**
     * Decorate a supplier with bulkhead only.
     * 
     * @param serviceName the name of the target service
     * @param supplier the operation to execute
     * @return decorated supplier with bulkhead
     */
    public <T> CompletableFuture<T> decorateWithBulkhead(String serviceName, 
                                                       Supplier<CompletionStage<T>> supplier) {
        
        Bulkhead bulkhead = resilienceConfig.getBulkhead(serviceName);
        
        Supplier<CompletionStage<T>> decoratedSupplier = Bulkhead
                .decorateCompletionStage(bulkhead, supplier);
        
        return decoratedSupplier.get().toCompletableFuture();
    }
    
    /**
     * Get circuit breaker state for monitoring.
     * 
     * @param serviceName the name of the service
     * @return circuit breaker state
     */
    public CircuitBreaker.State getCircuitBreakerState(String serviceName) {
        return resilienceConfig.getCircuitBreaker(serviceName).getState();
    }
    
    /**
     * Check if circuit breaker is open for a service.
     * 
     * @param serviceName the name of the service
     * @return true if circuit breaker is open
     */
    public boolean isCircuitBreakerOpen(String serviceName) {
        return getCircuitBreakerState(serviceName) == CircuitBreaker.State.OPEN;
    }
    
    /**
     * Get bulkhead available concurrent calls.
     * 
     * @param serviceName the name of the service
     * @return number of available concurrent calls
     */
    public int getBulkheadAvailableConcurrentCalls(String serviceName) {
        return resilienceConfig.getBulkhead(serviceName).getMetrics().getAvailableConcurrentCalls();
    }
    
    /**
     * Force circuit breaker to open state (for testing/maintenance).
     * 
     * @param serviceName the name of the service
     */
    public void forceCircuitBreakerOpen(String serviceName) {
        resilienceConfig.getCircuitBreaker(serviceName).transitionToOpenState();
        logger.warn("Forced circuit breaker to OPEN state for service: {}", serviceName);
    }
    
    /**
     * Force circuit breaker to closed state (for testing/maintenance).
     * 
     * @param serviceName the name of the service
     */
    public void forceCircuitBreakerClosed(String serviceName) {
        resilienceConfig.getCircuitBreaker(serviceName).transitionToClosedState();
        logger.info("Forced circuit breaker to CLOSED state for service: {}", serviceName);
    }
}

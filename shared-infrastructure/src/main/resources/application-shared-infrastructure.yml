# Shared Infrastructure Framework Configuration
# Include this configuration in your service's application.yml

# Resilience4j Configuration
resilience4j:
  circuitbreaker:
    configs:
      default:
        failure-rate-threshold: 50
        wait-duration-in-open-state: 30s
        sliding-window-size: 10
        minimum-number-of-calls: 5
        permitted-number-of-calls-in-half-open-state: 3
        automatic-transition-from-open-to-half-open-enabled: true
    instances:
      user-management-service:
        base-config: default
        failure-rate-threshold: 60
      customer-management-service:
        base-config: default
        failure-rate-threshold: 50
      product-catalog-service:
        base-config: default
        failure-rate-threshold: 70
        wait-duration-in-open-state: 20s
      order-management-service:
        base-config: default
        failure-rate-threshold: 40
        wait-duration-in-open-state: 60s
      notification-service:
        base-config: default
        failure-rate-threshold: 80
        wait-duration-in-open-state: 10s

  retry:
    configs:
      default:
        max-attempts: 3
        wait-duration: 1s
        exponential-backoff-multiplier: 2
        retry-exceptions:
          - java.net.ConnectException
          - java.net.SocketTimeoutException
          - java.util.concurrent.TimeoutException
    instances:
      user-management-service:
        base-config: default
      customer-management-service:
        base-config: default
      product-catalog-service:
        base-config: default
      order-management-service:
        base-config: default
        max-attempts: 2
        wait-duration: 2s
      notification-service:
        base-config: default
        max-attempts: 5
        wait-duration: 500ms

  timelimiter:
    configs:
      default:
        timeout-duration: 10s
        cancel-running-future: true
    instances:
      user-management-service:
        timeout-duration: 5s
      customer-management-service:
        timeout-duration: 5s
      product-catalog-service:
        timeout-duration: 10s
      order-management-service:
        timeout-duration: 30s
      notification-service:
        timeout-duration: 15s

  bulkhead:
    configs:
      default:
        max-concurrent-calls: 10
        max-wait-duration: 500ms
    instances:
      user-management-service:
        base-config: default
      customer-management-service:
        base-config: default
      product-catalog-service:
        base-config: default
      order-management-service:
        max-concurrent-calls: 5
      notification-service:
        max-concurrent-calls: 20

# Service Discovery Configuration
spring:
  cloud:
    consul:
      host: ${CONSUL_HOST:localhost}
      port: ${CONSUL_PORT:8500}
      discovery:
        enabled: true
        health-check-interval: 30s
        health-check-timeout: 10s
        instance-id: ${spring.application.name}:${server.port}
        prefer-ip-address: true
        tags:
          - version=${spring.application.version:1.0.0}
          - environment=${spring.profiles.active:development}

# Metrics Configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
    metrics:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
    tags:
      application: ${spring.application.name}
      environment: ${spring.profiles.active:development}

# Logging Configuration for Service Communication
logging:
  level:
    com.nttdata.ndvn.shared.infrastructure: DEBUG
    org.springframework.cloud.consul: INFO
    io.github.resilience4j: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{correlationId:-}] [%X{requestId:-}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{correlationId:-}] [%X{requestId:-}] %logger{36} - %msg%n"

package com.nttdata.ndvn.shared.infrastructure.automation;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.platform.suite.api.*;

/**
 * Integration Test Suite for automated execution of all integration tests.
 * 
 * This suite organizes and executes different categories of integration tests:
 * - End-to-end integration tests
 * - Contract tests
 * - Chaos engineering tests
 * - Performance tests
 * - Security tests
 * 
 * Test execution can be controlled using tags and profiles:
 * - @Tag("integration") - All integration tests
 * - @Tag("contract") - Contract tests only
 * - @Tag("chaos") - Chaos engineering tests only
 * - @Tag("e2e") - End-to-end tests only
 * - @Tag("performance") - Performance tests only
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-28
 */
@Suite
@SuiteDisplayName("NDVN SCS Integration Test Suite")
@SelectPackages({
    "com.nttdata.ndvn.shared.infrastructure.integration",
    "com.nttdata.ndvn.shared.infrastructure.contract",
    "com.nttdata.ndvn.shared.infrastructure.chaos",
    "com.nttdata.ndvn.shared.infrastructure.performance"
})
@IncludeTags({"integration"})
public class IntegrationTestSuite {
    // Test suite configuration
}

/**
 * Contract Test Suite - Executes all contract tests.
 */
@Suite
@SuiteDisplayName("Contract Test Suite")
@SelectPackages("com.nttdata.ndvn.shared.infrastructure.contract")
@IncludeTags({"contract"})
class ContractTestSuite {
}

/**
 * Chaos Engineering Test Suite - Executes all chaos engineering tests.
 */
@Suite
@SuiteDisplayName("Chaos Engineering Test Suite")
@SelectPackages("com.nttdata.ndvn.shared.infrastructure.chaos")
@IncludeTags({"chaos"})
class ChaosTestSuite {
}

/**
 * End-to-End Test Suite - Executes all end-to-end integration tests.
 */
@Suite
@SuiteDisplayName("End-to-End Test Suite")
@SelectPackages("com.nttdata.ndvn.shared.infrastructure.integration")
@IncludeTags({"e2e"})
class EndToEndTestSuite {
}

/**
 * Performance Test Suite - Executes all performance tests.
 */
@Suite
@SuiteDisplayName("Performance Test Suite")
@SelectPackages("com.nttdata.ndvn.shared.infrastructure.performance")
@IncludeTags({"performance"})
class PerformanceTestSuite {
}

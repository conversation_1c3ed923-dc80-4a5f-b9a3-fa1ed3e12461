package com.nttdata.ndvn.shared.infrastructure.chaos;

import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.core.WireMockConfiguration;
import com.nttdata.ndvn.shared.infrastructure.client.CustomerServiceClient;
import com.nttdata.ndvn.shared.infrastructure.client.ProductServiceClient;
import com.nttdata.ndvn.shared.infrastructure.client.UserServiceClient;
import com.nttdata.ndvn.shared.infrastructure.config.SharedInfrastructureAutoConfiguration;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.junit.jupiter.api.*;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.cloud.client.loadbalancer.LoadBalancerClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.time.Duration;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

import static com.github.tomakehurst.wiremock.client.WireMock.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;

/**
 * Chaos Engineering tests for the SCS ecosystem.
 * 
 * These tests simulate various failure scenarios to validate system resilience:
 * - Service unavailability
 * - Network latency and timeouts
 * - Partial service degradation
 * - Circuit breaker behavior
 * - Retry mechanism effectiveness
 * - Graceful degradation patterns
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-28
 */
@SpringJUnitConfig
@SpringBootTest(classes = ChaosEngineeringTest.TestConfiguration.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class ChaosEngineeringTest {
    
    private WireMockServer userServiceMock;
    private WireMockServer customerServiceMock;
    private WireMockServer productServiceMock;
    
    private UserServiceClient userServiceClient;
    private CustomerServiceClient customerServiceClient;
    private ProductServiceClient productServiceClient;
    
    @BeforeEach
    void setUp() {
        // Start WireMock servers with fault injection capabilities
        userServiceMock = new WireMockServer(WireMockConfiguration.options()
                .port(8091)
                .extensions("com.github.tomakehurst.wiremock.extension.responsetemplating.ResponseTemplateTransformer"));
        customerServiceMock = new WireMockServer(WireMockConfiguration.options()
                .port(8092));
        productServiceMock = new WireMockServer(WireMockConfiguration.options()
                .port(8093));
        
        userServiceMock.start();
        customerServiceMock.start();
        productServiceMock.start();
        
        // Initialize service clients (would be injected in real scenario)
        initializeServiceClients();
    }
    
    @AfterEach
    void tearDown() {
        if (userServiceMock != null) userServiceMock.stop();
        if (customerServiceMock != null) customerServiceMock.stop();
        if (productServiceMock != null) productServiceMock.stop();
    }
    
    @Test
    @Order(1)
    @DisplayName("Test system behavior when User Service is completely unavailable")
    void testUserServiceCompleteFailure() throws Exception {
        // Given - User service is down (no stubs configured)
        WireMock.configureFor("localhost", 8091);
        
        // When - Multiple concurrent requests are made
        ExecutorService executor = Executors.newFixedThreadPool(10);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failureCount = new AtomicInteger(0);
        
        CompletableFuture<Void>[] futures = new CompletableFuture[20];
        for (int i = 0; i < 20; i++) {
            futures[i] = CompletableFuture.runAsync(() -> {
                try {
                    CompletableFuture<Object> result = userServiceClient.getUser("test-user-" + Thread.currentThread().getId());
                    result.get(2, TimeUnit.SECONDS);
                    successCount.incrementAndGet();
                } catch (Exception e) {
                    failureCount.incrementAndGet();
                }
            }, executor);
        }
        
        CompletableFuture.allOf(futures).get(30, TimeUnit.SECONDS);
        
        // Then - All requests should fail gracefully
        assertEquals(0, successCount.get(), "No requests should succeed when service is down");
        assertEquals(20, failureCount.get(), "All requests should fail gracefully");
        
        executor.shutdown();
    }
    
    @Test
    @Order(2)
    @DisplayName("Test circuit breaker behavior under high failure rate")
    void testCircuitBreakerActivation() throws Exception {
        // Given - Service returns 500 errors consistently
        WireMock.configureFor("localhost", 8092);
        stubFor(get(urlMatching("/api/v1/customers/.*"))
                .willReturn(aResponse()
                        .withStatus(500)
                        .withBody("Internal Server Error")));
        
        // When - Make requests to trigger circuit breaker
        AtomicInteger circuitOpenCount = new AtomicInteger(0);
        
        for (int i = 0; i < 15; i++) {
            try {
                CompletableFuture<Object> result = customerServiceClient.getCustomer("cb-test-" + i);
                result.get(3, TimeUnit.SECONDS);
            } catch (Exception e) {
                // Check if this is a circuit breaker exception
                if (e.getMessage() != null && e.getMessage().contains("circuit")) {
                    circuitOpenCount.incrementAndGet();
                }
            }
            
            // Small delay between requests
            Thread.sleep(100);
        }
        
        // Then - Circuit breaker should eventually open
        assertTrue(circuitOpenCount.get() > 0, "Circuit breaker should open after consecutive failures");
    }
    
    @Test
    @Order(3)
    @DisplayName("Test system behavior with intermittent service failures")
    void testIntermittentServiceFailures() throws Exception {
        // Given - Service fails 50% of the time
        WireMock.configureFor("localhost", 8093);
        
        // Configure alternating success/failure responses
        for (int i = 0; i < 20; i++) {
            if (i % 2 == 0) {
                stubFor(get(urlEqualTo("/api/v1/products/product-" + i))
                        .willReturn(aResponse()
                                .withStatus(200)
                                .withHeader("Content-Type", "application/json")
                                .withBody("{\"id\":\"product-" + i + "\",\"name\":\"Test Product " + i + "\"}")));
            } else {
                stubFor(get(urlEqualTo("/api/v1/products/product-" + i))
                        .willReturn(aResponse()
                                .withStatus(500)
                                .withFixedDelay(1000)
                                .withBody("Service Temporarily Unavailable")));
            }
        }
        
        // When - Make concurrent requests
        ExecutorService executor = Executors.newFixedThreadPool(10);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger retrySuccessCount = new AtomicInteger(0);
        
        CompletableFuture<Void>[] futures = new CompletableFuture[20];
        for (int i = 0; i < 20; i++) {
            final int productId = i;
            futures[i] = CompletableFuture.runAsync(() -> {
                try {
                    CompletableFuture<Object> result = productServiceClient.getProduct("product-" + productId);
                    Object product = result.get(5, TimeUnit.SECONDS);
                    if (product != null) {
                        successCount.incrementAndGet();
                        if (productId % 2 == 1) {
                            // This was a retry success
                            retrySuccessCount.incrementAndGet();
                        }
                    }
                } catch (Exception e) {
                    // Request failed even with retries
                }
            }, executor);
        }
        
        CompletableFuture.allOf(futures).get(60, TimeUnit.SECONDS);
        
        // Then - Some requests should succeed, including retried ones
        assertTrue(successCount.get() >= 10, "At least half the requests should succeed");
        
        executor.shutdown();
    }
    
    @Test
    @Order(4)
    @DisplayName("Test system behavior under high latency conditions")
    void testHighLatencyResilience() throws Exception {
        // Given - Service responds with high latency
        WireMock.configureFor("localhost", 8091);
        stubFor(get(urlMatching("/api/v1/users/.*"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withHeader("Content-Type", "application/json")
                        .withBody("{\"id\":\"test-user\",\"username\":\"testuser\"}")
                        .withFixedDelay(3000))); // 3 second delay
        
        // When - Make requests with timeout expectations
        long startTime = System.currentTimeMillis();
        AtomicInteger timeoutCount = new AtomicInteger(0);
        AtomicInteger successCount = new AtomicInteger(0);
        
        ExecutorService executor = Executors.newFixedThreadPool(5);
        CompletableFuture<Void>[] futures = new CompletableFuture[10];
        
        for (int i = 0; i < 10; i++) {
            futures[i] = CompletableFuture.runAsync(() -> {
                try {
                    CompletableFuture<Object> result = userServiceClient.getUser("latency-test-user");
                    result.get(2, TimeUnit.SECONDS); // 2 second timeout
                    successCount.incrementAndGet();
                } catch (TimeoutException e) {
                    timeoutCount.incrementAndGet();
                } catch (Exception e) {
                    // Other exceptions
                }
            }, executor);
        }
        
        CompletableFuture.allOf(futures).get(30, TimeUnit.SECONDS);
        long endTime = System.currentTimeMillis();
        
        // Then - Requests should timeout appropriately
        assertTrue(timeoutCount.get() > 0, "Some requests should timeout due to high latency");
        assertTrue((endTime - startTime) < 25000, "Overall test should complete within reasonable time");
        
        executor.shutdown();
    }
    
    @Test
    @Order(5)
    @DisplayName("Test graceful degradation when multiple services are degraded")
    void testMultiServiceDegradation() throws Exception {
        // Given - Multiple services are partially degraded
        
        // User service: 30% failure rate
        WireMock.configureFor("localhost", 8091);
        stubFor(get(urlMatching("/api/v1/users/user-[0-2]"))
                .willReturn(aResponse().withStatus(500)));
        stubFor(get(urlMatching("/api/v1/users/user-[3-9]"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withHeader("Content-Type", "application/json")
                        .withBody("{\"id\":\"test-user\",\"username\":\"testuser\"}")));
        
        // Customer service: High latency
        WireMock.configureFor("localhost", 8092);
        stubFor(get(urlMatching("/api/v1/customers/.*"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withHeader("Content-Type", "application/json")
                        .withBody("{\"id\":\"test-customer\",\"name\":\"Test Customer\"}")
                        .withFixedDelay(2000)));
        
        // Product service: Normal operation
        WireMock.configureFor("localhost", 8093);
        stubFor(get(urlMatching("/api/v1/products/.*"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withHeader("Content-Type", "application/json")
                        .withBody("{\"id\":\"test-product\",\"name\":\"Test Product\"}")));
        
        // When - Simulate a complex workflow requiring all services
        AtomicInteger workflowSuccessCount = new AtomicInteger(0);
        AtomicInteger partialSuccessCount = new AtomicInteger(0);
        
        ExecutorService executor = Executors.newFixedThreadPool(5);
        CompletableFuture<Void>[] futures = new CompletableFuture[10];
        
        for (int i = 0; i < 10; i++) {
            final int workflowId = i;
            futures[i] = CompletableFuture.runAsync(() -> {
                int successfulCalls = 0;
                
                // Try to call all three services
                try {
                    userServiceClient.getUser("user-" + workflowId).get(3, TimeUnit.SECONDS);
                    successfulCalls++;
                } catch (Exception e) {
                    // User service failed
                }
                
                try {
                    customerServiceClient.getCustomer("customer-" + workflowId).get(3, TimeUnit.SECONDS);
                    successfulCalls++;
                } catch (Exception e) {
                    // Customer service failed
                }
                
                try {
                    productServiceClient.getProduct("product-" + workflowId).get(3, TimeUnit.SECONDS);
                    successfulCalls++;
                } catch (Exception e) {
                    // Product service failed
                }
                
                if (successfulCalls == 3) {
                    workflowSuccessCount.incrementAndGet();
                } else if (successfulCalls > 0) {
                    partialSuccessCount.incrementAndGet();
                }
            }, executor);
        }
        
        CompletableFuture.allOf(futures).get(45, TimeUnit.SECONDS);
        
        // Then - System should handle partial failures gracefully
        int totalProcessed = workflowSuccessCount.get() + partialSuccessCount.get();
        assertTrue(totalProcessed > 0, "System should handle some workflows even with degraded services");
        
        executor.shutdown();
    }
    
    private void initializeServiceClients() {
        // Initialize mock service clients
        // In a real scenario, these would be properly configured with the test infrastructure
        userServiceClient = mock(UserServiceClient.class);
        customerServiceClient = mock(CustomerServiceClient.class);
        productServiceClient = mock(ProductServiceClient.class);
    }
    
    @Configuration
    @Import(SharedInfrastructureAutoConfiguration.class)
    static class TestConfiguration {
        
        @Bean
        public MeterRegistry meterRegistry() {
            return new SimpleMeterRegistry();
        }
        
        @Bean
        public DiscoveryClient discoveryClient() {
            return mock(DiscoveryClient.class);
        }
        
        @Bean
        public LoadBalancerClient loadBalancerClient() {
            return mock(LoadBalancerClient.class);
        }
    }
}

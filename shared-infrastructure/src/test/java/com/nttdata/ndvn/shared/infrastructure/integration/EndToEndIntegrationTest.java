package com.nttdata.ndvn.shared.infrastructure.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.nttdata.ndvn.shared.infrastructure.config.SharedInfrastructureAutoConfiguration;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.http.*;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.containers.KafkaContainer;
import org.testcontainers.containers.Network;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.containers.wait.strategy.Wait;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.utility.DockerImageName;

import java.time.Duration;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * End-to-end integration tests for the complete SCS ecosystem.
 * 
 * This test suite validates the entire system workflow including:
 * - Multi-service interactions
 * - Database transactions across services
 * - Event-driven communication
 * - Service discovery and load balancing
 * - Authentication and authorization flows
 * - Data consistency across bounded contexts
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-28
 */
@SpringJUnitConfig
@SpringBootTest(
    classes = EndToEndIntegrationTest.TestConfiguration.class,
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT
)
@Testcontainers
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class EndToEndIntegrationTest {
    
    private static final Network network = Network.newNetwork();
    
    @Container
    static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:15-alpine")
            .withNetwork(network)
            .withNetworkAliases("postgres")
            .withDatabaseName("ndvn_test")
            .withUsername("test")
            .withPassword("test");
    
    @Container
    static KafkaContainer kafka = new KafkaContainer(DockerImageName.parse("confluentinc/cp-kafka:7.4.0"))
            .withNetwork(network)
            .withNetworkAliases("kafka");
    
    @Container
    static GenericContainer<?> redis = new GenericContainer<>("redis:7-alpine")
            .withNetwork(network)
            .withNetworkAliases("redis")
            .withExposedPorts(6379)
            .waitingFor(Wait.forListeningPort());
    
    @Container
    static GenericContainer<?> consul = new GenericContainer<>("consul:1.16")
            .withNetwork(network)
            .withNetworkAliases("consul")
            .withExposedPorts(8500)
            .withCommand("consul", "agent", "-server", "-bootstrap-expect=1", "-ui", "-client=0.0.0.0", "-bind=0.0.0.0")
            .waitingFor(Wait.forHttp("/v1/status/leader").forPort(8500));
    
    @LocalServerPort
    private int port;
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        // Database configuration
        registry.add("spring.datasource.url", postgres::getJdbcUrl);
        registry.add("spring.datasource.username", postgres::getUsername);
        registry.add("spring.datasource.password", postgres::getPassword);
        
        // Kafka configuration
        registry.add("spring.kafka.bootstrap-servers", kafka::getBootstrapServers);
        
        // Redis configuration
        registry.add("spring.data.redis.host", redis::getHost);
        registry.add("spring.data.redis.port", () -> redis.getMappedPort(6379));
        
        // Consul configuration
        registry.add("spring.cloud.consul.host", consul::getHost);
        registry.add("spring.cloud.consul.port", () -> consul.getMappedPort(8500));
        registry.add("spring.cloud.consul.discovery.enabled", () -> "true");
    }
    
    @BeforeAll
    static void setUp() {
        // Ensure all containers are started
        assertTrue(postgres.isRunning(), "PostgreSQL container should be running");
        assertTrue(kafka.isRunning(), "Kafka container should be running");
        assertTrue(redis.isRunning(), "Redis container should be running");
        assertTrue(consul.isRunning(), "Consul container should be running");
    }
    
    @Test
    @Order(1)
    @DisplayName("Test complete user registration and customer creation workflow")
    void testUserRegistrationAndCustomerCreationWorkflow() throws Exception {
        // Given - User registration data
        Map<String, Object> userRegistration = Map.of(
            "username", "testuser",
            "email", "<EMAIL>",
            "password", "SecurePassword123!",
            "firstName", "Test",
            "lastName", "User"
        );
        
        // When - Register user
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Map<String, Object>> userRequest = new HttpEntity<>(userRegistration, headers);
        
        ResponseEntity<Map> userResponse = restTemplate.postForEntity(
            "/api/v1/users/register", userRequest, Map.class);
        
        // Then - User should be created successfully
        assertEquals(HttpStatus.CREATED, userResponse.getStatusCode());
        assertNotNull(userResponse.getBody());
        String userId = (String) userResponse.getBody().get("id");
        assertNotNull(userId);
        
        // And - Customer should be automatically created via event
        Thread.sleep(2000); // Wait for async event processing
        
        ResponseEntity<Map> customerResponse = restTemplate.getForEntity(
            "/api/v1/customers/by-user/" + userId, Map.class);
        
        assertEquals(HttpStatus.OK, customerResponse.getStatusCode());
        assertNotNull(customerResponse.getBody());
        assertEquals("<EMAIL>", customerResponse.getBody().get("email"));
    }
    
    @Test
    @Order(2)
    @DisplayName("Test complete order processing workflow")
    void testOrderProcessingWorkflow() throws Exception {
        // Given - Create test data first
        String customerId = createTestCustomer();
        String productId = createTestProduct();
        
        // When - Create order
        Map<String, Object> orderData = Map.of(
            "customerId", customerId,
            "items", java.util.List.of(
                Map.of(
                    "productId", productId,
                    "quantity", 2,
                    "unitPrice", 99.99
                )
            ),
            "shippingAddress", Map.of(
                "street", "123 Test St",
                "city", "Test City",
                "state", "TS",
                "zipCode", "12345",
                "country", "US"
            )
        );
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Map<String, Object>> orderRequest = new HttpEntity<>(orderData, headers);
        
        ResponseEntity<Map> orderResponse = restTemplate.postForEntity(
            "/api/v1/orders", orderRequest, Map.class);
        
        // Then - Order should be created
        assertEquals(HttpStatus.CREATED, orderResponse.getStatusCode());
        String orderId = (String) orderResponse.getBody().get("id");
        assertNotNull(orderId);
        
        // And - Inventory should be updated
        Thread.sleep(1000); // Wait for async processing
        
        ResponseEntity<Map> inventoryResponse = restTemplate.getForEntity(
            "/api/v1/inventory/product/" + productId, Map.class);
        
        assertEquals(HttpStatus.OK, inventoryResponse.getStatusCode());
        // Verify inventory was decremented
    }
    
    @Test
    @Order(3)
    @DisplayName("Test cross-service data consistency")
    void testCrossServiceDataConsistency() throws Exception {
        // Given - Create entities across multiple services
        String userId = createTestUser();
        String customerId = createTestCustomer();
        String productId = createTestProduct();
        
        // When - Perform operations that affect multiple services
        CompletableFuture<Void> userUpdate = CompletableFuture.runAsync(() -> {
            try {
                updateUser(userId);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        
        CompletableFuture<Void> customerUpdate = CompletableFuture.runAsync(() -> {
            try {
                updateCustomer(customerId);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        
        CompletableFuture<Void> productUpdate = CompletableFuture.runAsync(() -> {
            try {
                updateProduct(productId);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        
        // Wait for all updates to complete
        CompletableFuture.allOf(userUpdate, customerUpdate, productUpdate)
                .get(10, TimeUnit.SECONDS);
        
        // Then - Verify data consistency across services
        Thread.sleep(2000); // Wait for eventual consistency
        
        // Verify all updates were applied correctly
        verifyUserUpdate(userId);
        verifyCustomerUpdate(customerId);
        verifyProductUpdate(productId);
    }
    
    @Test
    @Order(4)
    @DisplayName("Test service resilience and failure recovery")
    void testServiceResilienceAndFailureRecovery() throws Exception {
        // This test would simulate service failures and verify recovery
        // Implementation would depend on specific resilience patterns
        assertTrue(true, "Resilience test placeholder");
    }
    
    // Helper methods
    private String createTestUser() throws Exception {
        Map<String, Object> userData = Map.of(
            "username", "testuser" + System.currentTimeMillis(),
            "email", "test" + System.currentTimeMillis() + "@example.com",
            "password", "SecurePassword123!"
        );
        
        HttpEntity<Map<String, Object>> request = new HttpEntity<>(userData, createHeaders());
        ResponseEntity<Map> response = restTemplate.postForEntity("/api/v1/users", request, Map.class);
        
        assertEquals(HttpStatus.CREATED, response.getStatusCode());
        return (String) response.getBody().get("id");
    }
    
    private String createTestCustomer() throws Exception {
        Map<String, Object> customerData = Map.of(
            "firstName", "Test",
            "lastName", "Customer",
            "email", "customer" + System.currentTimeMillis() + "@example.com",
            "phone", "+1234567890"
        );
        
        HttpEntity<Map<String, Object>> request = new HttpEntity<>(customerData, createHeaders());
        ResponseEntity<Map> response = restTemplate.postForEntity("/api/v1/customers", request, Map.class);
        
        assertEquals(HttpStatus.CREATED, response.getStatusCode());
        return (String) response.getBody().get("id");
    }
    
    private String createTestProduct() throws Exception {
        Map<String, Object> productData = Map.of(
            "name", "Test Product " + System.currentTimeMillis(),
            "sku", "TEST-" + System.currentTimeMillis(),
            "basePrice", 99.99,
            "description", "Test product description"
        );
        
        HttpEntity<Map<String, Object>> request = new HttpEntity<>(productData, createHeaders());
        ResponseEntity<Map> response = restTemplate.postForEntity("/api/v1/products", request, Map.class);
        
        assertEquals(HttpStatus.CREATED, response.getStatusCode());
        return (String) response.getBody().get("id");
    }
    
    private void updateUser(String userId) throws Exception {
        Map<String, Object> updateData = Map.of("firstName", "Updated");
        HttpEntity<Map<String, Object>> request = new HttpEntity<>(updateData, createHeaders());
        restTemplate.put("/api/v1/users/" + userId, request);
    }
    
    private void updateCustomer(String customerId) throws Exception {
        Map<String, Object> updateData = Map.of("firstName", "Updated Customer");
        HttpEntity<Map<String, Object>> request = new HttpEntity<>(updateData, createHeaders());
        restTemplate.put("/api/v1/customers/" + customerId, request);
    }
    
    private void updateProduct(String productId) throws Exception {
        Map<String, Object> updateData = Map.of("name", "Updated Product");
        HttpEntity<Map<String, Object>> request = new HttpEntity<>(updateData, createHeaders());
        restTemplate.put("/api/v1/products/" + productId, request);
    }
    
    private void verifyUserUpdate(String userId) {
        ResponseEntity<Map> response = restTemplate.getForEntity("/api/v1/users/" + userId, Map.class);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals("Updated", response.getBody().get("firstName"));
    }
    
    private void verifyCustomerUpdate(String customerId) {
        ResponseEntity<Map> response = restTemplate.getForEntity("/api/v1/customers/" + customerId, Map.class);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals("Updated Customer", response.getBody().get("firstName"));
    }
    
    private void verifyProductUpdate(String productId) {
        ResponseEntity<Map> response = restTemplate.getForEntity("/api/v1/products/" + productId, Map.class);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals("Updated Product", response.getBody().get("name"));
    }
    
    private HttpHeaders createHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        return headers;
    }
    
    @Configuration
    @Import(SharedInfrastructureAutoConfiguration.class)
    static class TestConfiguration {
        // Test configuration beans if needed
    }
}

package com.nttdata.ndvn.shared.infrastructure.performance;

import com.nttdata.ndvn.shared.infrastructure.testutil.IntegrationTestBase;
import org.junit.jupiter.api.*;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Performance tests for SCS service-to-service communication.
 * 
 * These tests validate:
 * - Response time under normal load
 * - Throughput capabilities
 * - Behavior under high concurrency
 * - Resource utilization patterns
 * - Scalability characteristics
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-28
 */
@SpringJUnitConfig
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Tag("performance")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class ServicePerformanceTest extends IntegrationTestBase {
    
    private static final int WARMUP_REQUESTS = 50;
    private static final int PERFORMANCE_REQUESTS = 1000;
    private static final int CONCURRENT_USERS = 50;
    
    @Test
    @Order(1)
    @DisplayName("Test single service response time baseline")
    void testSingleServiceResponseTime() throws Exception {
        // Given - Warmup phase
        for (int i = 0; i < WARMUP_REQUESTS; i++) {
            restTemplate.getForEntity("/api/v1/users/warmup-" + i, Map.class);
        }
        
        // When - Measure response times
        List<Long> responseTimes = new ArrayList<>();
        
        for (int i = 0; i < 100; i++) {
            Instant start = Instant.now();
            ResponseEntity<Map> response = restTemplate.getForEntity("/api/v1/users/perf-test-" + i, Map.class);
            Instant end = Instant.now();
            
            if (response.getStatusCode().is2xxSuccessful()) {
                responseTimes.add(Duration.between(start, end).toMillis());
            }
        }
        
        // Then - Analyze performance metrics
        double averageResponseTime = responseTimes.stream()
                .mapToLong(Long::longValue)
                .average()
                .orElse(0.0);
        
        long maxResponseTime = responseTimes.stream()
                .mapToLong(Long::longValue)
                .max()
                .orElse(0L);
        
        long p95ResponseTime = responseTimes.stream()
                .sorted()
                .skip((long) (responseTimes.size() * 0.95))
                .findFirst()
                .orElse(0L);
        
        // Performance assertions
        assertTrue(averageResponseTime < 100, "Average response time should be under 100ms, was: " + averageResponseTime);
        assertTrue(maxResponseTime < 500, "Max response time should be under 500ms, was: " + maxResponseTime);
        assertTrue(p95ResponseTime < 200, "95th percentile should be under 200ms, was: " + p95ResponseTime);
        
        System.out.printf("Performance Metrics - Avg: %.2fms, Max: %dms, P95: %dms%n", 
                averageResponseTime, maxResponseTime, p95ResponseTime);
    }
    
    @Test
    @Order(2)
    @DisplayName("Test concurrent request handling")
    void testConcurrentRequestHandling() throws Exception {
        // Given - Concurrent execution setup
        ExecutorService executor = Executors.newFixedThreadPool(CONCURRENT_USERS);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger errorCount = new AtomicInteger(0);
        AtomicLong totalResponseTime = new AtomicLong(0);
        
        // When - Execute concurrent requests
        Instant testStart = Instant.now();
        CompletableFuture<Void>[] futures = new CompletableFuture[PERFORMANCE_REQUESTS];
        
        for (int i = 0; i < PERFORMANCE_REQUESTS; i++) {
            final int requestId = i;
            futures[i] = CompletableFuture.runAsync(() -> {
                try {
                    Instant start = Instant.now();
                    ResponseEntity<Map> response = restTemplate.getForEntity(
                            "/api/v1/users/concurrent-test-" + requestId, Map.class);
                    Instant end = Instant.now();
                    
                    if (response.getStatusCode().is2xxSuccessful()) {
                        successCount.incrementAndGet();
                        totalResponseTime.addAndGet(Duration.between(start, end).toMillis());
                    } else {
                        errorCount.incrementAndGet();
                    }
                } catch (Exception e) {
                    errorCount.incrementAndGet();
                }
            }, executor);
        }
        
        CompletableFuture.allOf(futures).get(60, TimeUnit.SECONDS);
        Instant testEnd = Instant.now();
        
        // Then - Analyze concurrent performance
        long totalTestTime = Duration.between(testStart, testEnd).toMillis();
        double throughput = (double) successCount.get() / (totalTestTime / 1000.0);
        double averageResponseTime = (double) totalResponseTime.get() / successCount.get();
        double errorRate = (double) errorCount.get() / PERFORMANCE_REQUESTS * 100;
        
        // Performance assertions
        assertTrue(errorRate < 1.0, "Error rate should be under 1%, was: " + errorRate + "%");
        assertTrue(throughput > 50, "Throughput should be over 50 req/sec, was: " + throughput);
        assertTrue(averageResponseTime < 200, "Average response time should be under 200ms under load, was: " + averageResponseTime);
        
        System.out.printf("Concurrent Performance - Throughput: %.2f req/sec, Avg Response: %.2fms, Error Rate: %.2f%%%n",
                throughput, averageResponseTime, errorRate);
        
        executor.shutdown();
    }
    
    @Test
    @Order(3)
    @DisplayName("Test service-to-service communication performance")
    void testServiceToServicePerformance() throws Exception {
        // Given - Multi-service workflow setup
        Map<String, Object> testUser = createTestUser();
        Map<String, Object> testCustomer = createTestCustomer();
        Map<String, Object> testProduct = createTestProduct();
        
        AtomicInteger workflowSuccessCount = new AtomicInteger(0);
        AtomicLong totalWorkflowTime = new AtomicLong(0);
        
        // When - Execute multi-service workflows
        ExecutorService executor = Executors.newFixedThreadPool(20);
        CompletableFuture<Void>[] futures = new CompletableFuture[200];
        
        for (int i = 0; i < 200; i++) {
            final int workflowId = i;
            futures[i] = CompletableFuture.runAsync(() -> {
                try {
                    Instant start = Instant.now();
                    
                    // Simulate a complex workflow involving multiple services
                    ResponseEntity<Map> userResponse = restTemplate.getForEntity(
                            "/api/v1/users/" + testUser.get("id"), Map.class);
                    
                    if (userResponse.getStatusCode().is2xxSuccessful()) {
                        ResponseEntity<Map> customerResponse = restTemplate.getForEntity(
                                "/api/v1/customers/" + testCustomer.get("id"), Map.class);
                        
                        if (customerResponse.getStatusCode().is2xxSuccessful()) {
                            ResponseEntity<Map> productResponse = restTemplate.getForEntity(
                                    "/api/v1/products/" + testProduct.get("id"), Map.class);
                            
                            if (productResponse.getStatusCode().is2xxSuccessful()) {
                                Instant end = Instant.now();
                                workflowSuccessCount.incrementAndGet();
                                totalWorkflowTime.addAndGet(Duration.between(start, end).toMillis());
                            }
                        }
                    }
                } catch (Exception e) {
                    // Workflow failed
                }
            }, executor);
        }
        
        CompletableFuture.allOf(futures).get(120, TimeUnit.SECONDS);
        
        // Then - Analyze workflow performance
        double averageWorkflowTime = (double) totalWorkflowTime.get() / workflowSuccessCount.get();
        double workflowSuccessRate = (double) workflowSuccessCount.get() / 200 * 100;
        
        // Performance assertions
        assertTrue(workflowSuccessRate > 95, "Workflow success rate should be over 95%, was: " + workflowSuccessRate + "%");
        assertTrue(averageWorkflowTime < 500, "Average workflow time should be under 500ms, was: " + averageWorkflowTime);
        
        System.out.printf("Workflow Performance - Success Rate: %.2f%%, Avg Time: %.2fms%n",
                workflowSuccessRate, averageWorkflowTime);
        
        executor.shutdown();
    }
    
    @Test
    @Order(4)
    @DisplayName("Test system performance under sustained load")
    void testSustainedLoadPerformance() throws Exception {
        // Given - Sustained load test setup
        AtomicInteger totalRequests = new AtomicInteger(0);
        AtomicInteger successfulRequests = new AtomicInteger(0);
        AtomicLong totalResponseTime = new AtomicLong(0);
        
        // When - Run sustained load for 30 seconds
        ExecutorService executor = Executors.newFixedThreadPool(10);
        Instant testStart = Instant.now();
        Instant testEnd = testStart.plusSeconds(30);
        
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        
        while (Instant.now().isBefore(testEnd)) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    Instant start = Instant.now();
                    ResponseEntity<Map> response = restTemplate.getForEntity(
                            "/api/v1/users/sustained-" + totalRequests.incrementAndGet(), Map.class);
                    Instant end = Instant.now();
                    
                    if (response.getStatusCode().is2xxSuccessful()) {
                        successfulRequests.incrementAndGet();
                        totalResponseTime.addAndGet(Duration.between(start, end).toMillis());
                    }
                } catch (Exception e) {
                    // Request failed
                }
            }, executor);
            
            futures.add(future);
            Thread.sleep(10); // Control request rate
        }
        
        // Wait for all requests to complete
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get(60, TimeUnit.SECONDS);
        
        // Then - Analyze sustained load performance
        long actualTestDuration = Duration.between(testStart, Instant.now()).toSeconds();
        double averageThroughput = (double) successfulRequests.get() / actualTestDuration;
        double averageResponseTime = (double) totalResponseTime.get() / successfulRequests.get();
        double successRate = (double) successfulRequests.get() / totalRequests.get() * 100;
        
        // Performance assertions
        assertTrue(successRate > 98, "Success rate under sustained load should be over 98%, was: " + successRate + "%");
        assertTrue(averageThroughput > 30, "Average throughput should be over 30 req/sec, was: " + averageThroughput);
        assertTrue(averageResponseTime < 300, "Average response time under sustained load should be under 300ms, was: " + averageResponseTime);
        
        System.out.printf("Sustained Load Performance - Throughput: %.2f req/sec, Avg Response: %.2fms, Success Rate: %.2f%%%n",
                averageThroughput, averageResponseTime, successRate);
        
        executor.shutdown();
    }
    
    @Test
    @Order(5)
    @DisplayName("Test memory and resource utilization")
    void testResourceUtilization() throws Exception {
        // Given - Memory monitoring setup
        Runtime runtime = Runtime.getRuntime();
        long initialMemory = runtime.totalMemory() - runtime.freeMemory();
        
        // When - Execute memory-intensive operations
        List<Map<String, Object>> testData = new ArrayList<>();
        
        for (int i = 0; i < 1000; i++) {
            ResponseEntity<Map> response = restTemplate.getForEntity("/api/v1/users/memory-test-" + i, Map.class);
            if (response.getStatusCode().is2xxSuccessful()) {
                testData.add(response.getBody());
            }
        }
        
        // Force garbage collection
        System.gc();
        Thread.sleep(1000);
        
        // Then - Check memory usage
        long finalMemory = runtime.totalMemory() - runtime.freeMemory();
        long memoryIncrease = finalMemory - initialMemory;
        double memoryIncreasePercent = (double) memoryIncrease / initialMemory * 100;
        
        // Resource utilization assertions
        assertTrue(memoryIncreasePercent < 50, "Memory increase should be under 50%, was: " + memoryIncreasePercent + "%");
        assertTrue(testData.size() > 950, "Should successfully process most requests, processed: " + testData.size());
        
        System.out.printf("Resource Utilization - Memory increase: %.2f%%, Processed requests: %d%n",
                memoryIncreasePercent, testData.size());
    }
}

package com.nttdata.ndvn.shared.infrastructure.saga;

import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Unit tests for SagaOrchestrator.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-28
 */
@ExtendWith(MockitoExtension.class)
class SagaOrchestratorTest {
    
    @Mock
    private SagaStateRepository sagaStateRepository;
    
    private SagaOrchestrator sagaOrchestrator;
    private SimpleMeterRegistry meterRegistry;
    
    @BeforeEach
    void setUp() {
        meterRegistry = new SimpleMeterRegistry();
        sagaOrchestrator = new SagaOrchestrator(sagaStateRepository, meterRegistry);
    }
    
    @Test
    void testSuccessfulSagaExecution() throws Exception {
        // Given
        SagaDefinition sagaDefinition = createTestSagaDefinition();
        SagaContext sagaContext = createTestSagaContext();
        
        when(sagaStateRepository.save(any(SagaState.class))).thenAnswer(invocation -> invocation.getArgument(0));
        
        // When
        CompletableFuture<SagaExecutionResult> future = sagaOrchestrator.executeSaga(sagaDefinition, sagaContext);
        SagaExecutionResult result = future.get(5, TimeUnit.SECONDS);
        
        // Then
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getSagaId());
        
        // Verify saga state was saved
        verify(sagaStateRepository, atLeast(2)).save(any(SagaState.class));
        
        // Verify metrics
        assertEquals(1.0, meterRegistry.counter("saga.started").count());
        assertEquals(1.0, meterRegistry.counter("saga.completed").count());
        assertEquals(0.0, meterRegistry.counter("saga.failed").count());
    }
    
    @Test
    void testSagaExecutionWithStepFailure() throws Exception {
        // Given
        SagaDefinition sagaDefinition = createFailingSagaDefinition();
        SagaContext sagaContext = createTestSagaContext();
        
        when(sagaStateRepository.save(any(SagaState.class))).thenAnswer(invocation -> invocation.getArgument(0));
        
        // When
        CompletableFuture<SagaExecutionResult> future = sagaOrchestrator.executeSaga(sagaDefinition, sagaContext);
        SagaExecutionResult result = future.get(5, TimeUnit.SECONDS);
        
        // Then
        assertNotNull(result);
        assertTrue(result.isFailure());
        assertNotNull(result.getErrorMessage());
        
        // Verify compensation was triggered
        verify(sagaStateRepository, atLeast(2)).save(any(SagaState.class));
        
        // Verify metrics
        assertEquals(1.0, meterRegistry.counter("saga.started").count());
        assertEquals(0.0, meterRegistry.counter("saga.completed").count());
        assertEquals(1.0, meterRegistry.counter("saga.failed").count());
        assertEquals(1.0, meterRegistry.counter("saga.compensated").count());
    }
    
    @Test
    void testSagaRecovery() throws Exception {
        // Given
        UUID sagaId = UUID.randomUUID();
        SagaState existingSagaState = createTestSagaState(sagaId);
        
        when(sagaStateRepository.findById(sagaId)).thenReturn(Optional.of(existingSagaState));
        
        // When
        CompletableFuture<SagaExecutionResult> future = sagaOrchestrator.recoverSaga(sagaId);
        SagaExecutionResult result = future.get(5, TimeUnit.SECONDS);
        
        // Then
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(sagaId, result.getSagaId());
    }
    
    @Test
    void testSagaRecoveryNotFound() throws Exception {
        // Given
        UUID sagaId = UUID.randomUUID();
        
        when(sagaStateRepository.findById(sagaId)).thenReturn(Optional.empty());
        
        // When
        CompletableFuture<SagaExecutionResult> future = sagaOrchestrator.recoverSaga(sagaId);
        SagaExecutionResult result = future.get(5, TimeUnit.SECONDS);
        
        // Then
        assertNotNull(result);
        assertTrue(result.isFailure());
        assertTrue(result.getErrorMessage().contains("Saga not found"));
    }
    
    @Test
    void testGetSagaState() {
        // Given
        UUID sagaId = UUID.randomUUID();
        SagaState expectedState = createTestSagaState(sagaId);
        
        when(sagaStateRepository.findById(sagaId)).thenReturn(Optional.of(expectedState));
        
        // When
        SagaState actualState = sagaOrchestrator.getSagaState(sagaId);
        
        // Then
        assertNotNull(actualState);
        assertEquals(expectedState.getSagaId(), actualState.getSagaId());
        assertEquals(expectedState.getSagaType(), actualState.getSagaType());
        assertEquals(expectedState.getStatus(), actualState.getStatus());
    }
    
    @Test
    void testGetSagaStateNotFound() {
        // Given
        UUID sagaId = UUID.randomUUID();
        
        when(sagaStateRepository.findById(sagaId)).thenReturn(Optional.empty());
        
        // When & Then
        assertThrows(SagaNotFoundException.class, () -> sagaOrchestrator.getSagaState(sagaId));
    }
    
    /**
     * Create a test saga definition with successful steps.
     */
    private SagaDefinition createTestSagaDefinition() {
        SagaStep step1 = new TestSagaStep("STEP_1", 1, true);
        SagaStep step2 = new TestSagaStep("STEP_2", 2, true);
        
        return new SagaDefinition("TEST_SAGA", 30)
                .addStep(step1)
                .addStep(step2);
    }
    
    /**
     * Create a test saga definition with a failing step.
     */
    private SagaDefinition createFailingSagaDefinition() {
        SagaStep step1 = new TestSagaStep("STEP_1", 1, true);
        SagaStep step2 = new TestSagaStep("STEP_2", 2, false); // This step will fail
        
        return new SagaDefinition("FAILING_SAGA", 30)
                .addStep(step1)
                .addStep(step2);
    }
    
    /**
     * Create a test saga context.
     */
    private SagaContext createTestSagaContext() {
        Map<String, Object> data = new HashMap<>();
        data.put("orderId", UUID.randomUUID());
        data.put("customerId", UUID.randomUUID());
        
        return new SagaContext(data);
    }
    
    /**
     * Create a test saga state.
     */
    private SagaState createTestSagaState(UUID sagaId) {
        SagaState sagaState = new SagaState();
        sagaState.setSagaId(sagaId);
        sagaState.setSagaType("TEST_SAGA");
        sagaState.setStatus(SagaStatus.COMPLETED);
        sagaState.setCurrentStep(2);
        sagaState.setTotalSteps(2);
        sagaState.setStartedAt(LocalDateTime.now().minusMinutes(5));
        sagaState.setCompletedAt(LocalDateTime.now());
        
        return sagaState;
    }
    
    /**
     * Test implementation of SagaStep.
     */
    private static class TestSagaStep extends SagaStep {
        private final boolean shouldSucceed;
        
        public TestSagaStep(String stepName, int order, boolean shouldSucceed) {
            super(stepName, order);
            this.shouldSucceed = shouldSucceed;
        }
        
        @Override
        public SagaStepResult execute(SagaContext context) {
            if (shouldSucceed) {
                Map<String, Object> data = new HashMap<>();
                data.put("executedAt", LocalDateTime.now());
                return SagaStepResult.success(data);
            } else {
                return SagaStepResult.failure("Test step failure");
            }
        }
        
        @Override
        public SagaStepResult compensate(SagaContext context) {
            // Always succeed compensation for test
            return SagaStepResult.success();
        }
    }
}

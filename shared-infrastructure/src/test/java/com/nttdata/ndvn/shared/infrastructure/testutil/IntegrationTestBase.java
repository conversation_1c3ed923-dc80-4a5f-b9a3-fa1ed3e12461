package com.nttdata.ndvn.shared.infrastructure.testutil;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.WireMock;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.containers.KafkaContainer;
import org.testcontainers.containers.Network;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.containers.wait.strategy.Wait;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.utility.DockerImageName;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Base class for integration tests providing common infrastructure and utilities.
 * 
 * This class provides:
 * - TestContainers setup for PostgreSQL, Kafka, Redis, and Consul
 * - WireMock servers for service mocking
 * - Common test utilities and helper methods
 * - Shared test configuration
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-28
 */
@Testcontainers
public abstract class IntegrationTestBase {
    
    protected static final Network network = Network.newNetwork();
    
    @Container
    protected static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:15-alpine")
            .withNetwork(network)
            .withNetworkAliases("postgres")
            .withDatabaseName("ndvn_test")
            .withUsername("test")
            .withPassword("test");
    
    @Container
    protected static KafkaContainer kafka = new KafkaContainer(DockerImageName.parse("confluentinc/cp-kafka:7.4.0"))
            .withNetwork(network)
            .withNetworkAliases("kafka");
    
    @Container
    protected static GenericContainer<?> redis = new GenericContainer<>("redis:7-alpine")
            .withNetwork(network)
            .withNetworkAliases("redis")
            .withExposedPorts(6379)
            .waitingFor(Wait.forListeningPort());
    
    @Container
    protected static GenericContainer<?> consul = new GenericContainer<>("consul:1.16")
            .withNetwork(network)
            .withNetworkAliases("consul")
            .withExposedPorts(8500)
            .withCommand("consul", "agent", "-server", "-bootstrap-expect=1", "-ui", "-client=0.0.0.0", "-bind=0.0.0.0")
            .waitingFor(Wait.forHttp("/v1/status/leader").forPort(8500));
    
    protected WireMockServer userServiceMock;
    protected WireMockServer customerServiceMock;
    protected WireMockServer productServiceMock;
    protected WireMockServer orderServiceMock;
    protected WireMockServer notificationServiceMock;
    
    @Autowired
    protected TestRestTemplate restTemplate;
    
    @Autowired
    protected ObjectMapper objectMapper;
    
    protected Map<String, Object> testData = new ConcurrentHashMap<>();
    
    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        // Database configuration
        registry.add("spring.datasource.url", postgres::getJdbcUrl);
        registry.add("spring.datasource.username", postgres::getUsername);
        registry.add("spring.datasource.password", postgres::getPassword);
        registry.add("spring.jpa.hibernate.ddl-auto", () -> "create-drop");
        
        // Kafka configuration
        registry.add("spring.kafka.bootstrap-servers", kafka::getBootstrapServers);
        registry.add("spring.kafka.consumer.auto-offset-reset", () -> "earliest");
        registry.add("spring.kafka.consumer.group-id", () -> "test-group");
        
        // Redis configuration
        registry.add("spring.data.redis.host", redis::getHost);
        registry.add("spring.data.redis.port", () -> redis.getMappedPort(6379));
        
        // Consul configuration
        registry.add("spring.cloud.consul.host", consul::getHost);
        registry.add("spring.cloud.consul.port", () -> consul.getMappedPort(8500));
        registry.add("spring.cloud.consul.discovery.enabled", () -> "true");
        registry.add("spring.cloud.consul.discovery.register", () -> "false");
        
        // Service URLs for testing
        registry.add("services.user-service.url", () -> "http://localhost:8091");
        registry.add("services.customer-service.url", () -> "http://localhost:8092");
        registry.add("services.product-service.url", () -> "http://localhost:8093");
        registry.add("services.order-service.url", () -> "http://localhost:8094");
        registry.add("services.notification-service.url", () -> "http://localhost:8095");
    }
    
    @BeforeEach
    void setUpMockServices() {
        // Start WireMock servers for each service
        userServiceMock = new WireMockServer(8091);
        customerServiceMock = new WireMockServer(8092);
        productServiceMock = new WireMockServer(8093);
        orderServiceMock = new WireMockServer(8094);
        notificationServiceMock = new WireMockServer(8095);
        
        userServiceMock.start();
        customerServiceMock.start();
        productServiceMock.start();
        orderServiceMock.start();
        notificationServiceMock.start();
        
        // Configure default successful responses
        configureDefaultMockResponses();
    }
    
    @AfterEach
    void tearDownMockServices() {
        if (userServiceMock != null) userServiceMock.stop();
        if (customerServiceMock != null) customerServiceMock.stop();
        if (productServiceMock != null) productServiceMock.stop();
        if (orderServiceMock != null) orderServiceMock.stop();
        if (notificationServiceMock != null) notificationServiceMock.stop();
        
        // Clear test data
        testData.clear();
    }
    
    protected void configureDefaultMockResponses() {
        // User Service default responses
        WireMock.configureFor("localhost", 8091);
        WireMock.stubFor(WireMock.get(WireMock.urlMatching("/api/v1/users/.*"))
                .willReturn(WireMock.aResponse()
                        .withStatus(200)
                        .withHeader("Content-Type", "application/json")
                        .withBody("{\"id\":\"test-user\",\"username\":\"testuser\",\"email\":\"<EMAIL>\"}")));
        
        // Customer Service default responses
        WireMock.configureFor("localhost", 8092);
        WireMock.stubFor(WireMock.get(WireMock.urlMatching("/api/v1/customers/.*"))
                .willReturn(WireMock.aResponse()
                        .withStatus(200)
                        .withHeader("Content-Type", "application/json")
                        .withBody("{\"id\":\"test-customer\",\"name\":\"Test Customer\",\"email\":\"<EMAIL>\"}")));
        
        // Product Service default responses
        WireMock.configureFor("localhost", 8093);
        WireMock.stubFor(WireMock.get(WireMock.urlMatching("/api/v1/products/.*"))
                .willReturn(WireMock.aResponse()
                        .withStatus(200)
                        .withHeader("Content-Type", "application/json")
                        .withBody("{\"id\":\"test-product\",\"name\":\"Test Product\",\"price\":99.99}")));
        
        // Order Service default responses
        WireMock.configureFor("localhost", 8094);
        WireMock.stubFor(WireMock.get(WireMock.urlMatching("/api/v1/orders/.*"))
                .willReturn(WireMock.aResponse()
                        .withStatus(200)
                        .withHeader("Content-Type", "application/json")
                        .withBody("{\"id\":\"test-order\",\"status\":\"PENDING\",\"total\":199.98}")));
        
        // Notification Service default responses
        WireMock.configureFor("localhost", 8095);
        WireMock.stubFor(WireMock.post(WireMock.urlMatching("/api/v1/notifications/.*"))
                .willReturn(WireMock.aResponse()
                        .withStatus(202)
                        .withHeader("Content-Type", "application/json")
                        .withBody("{\"id\":\"test-notification\",\"status\":\"QUEUED\"}")));
    }
    
    // Utility methods for test data creation
    protected HttpHeaders createJsonHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        return headers;
    }
    
    protected HttpHeaders createAuthHeaders(String token) {
        HttpHeaders headers = createJsonHeaders();
        headers.setBearerAuth(token);
        return headers;
    }
    
    protected <T> HttpEntity<T> createJsonRequest(T body) {
        return new HttpEntity<>(body, createJsonHeaders());
    }
    
    protected <T> HttpEntity<T> createAuthRequest(T body, String token) {
        return new HttpEntity<>(body, createAuthHeaders(token));
    }
    
    protected String generateTestId() {
        return "test-" + System.currentTimeMillis() + "-" + Thread.currentThread().getId();
    }
    
    protected Map<String, Object> createTestUser() {
        String id = generateTestId();
        Map<String, Object> user = Map.of(
            "id", id,
            "username", "user-" + id,
            "email", "user-" + id + "@example.com",
            "firstName", "Test",
            "lastName", "User"
        );
        testData.put("user-" + id, user);
        return user;
    }
    
    protected Map<String, Object> createTestCustomer() {
        String id = generateTestId();
        Map<String, Object> customer = Map.of(
            "id", id,
            "customerNumber", "CUST-" + id,
            "firstName", "Test",
            "lastName", "Customer",
            "email", "customer-" + id + "@example.com",
            "phone", "+1234567890"
        );
        testData.put("customer-" + id, customer);
        return customer;
    }
    
    protected Map<String, Object> createTestProduct() {
        String id = generateTestId();
        Map<String, Object> product = Map.of(
            "id", id,
            "sku", "PROD-" + id,
            "name", "Test Product " + id,
            "description", "Test product description",
            "basePrice", 99.99
        );
        testData.put("product-" + id, product);
        return product;
    }
    
    protected Map<String, Object> createTestOrder(String customerId, String productId) {
        String id = generateTestId();
        Map<String, Object> order = Map.of(
            "id", id,
            "customerId", customerId,
            "items", java.util.List.of(
                Map.of(
                    "productId", productId,
                    "quantity", 2,
                    "unitPrice", 99.99
                )
            ),
            "status", "PENDING",
            "total", 199.98
        );
        testData.put("order-" + id, order);
        return order;
    }
    
    protected void waitForAsyncProcessing() throws InterruptedException {
        Thread.sleep(1000); // Wait for async event processing
    }
    
    protected void waitForAsyncProcessing(long milliseconds) throws InterruptedException {
        Thread.sleep(milliseconds);
    }
}

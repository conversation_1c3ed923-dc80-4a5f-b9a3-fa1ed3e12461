package com.nttdata.ndvn.user.application.mapper;

import com.nttdata.ndvn.user.application.dto.CreateUserRequest;
import com.nttdata.ndvn.user.application.dto.PermissionDto;
import com.nttdata.ndvn.user.application.dto.RoleDto;
import com.nttdata.ndvn.user.application.dto.UserDto;
import com.nttdata.ndvn.user.domain.model.Permission;
import com.nttdata.ndvn.user.domain.model.Role;
import com.nttdata.ndvn.user.domain.model.User;
import org.mapstruct.*;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * MapStruct mapper for User-related entities and DTOs.
 * 
 * This mapper provides automatic mapping between domain entities and DTOs
 * while maintaining clean separation between layers.
 */
@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface UserMapper {
    
    // User mappings
    
    /**
     * Maps User entity to UserDto.
     */
    @Mapping(target = "roleNames", source = "roles", qualifiedByName = "rolesToRoleNames")
    @Mapping(target = "permissions", source = "roles", qualifiedByName = "rolesToPermissionNames")
    UserDto toDto(User user);
    
    /**
     * Maps list of User entities to list of UserDtos.
     */
    List<UserDto> toDto(List<User> users);
    
    /**
     * Maps UserDto to User entity.
     */
    @Mapping(target = "roles", ignore = true)
    @Mapping(target = "sessions", ignore = true)
    @Mapping(target = "passwordHash", ignore = true)
    User toEntity(UserDto userDto);
    
    /**
     * Maps CreateUserRequest to User entity.
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "passwordHash", source = "password")
    @Mapping(target = "roles", ignore = true)
    @Mapping(target = "sessions", ignore = true)
    @Mapping(target = "emailVerified", ignore = true)
    @Mapping(target = "accountNonExpired", ignore = true)
    @Mapping(target = "accountNonLocked", ignore = true)
    @Mapping(target = "credentialsNonExpired", ignore = true)
    @Mapping(target = "failedLoginAttempts", ignore = true)
    @Mapping(target = "lastLoginAt", ignore = true)
    @Mapping(target = "passwordChangedAt", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    User toEntity(CreateUserRequest request);
    
    /**
     * Updates User entity from UserDto.
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "roles", ignore = true)
    @Mapping(target = "sessions", ignore = true)
    @Mapping(target = "passwordHash", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    void updateEntity(@MappingTarget User user, UserDto userDto);
    
    // Role mappings
    
    /**
     * Maps Role entity to RoleDto.
     */
    @Mapping(target = "permissionNames", source = "permissions", qualifiedByName = "permissionsToPermissionNames")
    @Mapping(target = "userCount", source = "users", qualifiedByName = "usersToUserCount")
    RoleDto toDto(Role role);
    
    /**
     * Maps list of Role entities to list of RoleDtos.
     */
    List<RoleDto> rolesToDto(List<Role> roles);

    /**
     * Maps RoleDto to Role entity.
     */
    @Mapping(target = "permissions", ignore = true)
    @Mapping(target = "users", ignore = true)
    Role toEntity(RoleDto roleDto);

    // Permission mappings

    /**
     * Maps Permission entity to PermissionDto.
     */
    PermissionDto toDto(Permission permission);

    /**
     * Maps list of Permission entities to list of PermissionDtos.
     */
    List<PermissionDto> permissionsToDto(List<Permission> permissions);
    
    /**
     * Maps PermissionDto to Permission entity.
     */
    @Mapping(target = "roles", ignore = true)
    Permission toEntity(PermissionDto permissionDto);
    
    // Named mapping methods
    
    /**
     * Extracts role names from roles.
     */
    @Named("rolesToRoleNames")
    default Set<String> rolesToRoleNames(Set<Role> roles) {
        if (roles == null) {
            return null;
        }
        return roles.stream()
                .map(Role::getName)
                .collect(Collectors.toSet());
    }
    
    /**
     * Extracts permission names from roles.
     */
    @Named("rolesToPermissionNames")
    default Set<String> rolesToPermissionNames(Set<Role> roles) {
        if (roles == null) {
            return null;
        }
        return roles.stream()
                .flatMap(role -> role.getPermissions().stream())
                .map(Permission::getName)
                .collect(Collectors.toSet());
    }
    
    /**
     * Extracts permission names from permissions.
     */
    @Named("permissionsToPermissionNames")
    default Set<String> permissionsToPermissionNames(Set<Permission> permissions) {
        if (permissions == null) {
            return null;
        }
        return permissions.stream()
                .map(Permission::getName)
                .collect(Collectors.toSet());
    }
    
    /**
     * Counts users in a role.
     */
    @Named("usersToUserCount")
    default long usersToUserCount(Set<User> users) {
        return users != null ? users.size() : 0;
    }
}

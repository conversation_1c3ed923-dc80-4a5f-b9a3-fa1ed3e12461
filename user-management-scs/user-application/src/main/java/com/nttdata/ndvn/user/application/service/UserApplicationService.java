package com.nttdata.ndvn.user.application.service;

import com.nttdata.ndvn.user.application.dto.CreateUserRequest;
import com.nttdata.ndvn.user.application.dto.UserDto;
import com.nttdata.ndvn.user.application.mapper.UserMapper;
import com.nttdata.ndvn.user.domain.model.User;
import com.nttdata.ndvn.user.domain.repository.UserRepository;
import com.nttdata.ndvn.user.domain.service.UserDomainService;
import com.nttdata.ndvn.user.events.publisher.StandardizedUserEventPublisher;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;
import java.util.Set;
import java.util.UUID;

import static com.nttdata.ndvn.user.infrastructure.config.CacheConfig.*;

/**
 * Application service for User management operations.
 * 
 * This service orchestrates user-related use cases and coordinates between
 * domain services, repositories, and external systems while maintaining
 * transactional boundaries and caching strategies.
 */
@Service
@Transactional(readOnly = true)
public class UserApplicationService {
    
    private static final Logger logger = LoggerFactory.getLogger(UserApplicationService.class);
    
    private final UserRepository userRepository;
    private final UserDomainService userDomainService;
    private final UserMapper userMapper;
    private final StandardizedUserEventPublisher eventPublisher;

    public UserApplicationService(UserRepository userRepository,
                                UserDomainService userDomainService,
                                UserMapper userMapper,
                                StandardizedUserEventPublisher eventPublisher) {
        this.userRepository = userRepository;
        this.userDomainService = userDomainService;
        this.userMapper = userMapper;
        this.eventPublisher = eventPublisher;
    }
    
    /**
     * Creates a new user.
     *
     * @param request the user creation request
     * @return the created user DTO
     */
    @Transactional
    @CacheEvict(value = {USER_CACHE, USER_BY_USERNAME_CACHE, USER_BY_EMAIL_CACHE}, allEntries = true)
    public UserDto createUser(CreateUserRequest request) {
        logger.info("Creating new user with username: {}", request.getUsername());

        User user;
        if (request.getRoleNames() != null && !request.getRoleNames().isEmpty()) {
            user = userDomainService.createUserWithRoles(
                request.getUsername(),
                request.getEmail(),
                request.getPassword(),
                request.getRoleNames()
            );
        } else {
            user = userDomainService.createUser(
                request.getUsername(),
                request.getEmail(),
                request.getPassword()
            );
        }

        if (!request.isEnabled()) {
            user.disable();
            userRepository.save(user);
        }

        // Publish user created event
        eventPublisher.publishUserCreated(user)
            .whenComplete((result, ex) -> {
                if (ex != null) {
                    logger.error("Failed to publish UserCreatedEvent for user: {}", user.getId(), ex);
                } else {
                    logger.debug("Successfully published UserCreatedEvent for user: {}", user.getId());
                }
            });

        logger.info("Successfully created user with ID: {}", user.getId());
        return userMapper.toDto(user);
    }
    
    /**
     * Finds a user by ID.
     * 
     * @param id the user ID
     * @return the user DTO if found
     */
    @Cacheable(value = USER_CACHE, key = "#id")
    public Optional<UserDto> findById(UUID id) {
        logger.debug("Finding user by ID: {}", id);
        return userRepository.findById(id)
                .map(userMapper::toDto);
    }
    
    /**
     * Finds a user by username.
     * 
     * @param username the username
     * @return the user DTO if found
     */
    @Cacheable(value = USER_BY_USERNAME_CACHE, key = "#username")
    public Optional<UserDto> findByUsername(String username) {
        logger.debug("Finding user by username: {}", username);
        return userRepository.findByUsername(username)
                .map(userMapper::toDto);
    }
    
    /**
     * Finds a user by email.
     * 
     * @param email the email address
     * @return the user DTO if found
     */
    @Cacheable(value = USER_BY_EMAIL_CACHE, key = "#email")
    public Optional<UserDto> findByEmail(String email) {
        logger.debug("Finding user by email: {}", email);
        return userRepository.findByEmail(email)
                .map(userMapper::toDto);
    }
    
    /**
     * Finds all users with pagination.
     * 
     * @param pageable pagination information
     * @return page of user DTOs
     */
    public Page<UserDto> findAll(Pageable pageable) {
        logger.debug("Finding all users with pagination: {}", pageable);
        return userRepository.findAll(pageable)
                .map(userMapper::toDto);
    }
    
    /**
     * Searches users by criteria.
     * 
     * @param searchText search text for username or email
     * @param pageable pagination information
     * @return page of matching user DTOs
     */
    public Page<UserDto> searchUsers(String searchText, Pageable pageable) {
        logger.debug("Searching users with text: {}", searchText);
        return userRepository.findByUsernameContainingIgnoreCaseOrEmailContainingIgnoreCase(
                searchText, searchText, pageable)
                .map(userMapper::toDto);
    }
    
    /**
     * Finds users by enabled status.
     * 
     * @param enabled the enabled status
     * @param pageable pagination information
     * @return page of user DTOs
     */
    public Page<UserDto> findByEnabled(boolean enabled, Pageable pageable) {
        logger.debug("Finding users by enabled status: {}", enabled);
        return userRepository.findByEnabled(enabled, pageable)
                .map(userMapper::toDto);
    }
    
    /**
     * Finds users by role name.
     * 
     * @param roleName the role name
     * @param pageable pagination information
     * @return page of user DTOs
     */
    public Page<UserDto> findByRole(String roleName, Pageable pageable) {
        logger.debug("Finding users by role: {}", roleName);
        return userRepository.findByRolesName(roleName, pageable)
                .map(userMapper::toDto);
    }
    
    /**
     * Updates a user.
     *
     * @param id the user ID
     * @param userDto the updated user data
     * @return the updated user DTO
     */
    @Transactional
    @CacheEvict(value = {USER_CACHE, USER_BY_USERNAME_CACHE, USER_BY_EMAIL_CACHE}, allEntries = true)
    public UserDto updateUser(UUID id, UserDto userDto) {
        logger.info("Updating user with ID: {}", id);

        User user = userRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("User not found: " + id));

        // Update basic fields
        userMapper.updateEntity(user, userDto);

        User savedUser = userRepository.save(user);

        // Publish user updated event
        eventPublisher.publishUserUpdated(savedUser)
            .whenComplete((result, ex) -> {
                if (ex != null) {
                    logger.error("Failed to publish UserUpdatedEvent for user: {}", savedUser.getId(), ex);
                } else {
                    logger.debug("Successfully published UserUpdatedEvent for user: {}", savedUser.getId());
                }
            });

        logger.info("Successfully updated user with ID: {}", id);
        return userMapper.toDto(savedUser);
    }
    
    /**
     * Changes a user's password.
     *
     * @param id the user ID
     * @param currentPassword the current password
     * @param newPassword the new password
     */
    @Transactional
    @CacheEvict(value = {USER_CACHE, USER_BY_USERNAME_CACHE, USER_BY_EMAIL_CACHE}, key = "#id")
    public void changePassword(UUID id, String currentPassword, String newPassword) {
        logger.info("Changing password for user ID: {}", id);

        User user = userRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("User not found: " + id));

        userDomainService.changePassword(user, currentPassword, newPassword);

        // Publish password changed event
        eventPublisher.publishUserPasswordChanged(user)
            .whenComplete((result, ex) -> {
                if (ex != null) {
                    logger.error("Failed to publish UserPasswordChangedEvent for user: {}", user.getId(), ex);
                } else {
                    logger.debug("Successfully published UserPasswordChangedEvent for user: {}", user.getId());
                }
            });

        logger.info("Successfully changed password for user ID: {}", id);
    }
    
    /**
     * Resets a user's password (admin operation).
     *
     * @param id the user ID
     * @param newPassword the new password
     */
    @Transactional
    @CacheEvict(value = {USER_CACHE, USER_BY_USERNAME_CACHE, USER_BY_EMAIL_CACHE}, key = "#id")
    public void resetPassword(UUID id, String newPassword) {
        logger.info("Resetting password for user ID: {}", id);

        User user = userRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("User not found: " + id));

        userDomainService.resetPassword(user, newPassword);

        // Publish password changed event
        eventPublisher.publishUserPasswordChanged(user)
            .whenComplete((result, ex) -> {
                if (ex != null) {
                    logger.error("Failed to publish UserPasswordChangedEvent for user: {}", user.getId(), ex);
                } else {
                    logger.debug("Successfully published UserPasswordChangedEvent for user: {}", user.getId());
                }
            });

        logger.info("Successfully reset password for user ID: {}", id);
    }
    
    /**
     * Assigns roles to a user.
     * 
     * @param id the user ID
     * @param roleNames the role names to assign
     */
    @Transactional
    @CacheEvict(value = {USER_CACHE, USER_BY_USERNAME_CACHE, USER_BY_EMAIL_CACHE, USER_ROLES_CACHE}, allEntries = true)
    public void assignRoles(UUID id, Set<String> roleNames) {
        logger.info("Assigning roles {} to user ID: {}", roleNames, id);
        
        User user = userRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("User not found: " + id));
        
        for (String roleName : roleNames) {
            userDomainService.assignRole(user, roleName);
        }
        
        logger.info("Successfully assigned roles to user ID: {}", id);
    }
    
    /**
     * Removes roles from a user.
     * 
     * @param id the user ID
     * @param roleNames the role names to remove
     */
    @Transactional
    @CacheEvict(value = {USER_CACHE, USER_BY_USERNAME_CACHE, USER_BY_EMAIL_CACHE, USER_ROLES_CACHE}, allEntries = true)
    public void removeRoles(UUID id, Set<String> roleNames) {
        logger.info("Removing roles {} from user ID: {}", roleNames, id);
        
        User user = userRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("User not found: " + id));
        
        for (String roleName : roleNames) {
            userDomainService.removeRole(user, roleName);
        }
        
        logger.info("Successfully removed roles from user ID: {}", id);
    }
    
    /**
     * Enables a user account.
     *
     * @param id the user ID
     */
    @Transactional
    @CacheEvict(value = {USER_CACHE, USER_BY_USERNAME_CACHE, USER_BY_EMAIL_CACHE}, key = "#id")
    public void enableUser(UUID id) {
        logger.info("Enabling user ID: {}", id);

        User user = userRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("User not found: " + id));

        userDomainService.unlockUser(user);

        // Publish user enabled event
        eventPublisher.publishUserEnabled(user)
            .whenComplete((result, ex) -> {
                if (ex != null) {
                    logger.error("Failed to publish UserEnabledEvent for user: {}", user.getId(), ex);
                } else {
                    logger.debug("Successfully published UserEnabledEvent for user: {}", user.getId());
                }
            });

        logger.info("Successfully enabled user ID: {}", id);
    }
    
    /**
     * Disables a user account.
     *
     * @param id the user ID
     */
    @Transactional
    @CacheEvict(value = {USER_CACHE, USER_BY_USERNAME_CACHE, USER_BY_EMAIL_CACHE}, key = "#id")
    public void disableUser(UUID id) {
        logger.info("Disabling user ID: {}", id);

        User user = userRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("User not found: " + id));

        userDomainService.lockUser(user);

        // Publish user disabled event
        eventPublisher.publishUserDisabled(user)
            .whenComplete((result, ex) -> {
                if (ex != null) {
                    logger.error("Failed to publish UserDisabledEvent for user: {}", user.getId(), ex);
                } else {
                    logger.debug("Successfully published UserDisabledEvent for user: {}", user.getId());
                }
            });

        logger.info("Successfully disabled user ID: {}", id);
    }
    
    /**
     * Verifies a user's email address.
     *
     * @param id the user ID
     */
    @Transactional
    @CacheEvict(value = {USER_CACHE, USER_BY_USERNAME_CACHE, USER_BY_EMAIL_CACHE}, key = "#id")
    public void verifyEmail(UUID id) {
        logger.info("Verifying email for user ID: {}", id);

        User user = userRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("User not found: " + id));

        userDomainService.verifyEmail(user);

        // Publish email verified event
        eventPublisher.publishUserEmailVerified(user)
            .whenComplete((result, ex) -> {
                if (ex != null) {
                    logger.error("Failed to publish UserEmailVerifiedEvent for user: {}", user.getId(), ex);
                } else {
                    logger.debug("Successfully published UserEmailVerifiedEvent for user: {}", user.getId());
                }
            });

        logger.info("Successfully verified email for user ID: {}", id);
    }
    
    /**
     * Deletes a user.
     * 
     * @param id the user ID
     */
    @Transactional
    @CacheEvict(value = {USER_CACHE, USER_BY_USERNAME_CACHE, USER_BY_EMAIL_CACHE}, allEntries = true)
    public void deleteUser(UUID id) {
        logger.info("Deleting user ID: {}", id);
        
        if (!userRepository.existsById(id)) {
            throw new IllegalArgumentException("User not found: " + id);
        }
        
        userRepository.deleteById(id);
        logger.info("Successfully deleted user ID: {}", id);
    }
    
    /**
     * Checks if a username is available.
     * 
     * @param username the username to check
     * @return true if available, false otherwise
     */
    public boolean isUsernameAvailable(String username) {
        return userDomainService.isUsernameAvailable(username);
    }
    
    /**
     * Checks if an email is available.
     * 
     * @param email the email to check
     * @return true if available, false otherwise
     */
    public boolean isEmailAvailable(String email) {
        return userDomainService.isEmailAvailable(email);
    }
}

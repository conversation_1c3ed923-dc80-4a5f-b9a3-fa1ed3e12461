package com.nttdata.ndvn.user.web.controller;

import com.nttdata.ndvn.user.application.dto.CreateUserRequest;
import com.nttdata.ndvn.user.application.dto.UserDto;
import com.nttdata.ndvn.user.application.service.UserApplicationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Set;
import java.util.UUID;

/**
 * REST controller for User management operations.
 * 
 * This controller provides HTTP endpoints for user CRUD operations,
 * user search, and user management functions within the User Management SCS.
 */
@RestController
@RequestMapping("/api/v1/users")
@Tag(name = "User Management", description = "User management operations")
public class UserController {
    
    private static final Logger logger = LoggerFactory.getLogger(UserController.class);
    
    private final UserApplicationService userApplicationService;
    
    public UserController(UserApplicationService userApplicationService) {
        this.userApplicationService = userApplicationService;
    }
    
    @Operation(summary = "Create a new user", description = "Creates a new user account with the provided details")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "User created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid input data"),
        @ApiResponse(responseCode = "409", description = "Username or email already exists")
    })
    @PostMapping
    @PreAuthorize("hasAuthority('USER_WRITE')")
    public ResponseEntity<UserDto> createUser(@Valid @RequestBody CreateUserRequest request) {
        logger.info("Creating new user with username: {}", request.getUsername());
        UserDto createdUser = userApplicationService.createUser(request);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdUser);
    }
    
    @Operation(summary = "Get user by ID", description = "Retrieves a user by their unique identifier")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User found"),
        @ApiResponse(responseCode = "404", description = "User not found")
    })
    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('USER_READ')")
    public ResponseEntity<UserDto> getUserById(
            @Parameter(description = "User ID") @PathVariable UUID id) {
        logger.debug("Getting user by ID: {}", id);
        return userApplicationService.findById(id)
                .map(user -> ResponseEntity.ok(user))
                .orElse(ResponseEntity.notFound().build());
    }
    
    @Operation(summary = "Get user by username", description = "Retrieves a user by their username")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User found"),
        @ApiResponse(responseCode = "404", description = "User not found")
    })
    @GetMapping("/username/{username}")
    @PreAuthorize("hasAuthority('USER_READ')")
    public ResponseEntity<UserDto> getUserByUsername(
            @Parameter(description = "Username") @PathVariable String username) {
        logger.debug("Getting user by username: {}", username);
        return userApplicationService.findByUsername(username)
                .map(user -> ResponseEntity.ok(user))
                .orElse(ResponseEntity.notFound().build());
    }
    
    @Operation(summary = "Get user by email", description = "Retrieves a user by their email address")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User found"),
        @ApiResponse(responseCode = "404", description = "User not found")
    })
    @GetMapping("/email/{email}")
    @PreAuthorize("hasAuthority('USER_READ')")
    public ResponseEntity<UserDto> getUserByEmail(
            @Parameter(description = "Email address") @PathVariable String email) {
        logger.debug("Getting user by email: {}", email);
        return userApplicationService.findByEmail(email)
                .map(user -> ResponseEntity.ok(user))
                .orElse(ResponseEntity.notFound().build());
    }
    
    @Operation(summary = "Get all users", description = "Retrieves all users with pagination")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Users retrieved successfully")
    })
    @GetMapping
    @PreAuthorize("hasAuthority('USER_READ')")
    public ResponseEntity<Page<UserDto>> getAllUsers(
            @PageableDefault(size = 20) Pageable pageable) {
        logger.debug("Getting all users with pagination: {}", pageable);
        Page<UserDto> users = userApplicationService.findAll(pageable);
        return ResponseEntity.ok(users);
    }
    
    @Operation(summary = "Search users", description = "Searches users by username or email")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Search completed successfully")
    })
    @GetMapping("/search")
    @PreAuthorize("hasAuthority('USER_READ')")
    public ResponseEntity<Page<UserDto>> searchUsers(
            @Parameter(description = "Search text") @RequestParam String q,
            @PageableDefault(size = 20) Pageable pageable) {
        logger.debug("Searching users with query: {}", q);
        Page<UserDto> users = userApplicationService.searchUsers(q, pageable);
        return ResponseEntity.ok(users);
    }
    
    @Operation(summary = "Get users by enabled status", description = "Retrieves users filtered by enabled status")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Users retrieved successfully")
    })
    @GetMapping("/enabled/{enabled}")
    @PreAuthorize("hasAuthority('USER_READ')")
    public ResponseEntity<Page<UserDto>> getUsersByEnabled(
            @Parameter(description = "Enabled status") @PathVariable boolean enabled,
            @PageableDefault(size = 20) Pageable pageable) {
        logger.debug("Getting users by enabled status: {}", enabled);
        Page<UserDto> users = userApplicationService.findByEnabled(enabled, pageable);
        return ResponseEntity.ok(users);
    }
    
    @Operation(summary = "Get users by role", description = "Retrieves users with a specific role")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Users retrieved successfully")
    })
    @GetMapping("/role/{roleName}")
    @PreAuthorize("hasAuthority('USER_READ')")
    public ResponseEntity<Page<UserDto>> getUsersByRole(
            @Parameter(description = "Role name") @PathVariable String roleName,
            @PageableDefault(size = 20) Pageable pageable) {
        logger.debug("Getting users by role: {}", roleName);
        Page<UserDto> users = userApplicationService.findByRole(roleName, pageable);
        return ResponseEntity.ok(users);
    }
    
    @Operation(summary = "Update user", description = "Updates an existing user")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User updated successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid input data"),
        @ApiResponse(responseCode = "404", description = "User not found")
    })
    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority('USER_WRITE')")
    public ResponseEntity<UserDto> updateUser(
            @Parameter(description = "User ID") @PathVariable UUID id,
            @Valid @RequestBody UserDto userDto) {
        logger.info("Updating user with ID: {}", id);
        UserDto updatedUser = userApplicationService.updateUser(id, userDto);
        return ResponseEntity.ok(updatedUser);
    }
    
    @Operation(summary = "Change user password", description = "Changes a user's password")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Password changed successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid current password"),
        @ApiResponse(responseCode = "404", description = "User not found")
    })
    @PostMapping("/{id}/change-password")
    @PreAuthorize("hasAuthority('USER_WRITE') or #id == authentication.principal.id")
    public ResponseEntity<Void> changePassword(
            @Parameter(description = "User ID") @PathVariable UUID id,
            @RequestBody ChangePasswordRequest request) {
        logger.info("Changing password for user ID: {}", id);
        userApplicationService.changePassword(id, request.getCurrentPassword(), request.getNewPassword());
        return ResponseEntity.noContent().build();
    }
    
    @Operation(summary = "Reset user password", description = "Resets a user's password (admin operation)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Password reset successfully"),
        @ApiResponse(responseCode = "404", description = "User not found")
    })
    @PostMapping("/{id}/reset-password")
    @PreAuthorize("hasAuthority('USER_WRITE')")
    public ResponseEntity<Void> resetPassword(
            @Parameter(description = "User ID") @PathVariable UUID id,
            @RequestBody ResetPasswordRequest request) {
        logger.info("Resetting password for user ID: {}", id);
        userApplicationService.resetPassword(id, request.getNewPassword());
        return ResponseEntity.noContent().build();
    }
    
    @Operation(summary = "Assign roles to user", description = "Assigns roles to a user")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Roles assigned successfully"),
        @ApiResponse(responseCode = "404", description = "User not found")
    })
    @PostMapping("/{id}/roles")
    @PreAuthorize("hasAuthority('USER_WRITE')")
    public ResponseEntity<Void> assignRoles(
            @Parameter(description = "User ID") @PathVariable UUID id,
            @RequestBody Set<String> roleNames) {
        logger.info("Assigning roles {} to user ID: {}", roleNames, id);
        userApplicationService.assignRoles(id, roleNames);
        return ResponseEntity.noContent().build();
    }
    
    @Operation(summary = "Remove roles from user", description = "Removes roles from a user")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Roles removed successfully"),
        @ApiResponse(responseCode = "404", description = "User not found")
    })
    @DeleteMapping("/{id}/roles")
    @PreAuthorize("hasAuthority('USER_WRITE')")
    public ResponseEntity<Void> removeRoles(
            @Parameter(description = "User ID") @PathVariable UUID id,
            @RequestBody Set<String> roleNames) {
        logger.info("Removing roles {} from user ID: {}", roleNames, id);
        userApplicationService.removeRoles(id, roleNames);
        return ResponseEntity.noContent().build();
    }
    
    @Operation(summary = "Enable user", description = "Enables a user account")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "User enabled successfully"),
        @ApiResponse(responseCode = "404", description = "User not found")
    })
    @PostMapping("/{id}/enable")
    @PreAuthorize("hasAuthority('USER_WRITE')")
    public ResponseEntity<Void> enableUser(
            @Parameter(description = "User ID") @PathVariable UUID id) {
        logger.info("Enabling user ID: {}", id);
        userApplicationService.enableUser(id);
        return ResponseEntity.noContent().build();
    }
    
    @Operation(summary = "Disable user", description = "Disables a user account")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "User disabled successfully"),
        @ApiResponse(responseCode = "404", description = "User not found")
    })
    @PostMapping("/{id}/disable")
    @PreAuthorize("hasAuthority('USER_WRITE')")
    public ResponseEntity<Void> disableUser(
            @Parameter(description = "User ID") @PathVariable UUID id) {
        logger.info("Disabling user ID: {}", id);
        userApplicationService.disableUser(id);
        return ResponseEntity.noContent().build();
    }
    
    @Operation(summary = "Verify user email", description = "Verifies a user's email address")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Email verified successfully"),
        @ApiResponse(responseCode = "404", description = "User not found")
    })
    @PostMapping("/{id}/verify-email")
    @PreAuthorize("hasAuthority('USER_WRITE')")
    public ResponseEntity<Void> verifyEmail(
            @Parameter(description = "User ID") @PathVariable UUID id) {
        logger.info("Verifying email for user ID: {}", id);
        userApplicationService.verifyEmail(id);
        return ResponseEntity.noContent().build();
    }
    
    @Operation(summary = "Delete user", description = "Deletes a user account")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "User deleted successfully"),
        @ApiResponse(responseCode = "404", description = "User not found")
    })
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('USER_DELETE')")
    public ResponseEntity<Void> deleteUser(
            @Parameter(description = "User ID") @PathVariable UUID id) {
        logger.info("Deleting user ID: {}", id);
        userApplicationService.deleteUser(id);
        return ResponseEntity.noContent().build();
    }
    
    @Operation(summary = "Check username availability", description = "Checks if a username is available")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Availability check completed")
    })
    @GetMapping("/check-username/{username}")
    public ResponseEntity<Boolean> checkUsernameAvailability(
            @Parameter(description = "Username to check") @PathVariable String username) {
        logger.debug("Checking username availability: {}", username);
        boolean available = userApplicationService.isUsernameAvailable(username);
        return ResponseEntity.ok(available);
    }
    
    @Operation(summary = "Check email availability", description = "Checks if an email is available")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Availability check completed")
    })
    @GetMapping("/check-email/{email}")
    public ResponseEntity<Boolean> checkEmailAvailability(
            @Parameter(description = "Email to check") @PathVariable String email) {
        logger.debug("Checking email availability: {}", email);
        boolean available = userApplicationService.isEmailAvailable(email);
        return ResponseEntity.ok(available);
    }
    
    // Inner classes for request DTOs
    
    public static class ChangePasswordRequest {
        private String currentPassword;
        private String newPassword;
        
        public String getCurrentPassword() {
            return currentPassword;
        }
        
        public void setCurrentPassword(String currentPassword) {
            this.currentPassword = currentPassword;
        }
        
        public String getNewPassword() {
            return newPassword;
        }
        
        public void setNewPassword(String newPassword) {
            this.newPassword = newPassword;
        }
    }
    
    public static class ResetPasswordRequest {
        private String newPassword;
        
        public String getNewPassword() {
            return newPassword;
        }
        
        public void setNewPassword(String newPassword) {
            this.newPassword = newPassword;
        }
    }
}
